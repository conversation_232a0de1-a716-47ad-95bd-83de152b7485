---
// Компонент корзины для отображения в блоке с хлебными крошками
// Показывается только на страницах /products

interface Props {
  showCart?: boolean;
}

const { showCart = false } = Astro.props;
---

{showCart && (
  <div class="shopping-cart-container">
    <!-- Иконка корзины -->
    <button
      id="cart-button"
      class="cart-button"
      aria-label="Открыть корзину"
      title="Корзина"
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class="cart-icon"
      >
        <path
          d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>
    </button>

    <!-- Счетчик товаров -->
    <div id="cart-counter" class="cart-counter">
      <span id="cart-count">0</span>
    </div>

    <!-- Overlay для закрытия корзины -->
    <div id="cart-overlay" class="cart-overlay"></div>

    <!-- Боковая панель корзины -->
    <div id="cart-dropdown" class="cart-dropdown">
      <div class="cart-header">
        <h3>Корзина</h3>
        <button id="close-cart" class="close-cart-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <div id="cart-items" class="cart-items">
        <div class="empty-cart">
          <p>Добавляйте разные товары, чтобы оформить одним заказом.</p>
        </div>
      </div>

      <div class="cart-footer">
        <div id="cart-total" class="cart-total">
          <div class="total-label">Общая сумма:</div>
          <div class="total-amount">0.00 BYN</div>
        </div>
        <div class="cart-actions">
          <button id="view-cart-btn" class="view-cart-btn">
            Перейти в корзину
          </button>
          <button id="clear-cart-btn" class="clear-cart-btn">
            ОЧИСТИТЬ
          </button>
        </div>
      </div>
    </div>
  </div>
)}

<style>
  .shopping-cart-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-counter {
    background: #374151;
    color: white;
    border: none;
    border-radius: 50%;
    width: 2em;
    height: 2em;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    position: absolute;
    top: -8px;
    right: -8px;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0;
  }

  .cart-counter.visible {
    opacity: 1;
    transform: scale(1);
  }

  .cart-button {
    background: #baa385;
    color: white;
    border: none;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: box-shadow 0.3s ease;
    box-shadow: 0 2px 8px rgba(186, 163, 133, 0.3), 0 0 6px 2px rgba(52, 49, 45, 0.10);
  }

  .cart-button:hover {
    box-shadow: 0 0 0 4px rgba(186, 163, 133, 0.2);
  }

  .cart-icon {
    width: 24px;
    height: 24px;
  }

  .cart-dropdown {
    position: fixed;
    top: 0;
    right: 16px;
    background: white;
    border: 1px solid #e5e7eb;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    width: 25vw;
    min-width: 320px;
    max-width: 400px;
    height: calc(100vh - 32px);
    margin-top: 16px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateX(100%);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
  }

  .cart-dropdown.open {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
  }

  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
    background: #fafafa;
    flex-shrink: 0;
  }

  .cart-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #1f2937;
  }

  .close-cart-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
  }

  .close-cart-btn:hover {
    color: #374151;
  }

  .cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: white;
  }

  /* Повышенная специфичность для пустой корзины */
  .cart-items .empty-cart {
    padding: 0 20px;
    color: #6b7280;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
    text-align: center;
  }
  .cart-items .empty-cart p {
    font-size: 17px !important;
    font-weight: 500 !important;
    margin: 0 !important;
    line-height: 1.6 !important;
    max-width: 340px;
  }
  .cart-items .empty-cart p:first-child {
    margin-bottom: 16px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #374151 !important;
  }

  .cart-item {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    background: white;
    transition: background-color 0.2s ease;
  }

  .cart-item:hover {
    background: #fafafa;
  }

  .cart-item:last-child {
    border-bottom: none;
  }

  .cart-item-top-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 8px !important;
  }

  .cart-item-bottom-row {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }

  .cart-item-name {
    font-size: 15px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.3;
    flex: 1;
    margin-right: 12px;
  }

  .cart-item-total {
    font-size: 16px;
    font-weight: 700;
    color: #baa385;
  }

  /* Стили для селектора количества в корзине */
  .cart-quantity-btn:hover {
    background: #e5e7eb !important;
  }

  .cart-quantity-btn:active {
    background: #c8b499 !important;
    color: white !important;
    transform: scale(0.95) !important;
  }

  /* Скрываем стрелки в input number */
  .cart-quantity-input::-webkit-outer-spin-button,
  .cart-quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  /* Стили для кнопки удаления */
  .cart-remove-btn:hover {
    background: #e53e3e !important;
    color: white !important;
  }

  .quantity-display {
    min-width: 32px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    color: #1f2937;
    background: white;
    border-radius: 2px;
  }

  .cart-footer {
    padding: 20px;
    border-top: 1px solid #e5e7eb;
    background: #fafafa;
    flex-shrink: 0;
  }

  .cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px 16px;
    background: white;
    border: 1px solid #e5e7eb;
  }

  .total-label {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
  }

  .total-amount {
    font-size: 18px;
    font-weight: 700;
    color: #baa385;
  }

  .cart-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .view-cart-btn {
    background: #baa385;
    color: white;
    border: none;
    padding: 14px 20px;
    font-weight: 600;
    font-size: 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .view-cart-btn:hover {
    background: #a89274;
  }

  .clear-cart-btn {
    background: white;
    color: #6b7280;
    border: 1px solid #e5e7eb;
    padding: 12px 20px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .clear-cart-btn:hover {
    background: #f9fafb;
    color: #374151;
    border-color: #d1d5db;
  }

  .clear-cart-btn:disabled,
  .view-cart-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* Overlay для закрытия корзины */
  .cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .cart-overlay.open {
    opacity: 1;
    visibility: visible;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .cart-dropdown {
      width: 100vw;
      max-width: none;
      min-width: auto;
    }
  }

  @media (max-width: 480px) {
    .cart-header {
      padding: 16px;
    }

    .cart-footer {
      padding: 16px;
    }

    .cart-item {
      padding: 16px;
    }
  }

  /* Стили для кнопок в состоянии "добавлено" */
  .cart-added-state {
    background: #10b981 !important;
    border-color: #10b981 !important;
    color: white !important;
  }

  .cart-added-state:hover {
    background: #059669 !important;
    border-color: #059669 !important;
  }
</style>

<script>
  // Корзина покупок
  class ShoppingCartManager {
    constructor() {
      this.cart = this.loadCart();
      this.init();
    }

    init() {
      this.updateCartDisplay();
      this.bindEvents();
    }

    bindEvents() {
      const cartButton = document.getElementById('cart-button');
      const closeCartBtn = document.getElementById('close-cart');
      const cartDropdown = document.getElementById('cart-dropdown');
      const cartOverlay = document.getElementById('cart-overlay');
      const viewCartBtn = document.getElementById('view-cart-btn');
      const clearCartBtn = document.getElementById('clear-cart-btn');

      if (cartButton) {
        cartButton.addEventListener('click', () => this.toggleCart());
      }

      if (closeCartBtn) {
        closeCartBtn.addEventListener('click', () => this.closeCart());
      }

      if (cartOverlay) {
        cartOverlay.addEventListener('click', () => this.closeCart());
      }

      if (viewCartBtn) {
        viewCartBtn.addEventListener('click', () => this.viewCart());
      }

      if (clearCartBtn) {
        clearCartBtn.addEventListener('click', () => this.clearCart());
      }

      // Закрытие корзины при нажатии Escape
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && cartDropdown?.classList.contains('open')) {
          this.closeCart();
        }
      });
    }

    loadCart() {
      try {
        const saved = localStorage.getItem('shopping-cart');
        return saved ? JSON.parse(saved) : [];
      } catch {
        return [];
      }
    }

    saveCart() {
      localStorage.setItem('shopping-cart', JSON.stringify(this.cart));
    }

    addToCart(product) {
      // Создаем уникальный ID для товара
      let itemId = product.id;
      if (product.variant && product.variant.id) {
        itemId = `${product.id}-${product.variant.id}`;
      }

      const existingItem = this.cart.find(item =>
        item.id === itemId
      );

      if (existingItem) {
        existingItem.quantity += product.quantity || 1;
      } else {
        this.cart.push({
          id: itemId,
          name: product.name,
          price: product.price,
          image: product.image,
          variant: product.variant || null,
          unit: product.unit || (product.variant?.unit) || (product.basePrice?.unit) || 'piece',
          currency: product.currency || (product.variant?.currency) || (product.basePrice?.currency) || 'BYN',
          quantity: product.quantity || 1,
          productUrl: product.category && product.slug ? `/products/${product.categorySlug || product.category.toLowerCase().replace(/\s+/g, '-')}/${product.slug}` : null
        });
      }

      this.saveCart();
      this.updateCartDisplay();
      this.updateAddToCartButtons();
      this.showAddedNotification(product.name);
    }

    removeFromCart(index) {
      this.cart.splice(index, 1);
      this.saveCart();
      this.updateCartDisplay();
      this.updateAddToCartButtons();
    }

    updateQuantity(index, quantity) {
      if (quantity <= 0) {
        this.removeFromCart(index);
      } else {
        this.cart[index].quantity = quantity;
        this.saveCart();
        this.updateCartDisplay();
      }
    }

    updateCartDisplay() {
      const cartCounter = document.getElementById('cart-counter');
      const cartCount = document.getElementById('cart-count');
      const cartItems = document.getElementById('cart-items');
      const cartTotal = document.getElementById('cart-total');
      const viewCartBtn = document.getElementById('view-cart-btn');
      const clearCartBtn = document.getElementById('clear-cart-btn');

      const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
      const displayCount = totalItems > 99 ? '99+' : totalItems;

      // Обновляем счетчик
      if (cartCount) {
        cartCount.textContent = displayCount;
      }

      if (cartCounter) {
        if (totalItems > 0) {
          cartCounter.classList.add('visible');
        } else {
          cartCounter.classList.remove('visible');
        }
      }

      // Обновляем содержимое корзины
      if (cartItems) {
        if (this.cart.length === 0) {
          cartItems.innerHTML = `
            <div class="empty-cart" style="padding: 0 20px; color: #6b7280; display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; min-height: 200px; text-align: center;">
              <p style="margin-bottom: 16px; font-size: 18px; font-weight: 600; color: #374151;">Корзина пока пуста.</p>
              <p style="font-size: 17px; font-weight: 500; margin: 0; line-height: 1.6; max-width: 340px;">Добавляйте разные товары, чтобы оформить одним заказом.</p>
            </div>
          `;
        } else {
          // Отображение товаров в корзине - двухстрочный формат
          cartItems.innerHTML = this.cart.map((item, index) => `
            <div class="cart-item" style="padding: 16px 20px; border-bottom: 1px solid #e5e7eb; background: white;">
              <div class="cart-item-top-row" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <div class="cart-item-name" style="font-size: 15px; font-weight: 600; color: #1f2937; flex: 1; margin-right: 12px;">${item.name}</div>
                <div class="cart-quantity-selector" style="display: flex; align-items: center; border: 1px solid #d1d5db; overflow: hidden;">
                  <button class="cart-quantity-btn" onclick="cartManager.updateQuantity(${index}, ${item.quantity - 1})" style="background: #f3f4f6; border: none; width: 32px; height: 32px; cursor: pointer; font-size: 16px; font-weight: 600; color: #374151; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease; user-select: none;">-</button>
                  <div class="cart-quantity-display-wrapper" style="min-width: 32px; height: 32px; position: relative; background: white;">
                    <span class="cart-quantity-display" onclick="cartManager.editQuantity(${index}, this)" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: 500; cursor: pointer;">${item.quantity}</span>
                    <input type="number" min="1" max="999" value="${item.quantity}" class="cart-quantity-input" onblur="cartManager.saveQuantity(${index}, this)" onkeydown="cartManager.handleQuantityKeydown(${index}, this, event)" style="width: 100%; height: 100%; text-align: center; font-size: 14px; font-weight: 500; background: white; border: none; outline: none; position: absolute; top: 0; left: 0; display: none; -moz-appearance: textfield;" />
                  </div>
                  <button class="cart-quantity-btn" onclick="cartManager.updateQuantity(${index}, ${item.quantity + 1})" style="background: #f3f4f6; border: none; width: 32px; height: 32px; cursor: pointer; font-size: 16px; font-weight: 600; color: #374151; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease; user-select: none;">+</button>
                </div>
              </div>
              <div class="cart-item-bottom-row" style="display: flex; justify-content: space-between; align-items: center;">
                <div class="cart-item-total" style="font-size: 16px; font-weight: 700; color: #baa385;">${(parseFloat(item.price) * item.quantity).toFixed(2)} ${this.formatCartCurrency(item.currency || 'BYN')}${this.formatCartUnit(item)}</div>
                <button class="cart-remove-btn" onclick="cartManager.removeFromCart(${index})" style="background: #f3f4f6; border: 1px solid #d1d5db; color: #ef4444; cursor: pointer; padding: 8px; display: flex; align-items: center; justify-content: center; transition: all 0.2s; flex-shrink: 0; width: 96px; height: 32px; border-radius: 0; font-size: 11px; font-weight: 600; letter-spacing: 0.5px;" onmouseover="this.style.background='#ef4444'; this.style.color='white'; this.style.borderColor='#ef4444';" onmouseout="this.style.background='#f3f4f6'; this.style.color='#ef4444'; this.style.borderColor='#d1d5db';">
                  УДАЛИТЬ
                </button>
              </div>
            </div>
          `).join('');
        }
      }

      // Обновляем общую сумму
      if (cartTotal) {
        const totalAmountElement = cartTotal.querySelector('.total-amount');
        if (totalAmountElement) {
          // Группируем товары по валютам и считаем сумму для каждой валюты
          const currencyTotals = {};
          this.cart.forEach(item => {
            const currency = item.currency || 'BYN';
            const itemTotal = parseFloat(item.price) * item.quantity;
            currencyTotals[currency] = (currencyTotals[currency] || 0) + itemTotal;
          });

          // Формируем строку с суммами по валютам
          const totalStrings = Object.entries(currencyTotals).map(([currency, total]) =>
            `${total.toFixed(2)} ${this.formatCartCurrency(currency)}`
          );

          totalAmountElement.textContent = totalStrings.join(' + ');
        }

        // Скрываем/показываем блок общей суммы
        cartTotal.style.display = this.cart.length === 0 ? 'none' : 'flex';
      }

      // Обновляем состояние кнопок
      if (viewCartBtn) {
        viewCartBtn.disabled = this.cart.length === 0;
      }

      if (clearCartBtn) {
        clearCartBtn.disabled = this.cart.length === 0;
      }
    }

    toggleCart() {
      const cartDropdown = document.getElementById('cart-dropdown');
      const cartOverlay = document.getElementById('cart-overlay');

      if (cartDropdown && cartOverlay) {
        const isOpen = cartDropdown.classList.contains('open');

        if (isOpen) {
          this.closeCart();
        } else {
          cartDropdown.classList.add('open');
          cartOverlay.classList.add('open');
          // Предотвращаем прокрутку страницы когда корзина открыта
          document.body.style.overflow = 'hidden';
        }
      }
    }

    closeCart() {
      const cartDropdown = document.getElementById('cart-dropdown');
      const cartOverlay = document.getElementById('cart-overlay');

      if (cartDropdown && cartOverlay) {
        cartDropdown.classList.remove('open');
        cartOverlay.classList.remove('open');
        // Восстанавливаем прокрутку страницы
        document.body.style.overflow = '';
      }
    }

    viewCart() {
      if (this.cart.length === 0) return;

      // Перенаправляем на страницу корзины/заказа
      window.location.href = '/cart';
    }

    formatCartUnit(item) {
      // Если количество больше 1, не показываем единицу измерения
      if (item.quantity > 1) return '';

      // Получаем единицу измерения из варианта или товара
      const unit = item.variant?.unit || item.unit;
      if (!unit) return '';

      // Преобразуем технические ключи в русские названия (как в formatUnit.js)
      const unitMap = {
        // Вес
        'kg': 'кг',
        'g': 'г',
        'lb': 'фунт',
        'oz': 'унция',

        // Объем
        'L': 'л',
        'mL': 'мл',
        'm3': 'м³',
        'gal': 'галлон',

        // Размеры
        'm': 'м',
        'cm': 'см',
        'mm': 'мм',
        'm2': 'м²',

        // Штучные
        'piece': 'шт',
        'pack': 'упаковка',
        'set': 'набор',

        // Услуги
        'hour': 'час',
        'day': 'день',
        'month': 'месяц',
        'project': 'проект'
      };

      const formattedUnit = unitMap[unit] || unit;
      return ` / ${formattedUnit}`;
    }

    formatCartCurrency(currency) {
      // Преобразуем валюту в отображаемый символ
      const currencyMap = {
        'BYN': 'BYN',
        'USD': '$',
        'EUR': '€',
        'RUB': '₽'
      };

      return currencyMap[currency] || currency;
    }

    editQuantity(index, spanElement) {
      const wrapper = spanElement.parentElement;
      const input = wrapper.querySelector('.cart-quantity-input');

      // Скрываем span и показываем input
      spanElement.style.display = 'none';
      input.style.display = 'flex';
      input.focus();
      input.select();
    }

    saveQuantity(index, inputElement) {
      const wrapper = inputElement.parentElement;
      const span = wrapper.querySelector('.cart-quantity-display');
      const newQuantity = parseInt(inputElement.value) || 1;

      // Обновляем количество
      this.updateQuantity(index, newQuantity);

      // Возвращаем к отображению span
      inputElement.style.display = 'none';
      span.style.display = 'flex';
    }

    handleQuantityKeydown(index, inputElement, event) {
      if (event.key === 'Enter') {
        inputElement.blur(); // Это вызовет saveQuantity
      } else if (event.key === 'Escape') {
        const wrapper = inputElement.parentElement;
        const span = wrapper.querySelector('.cart-quantity-display');

        // Отменяем изменения и возвращаем исходное значение
        inputElement.value = this.cart[index].quantity;
        inputElement.style.display = 'none';
        span.style.display = 'flex';
      }
    }

    showAddedNotification(productName) {
      // Используем существующий toast manager если доступен
      if (window.toastManager) {
        window.toastManager.show({
          type: 'success',
          title: 'Товар добавлен',
          message: `${productName} добавлен в корзину`,
          duration: 3000
        });
      }
    }

    isProductInCart(productId, variant = null) {
      if (variant && variant.id) {
        // Для товаров с вариантами проверяем ID варианта напрямую
        return this.cart.some(item => item.id === variant.id);
      }
      // Для обычных товаров проверяем простой ID
      return this.cart.some(item => item.id === productId);
    }

    updateAddToCartButtons() {
      // Обновляем все кнопки "В корзину" на странице
      const buttons = document.querySelectorAll('.add-to-cart-btn');
      buttons.forEach(button => {
        const productId = button.dataset.productId;
        if (productId && this.isProductInCart(productId)) {
          this.setButtonAddedState(button);
        } else {
          this.setButtonNormalState(button);
        }
      });

      // Обновляем основную кнопку на странице товара
      const mainButton = document.getElementById('main-add-to-cart-btn');
      if (mainButton) {
        const productId = mainButton.dataset.productId;
        if (productId && this.isProductInCart(productId)) {
          this.setButtonAddedState(mainButton);
        } else {
          this.setButtonNormalState(mainButton);
        }
      }

      // Обновляем кнопки вариантов
      const variantContainers = document.querySelectorAll('[data-variant-id]');
      variantContainers.forEach(container => {
        const productId = container.closest('[data-product-id]')?.dataset.productId ||
                         document.querySelector('[data-product-id]')?.dataset.productId;
        const variantId = container.dataset.variantId;

        if (productId && variantId) {
          const orderButtons = container.querySelectorAll('.order-button');
          orderButtons.forEach(orderButton => {
            if (this.isProductInCart(productId, {id: variantId})) {
              this.setButtonAddedState(orderButton);
            } else {
              this.setButtonNormalState(orderButton);
            }
          });
        }
      });
    }

    setButtonAddedState(button) {
      button.textContent = 'Добавлено!';
      button.style.background = '#10b981 !important';
      button.style.borderColor = '#10b981 !important';
      button.style.color = 'white !important';
      button.disabled = false; // Оставляем кнопку активной
      button.classList.add('cart-added-state');
    }

    setButtonNormalState(button) {
      button.textContent = button.dataset.originalText || 'В корзину';
      button.style.background = '#baa385';
      button.style.borderColor = '';
      button.style.color = '';
      button.disabled = false;
      button.classList.remove('cart-added-state');
    }

    clearCart() {
      this.cart = [];
      this.saveCart();
      this.updateCartDisplay();
      this.updateAddToCartButtons();

      // Показываем уведомление
      if (window.toastManager) {
        window.toastManager.show({
          type: 'info',
          title: 'Корзина очищена',
          message: 'Все товары удалены из корзины',
          duration: 2000
        });
      }

      // Закрываем корзину
      this.closeCart();
    }
  }

  // Инициализация корзины при загрузке страницы
  document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('cart-button')) {
      window.cartManager = new ShoppingCartManager();
      // Обновляем состояние кнопок при загрузке
      setTimeout(() => {
        window.cartManager.updateAddToCartButtons();
      }, 100);
    }
  });

  // === ЭФФЕКТ: cart-quantity-input очищается при фокусе, восстанавливается при blur ===
  document.addEventListener('DOMContentLoaded', function() {
    const cartQtyInputs = document.querySelectorAll('.cart-quantity-input');
    cartQtyInputs.forEach(function(input) {
      input.removeAttribute('placeholder');
      input.addEventListener('focus', function(e) {
        const el = e.target as HTMLInputElement;
        if (el && !el.hasAttribute('data-prev')) {
          el.setAttribute('data-prev', el.value);
        }
        if (el) el.value = '';
      });
      input.addEventListener('blur', function(e) {
        const el = e.target as HTMLInputElement;
        if (el && el.value === '' && el.hasAttribute('data-prev')) {
          el.value = el.getAttribute('data-prev') || '';
        }
        if (el) el.removeAttribute('data-prev');
      });
    });
  });
</script>
