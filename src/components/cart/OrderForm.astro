---
// Компонент формы оформления заказа
---

<div class="order-form-container">
  <div class="order-form-header">
    <h2 class="text-xl font-bold text-text-main mb-4">Оформление заказа</h2>
  </div>

  <form id="orderForm" class="order-form">
    <!-- Тип клиента -->
    <div class="form-group">
      <label class="form-label">Тип клиента:</label>
      <div class="radio-group-inline">
        <label class="radio-option">
          <input type="radio" name="clientType" value="individual" checked />
          <span class="radio-custom"></span>
          <span class="radio-text">Физическое лицо</span>
        </label>
        <label class="radio-option">
          <input type="radio" name="clientType" value="legal" />
          <span class="radio-custom"></span>
          <span class="radio-text">Юридическое лицо (ИП, компания)</span>
        </label>
      </div>
    </div>

    <!-- Способ оплаты -->
    <div class="form-group">
      <label class="form-label">Способ оплаты:</label>
      <div class="radio-group-inline">
        <label class="radio-option">
          <input type="radio" name="paymentMethod" value="cash" checked />
          <span class="radio-custom"></span>
          <span class="radio-text">Наличный</span>
        </label>
        <label class="radio-option">
          <input type="radio" name="paymentMethod" value="cashless" />
          <span class="radio-custom"></span>
          <span class="radio-text">Безналичный</span>
        </label>
        <label class="radio-option">
          <input type="radio" name="paymentMethod" value="undecided" />
          <span class="radio-custom"></span>
          <span class="radio-text">Пока не определился</span>
        </label>
      </div>
    </div>

    <!-- Способ доставки -->
    <div class="form-group">
      <label class="form-label">Доставка:</label>
      <div class="radio-group-inline">
        <label class="radio-option">
          <input type="radio" name="deliveryMethod" value="pickup" checked />
          <span class="radio-custom"></span>
          <span class="radio-text">Самовывоз (Могилевская обл. г.Осиповичи, ул. Козловской ...)</span>
        </label>
        <label class="radio-option">
          <input type="radio" name="deliveryMethod" value="delivery" />
          <span class="radio-custom"></span>
          <span class="radio-text">С доставкой (желательно)</span>
        </label>
      </div>
    </div>

    <!-- Имя/Название компании -->
    <div class="form-group">
      <label for="clientName" class="form-label">
        <span id="clientNameLabel">Имя заказчика</span>
      </label>
      <input 
        type="text" 
        id="clientName" 
        name="clientName" 
        class="form-input" 
        placeholder="Введите ваше имя"
        required 
      />
    </div>

    <!-- Название компании (показывается только для юр. лица) -->
    <div class="form-group" id="companyNameGroup" style="display: none;">
      <label for="companyName" class="form-label">Название компании</label>
      <input 
        type="text" 
        id="companyName" 
        name="companyName" 
        class="form-input" 
        placeholder="Введите название компании"
      />
    </div>

    <!-- Контактный телефон -->
    <div class="form-group">
      <label for="phone" class="form-label">Контактный телефон</label>
      <input 
        type="tel" 
        id="phone" 
        name="phone" 
        class="form-input" 
        placeholder="+375 (XX) XXX-XX-XX"
        required 
      />
    </div>

    <!-- Комментарий -->
    <div class="form-group">
      <label for="comment" class="form-label">Комментарий</label>
      <textarea 
        id="comment" 
        name="comment" 
        class="form-textarea" 
        placeholder="Дополнительная информация к заказу (до 500 символов)"
        maxlength="500"
        rows="4"
      ></textarea>
      <div class="character-counter">
        <span id="commentCounter">0</span>/500 символов
      </div>
    </div>

    <!-- Кнопка отправки -->
    <div class="form-group">
      <button type="submit" class="submit-btn" id="submitOrderBtn">
        Отправить заявку
      </button>
    </div>
  </form>
</div>

<style>
  .order-form-container {
    background: #e5e5e5;
    padding: 20px;
    height: fit-content;
  }

  .order-form-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
  }

  .radio-group-inline {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  .radio-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-bottom: 8px;
  }

  .radio-option input[type="radio"] {
    display: none;
  }

  .radio-custom {
    width: 16px;
    height: 16px;
    border: 1px solid #6b7280;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
    transition: all 0.2s;
    background: white;
  }

  .radio-option input[type="radio"]:checked + .radio-custom {
    border-color: #374151;
  }

  .radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: #374151;
    border-radius: 50%;
  }

  .radio-text {
    color: #374151;
    font-size: 14px;
    line-height: 1.4;
  }

  .form-input,
  .form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #9ca3af;
    background: white;
    color: #374151;
    font-size: 14px;
    transition: border-color 0.2s;
    height: 36px;
  }

  .form-input:focus,
  .form-textarea:focus {
    outline: none;
    border-color: #6b7280;
  }

  .form-textarea {
    resize: vertical;
    min-height: 80px;
    height: auto;
    padding: 8px 12px;
  }

  .character-counter {
    text-align: right;
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
  }

  .submit-btn {
    width: 100%;
    background: #c8b499;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .submit-btn:hover {
    background: #b39f86;
  }

  .submit-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
  }

  @media (max-width: 768px) {
    .order-form-container {
      padding: 16px;
    }

    .radio-group-inline {
      flex-direction: column;
      gap: 8px;
    }

    .submit-btn {
      padding: 12px 20px;
      font-size: 15px;
    }
  }
</style>
</style>

<script>
  // Функция валидации белорусского номера телефона
  function validateBelarusianPhone(phone) {
    // Убираем все символы кроме цифр
    const digitsOnly = phone.replace(/\D/g, '');

    // Проверяем белорусские номера
    // Формат: +375 (XX) XXX-XX-XX или 375XXXXXXXXX
    if (digitsOnly.startsWith('375')) {
      // Полный номер с кодом страны: 375 + 2 цифры кода оператора + 7 цифр номера = 12 цифр
      return digitsOnly.length === 12;
    } else if (digitsOnly.length === 9) {
      // Номер без кода страны: 2 цифры кода оператора + 7 цифр номера = 9 цифр
      // Проверяем, что начинается с корректного кода оператора
      const operatorCodes = ['25', '29', '33', '44'];
      return operatorCodes.some(code => digitsOnly.startsWith(code));
    }

    return false;
  }

  document.addEventListener('DOMContentLoaded', function() {
    const clientTypeRadios = document.querySelectorAll('input[name="clientType"]');
    const clientNameLabel = document.getElementById('clientNameLabel');
    const clientNameInput = document.getElementById('clientName');
    const companyNameGroup = document.getElementById('companyNameGroup');
    const companyNameInput = document.getElementById('companyName');
    const phoneInput = document.getElementById('phone');
    const commentTextarea = document.getElementById('comment');
    const commentCounter = document.getElementById('commentCounter');
    const orderForm = document.getElementById('orderForm');

    // Обработка изменения типа клиента
    clientTypeRadios.forEach(radio => {
      radio.addEventListener('change', function() {
        if (this.value === 'legal') {
          clientNameLabel.textContent = 'Контактное лицо';
          clientNameInput.placeholder = 'Введите имя контактного лица';
          companyNameGroup.style.display = 'block';
          companyNameInput.required = true;
        } else {
          clientNameLabel.textContent = 'Имя заказчика';
          clientNameInput.placeholder = 'Введите ваше имя';
          companyNameGroup.style.display = 'none';
          companyNameInput.required = false;
          companyNameInput.value = '';
        }
      });
    });

    // Маска для телефона
    phoneInput.addEventListener('input', function(e) {
      let input = e.target.value.replace(/\D/g, '');
      let formattedInput = '';

      if (!input.startsWith('375')) {
        if (input.startsWith('8')) {
          input = input.substring(1);
        } else if (input.startsWith('0')) {
          input = input.substring(1);
        }
        formattedInput = '+375' + input;
      } else {
        formattedInput = '+375' + input.substring(3);
      }

      if (formattedInput.length > 4) {
        const digits = formattedInput.substring(4).replace(/\D/g, '');
        let currentFormat = '+375';
        if (digits.length > 0) currentFormat += ' (' + digits.substring(0, 2);
        if (digits.length > 2) currentFormat += ') ' + digits.substring(2, 5);
        if (digits.length > 5) currentFormat += '-' + digits.substring(5, 7);
        if (digits.length > 7) currentFormat += '-' + digits.substring(7, 9);
        formattedInput = currentFormat;
      } else if (formattedInput.length === 4) {
        formattedInput = '+375';
      }

      e.target.value = formattedInput.substring(0, 19);
    });

    // Счетчик символов для комментария
    commentTextarea.addEventListener('input', function() {
      const length = this.value.length;
      commentCounter.textContent = length;
      
      if (length > 450) {
        commentCounter.style.color = '#ef4444';
      } else if (length > 400) {
        commentCounter.style.color = '#f59e0b';
      } else {
        commentCounter.style.color = '#6b7280';
      }
    });

    // Обработка отправки формы
    orderForm.addEventListener('submit', async function(e) {
      e.preventDefault();
      
      const submitBtn = document.getElementById('submitOrderBtn');
      const originalText = submitBtn.textContent;
      
      // Проверяем, есть ли товары в корзине
      const cart = JSON.parse(localStorage.getItem('shopping-cart') || '[]');
      if (cart.length === 0) {
        alert('Добавьте товары в корзину перед оформлением заказа');
        return;
      }

      // Собираем данные формы
      const formData = new FormData(this);
      const clientName = formData.get('clientName');
      const phone = formData.get('phone');

      // Валидация обязательных полей
      if (!clientName || clientName.trim().length < 2) {
        alert('Пожалуйста, введите корректное имя (минимум 2 символа)');
        return;
      }

      if (!phone || !validateBelarusianPhone(phone)) {
        alert('Пожалуйста, введите корректный белорусский номер телефона\nПример: +375 (29) 123-45-67');
        return;
      }

      const orderData = {
        clientType: formData.get('clientType'),
        paymentMethod: formData.get('paymentMethod'),
        deliveryMethod: formData.get('deliveryMethod'),
        clientName: clientName.trim(),
        companyName: formData.get('companyName'),
        phone: phone,
        comment: formData.get('comment'),
        items: cart,
        totalAmount: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        orderDate: new Date().toISOString()
      };

      // Показываем состояние загрузки
      submitBtn.disabled = true;
      submitBtn.textContent = 'Отправляем...';

      // Отправка данных на сервер
      try {
        const response = await fetch('/api/orders/product', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(orderData)
        });

        const result = await response.json();

        if (result.success) {
          submitBtn.textContent = 'Заявка отправлена!';
          submitBtn.style.background = '#10b981';

          // Очищаем корзину после успешной отправки
          localStorage.removeItem('shopping-cart');

          // Показываем сообщение об успехе
          alert(`Заявка успешно отправлена! Номер заказа: ${result.orderId}`);

          // Перезагружаем страницу через 2 секунды
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          throw new Error(result.error || 'Ошибка при отправке заявки');
        }
      } catch (error) {
        console.error('Ошибка при отправке заказа:', error);
        alert('Ошибка при отправке заявки: ' + error.message);

        // Восстанавливаем кнопку
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
        submitBtn.style.background = '';
      }
    });
  });
</script>
