---
// Компонент блока консультации для нижней части страницы корзины
---

<section class="consultation-block">
  <div class="container">
    <div class="consultation-content">
      <!-- Заголовок -->
      <div class="consultation-header">
        <h2 class="consultation-title">Нужна консультация?</h2>
        <p class="consultation-subtitle">Напишите нам в удобном для вас мессенджере или оставьте заявку на обратную связь</p>
      </div>

      <div class="consultation-grid">
        <!-- Левая часть - Социальные сети -->
        <div class="social-links-section">
          <h3 class="section-title">Напишите нам:</h3>
          <div class="social-links">
            <a href="https://t.me/your_telegram" class="social-link telegram" target="_blank" rel="noopener noreferrer">
              <div class="social-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 0C5.374 0 0 5.374 0 12s5.374 12 12 12 12-5.374 12-12S18.626 0 12 0zm5.568 8.16c-.169 1.858-.896 6.728-.896 6.728-.896 6.728-1.268 7.928-1.268 7.928-.16.906-.576 1.056-.944.656 0 0-3.136-2.448-4.256-3.328-.544-.416-.864-.624-.896-1.072-.032-.448.288-.8.736-1.184 1.312-1.12 2.896-2.624 3.936-3.648.416-.416.832-.832.416-1.248-.416-.416-1.248 0-1.248 0-1.696 1.072-4.896 3.136-5.728 3.712-.832.576-1.216.624-1.728.576-.512-.048-1.632-.384-1.632-.384s-.96-.608-.064-1.248c0 0 7.424-2.688 9.984-3.616 1.12-.416 2.688-.896 2.688-.896s.96-.384.96.256z"/>
                </svg>
              </div>
              <div class="social-text">
                <span class="social-name">Telegram</span>
                <span class="social-description">Быстрые ответы</span>
              </div>
            </a>

            <a href="https://wa.me/375291234567" class="social-link whatsapp" target="_blank" rel="noopener noreferrer">
              <div class="social-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                </svg>
              </div>
              <div class="social-text">
                <span class="social-name">WhatsApp</span>
                <span class="social-description">+375 (29) 123-45-67</span>
              </div>
            </a>

            <a href="viber://chat?number=375291234567" class="social-link viber" target="_blank" rel="noopener noreferrer">
              <div class="social-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12.04 2c-5.46 0-9.91 4.45-9.91 9.91 0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21 5.46 0 9.91-4.45 9.91-9.91S17.5 2 12.04 2m4.52 7.33c.***********.14 1.18 0 .45-.14.73-.14 1.18-.14.35-.28.63-.49.84-.21.21-.49.35-.84.49-.35.14-.73.14-1.18.14-.45 0-.73-.14-1.18-.14-.35-.14-.63-.28-.84-.49-.21-.21-.35-.49-.49-.84-.14-.35-.14-.73-.14-1.18 0-.45.14-.73.14-1.18.14-.35.28-.63.49-.84.21-.21.49-.35.84-.49.35-.14.73-.14 1.18-.14.45 0 .73.14 **********.***********.***********.49.49.84"/>
                </svg>
              </div>
              <div class="social-text">
                <span class="social-name">Viber</span>
                <span class="social-description">+375 (29) 123-45-67</span>
              </div>
            </a>
          </div>
        </div>

        <!-- Правая часть - Форма обратной связи -->
        <div class="callback-form-section">
          <h3 class="section-title">Заказать обратный звонок:</h3>
          <form id="callbackForm" class="callback-form">
            <div class="form-row">
              <input 
                type="text" 
                name="name" 
                placeholder="Ваше имя" 
                class="form-input"
                required 
              />
              <input 
                type="tel" 
                name="phone" 
                id="callbackPhone"
                placeholder="+375 (XX) XXX-XX-XX" 
                class="form-input"
                required 
              />
            </div>
            <textarea 
              name="message" 
              placeholder="Ваш вопрос (необязательно)" 
              class="form-textarea"
              rows="3"
            ></textarea>
            <button type="submit" class="callback-btn" id="callbackSubmitBtn">
              Заказать звонок
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  .consultation-block {
    background: #f9fafb;
    padding: 60px 0;
    margin-top: 60px;
  }

  .consultation-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .consultation-header {
    text-align: center;
    margin-bottom: 48px;
  }

  .consultation-title {
    font-size: 32px;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 12px;
  }

  .consultation-subtitle {
    font-size: 18px;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .consultation-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 48px;
    align-items: start;
  }

  .section-title {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 24px;
  }

  .social-links {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .social-link {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: white;
    border: 1px solid #e5e7eb;
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .social-link.telegram:hover {
    border-color: #0088cc;
  }

  .social-link.whatsapp:hover {
    border-color: #25d366;
  }

  .social-link.viber:hover {
    border-color: #665cac;
  }

  .social-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    border-radius: 50%;
    transition: all 0.3s ease;
  }

  .telegram .social-icon {
    background: #e3f2fd;
    color: #0088cc;
  }

  .whatsapp .social-icon {
    background: #e8f5e8;
    color: #25d366;
  }

  .viber .social-icon {
    background: #f3f0ff;
    color: #665cac;
  }

  .social-text {
    display: flex;
    flex-direction: column;
  }

  .social-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 16px;
  }

  .social-description {
    color: #6b7280;
    font-size: 14px;
    margin-top: 2px;
  }

  .callback-form {
    background: white;
    padding: 24px;
    border: 1px solid #e5e7eb;
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
  }

  .form-input,
  .form-textarea {
    padding: 12px 16px;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    font-size: 14px;
    transition: border-color 0.2s;
  }

  .form-input:focus,
  .form-textarea:focus {
    outline: none;
    border-color: #c8b499;
  }

  .form-textarea {
    width: 100%;
    resize: vertical;
    margin-bottom: 16px;
  }

  .callback-btn {
    width: 100%;
    background: #c8b499;
    color: white;
    border: none;
    padding: 14px 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .callback-btn:hover {
    background: #b39f86;
  }

  .callback-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
  }

  @media (max-width: 768px) {
    .consultation-block {
      padding: 40px 0;
      margin-top: 40px;
    }

    .consultation-title {
      font-size: 24px;
    }

    .consultation-subtitle {
      font-size: 16px;
    }

    .consultation-grid {
      grid-template-columns: 1fr;
      gap: 32px;
    }

    .form-row {
      grid-template-columns: 1fr;
    }

    .social-link {
      padding: 12px 16px;
    }

    .social-icon {
      width: 40px;
      height: 40px;
      margin-right: 12px;
    }

    .callback-form {
      padding: 20px;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const callbackPhone = document.getElementById('callbackPhone');
    const callbackForm = document.getElementById('callbackForm');

    // Маска для телефона в форме обратной связи
    if (callbackPhone) {
      callbackPhone.addEventListener('input', function(e) {
        let input = e.target.value.replace(/\D/g, '');
        let formattedInput = '';

        if (!input.startsWith('375')) {
          if (input.startsWith('8')) {
            input = input.substring(1);
          } else if (input.startsWith('0')) {
            input = input.substring(1);
          }
          formattedInput = '+375' + input;
        } else {
          formattedInput = '+375' + input.substring(3);
        }

        if (formattedInput.length > 4) {
          const digits = formattedInput.substring(4).replace(/\D/g, '');
          let currentFormat = '+375';
          if (digits.length > 0) currentFormat += ' (' + digits.substring(0, 2);
          if (digits.length > 2) currentFormat += ') ' + digits.substring(2, 5);
          if (digits.length > 5) currentFormat += '-' + digits.substring(5, 7);
          if (digits.length > 7) currentFormat += '-' + digits.substring(7, 9);
          formattedInput = currentFormat;
        } else if (formattedInput.length === 4) {
          formattedInput = '+375';
        }

        e.target.value = formattedInput.substring(0, 19);
      });
    }

    // Обработка отправки формы обратной связи
    if (callbackForm) {
      callbackForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = document.getElementById('callbackSubmitBtn');
        const originalText = submitBtn.textContent;
        
        // Собираем данные формы
        const formData = new FormData(this);
        const callbackData = {
          name: formData.get('name'),
          phone: formData.get('phone'),
          message: formData.get('message'),
          requestDate: new Date().toISOString(),
          type: 'callback'
        };

        // Показываем состояние загрузки
        submitBtn.disabled = true;
        submitBtn.textContent = 'Отправляем...';

        // Здесь будет отправка данных на сервер
        console.log('Данные заявки на обратный звонок:', callbackData);
        
        // Имитация отправки
        setTimeout(() => {
          submitBtn.textContent = 'Заявка отправлена!';
          submitBtn.style.background = '#10b981';
          
          // Очищаем форму
          this.reset();
          
          // Показываем сообщение об успехе
          alert('Заявка на обратный звонок отправлена! Мы свяжемся с вами в ближайшее время.');
          
          // Возвращаем кнопку в исходное состояние
          setTimeout(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
            submitBtn.style.background = '#c8b499';
          }, 3000);
        }, 2000);
      });
    }
  });
</script>
