---
import HeaderTop from './HeaderTop.astro';
import Navigation from './Navigation.astro';
---

<header class="bg-white shadow-header border-b border-gray-100">
  <HeaderTop />
  
  <div class="container mx-auto px-4">
    <div class="flex items-center justify-between py-6">
      <!-- Logo -->
      <a href="/" class="flex-shrink-0">
        <img
          src="/images/logo.png"
          alt="Aizen Architecture"
          class="h-12 w-auto"
        />
      </a>

      <!-- Navigation -->
      <Navigation />
    </div>
  </div>
</header>

<!-- Sticky Header -->
<div
  id="sticky-header"
  class="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 shadow-header transform -translate-y-full transition-transform duration-300 z-50"
>
  <div class="container mx-auto px-4">
    <div class="flex items-center justify-between py-3">
      <a href="/" class="flex-shrink-0">
        <img
          src="/images/logo-small.png"
          alt="Aizen Architecture"
          class="h-8 w-auto"
        />
      </a>
      <Navigation />
    </div>
  </div>
</div>

<script>
  // Sticky header functionality
  const stickyHeader = document.getElementById('sticky-header');
  let lastScroll = 0;

  if (stickyHeader) {
    window.addEventListener('scroll', () => {
      const currentScroll = window.pageYOffset;

      if (currentScroll <= 0) {
        stickyHeader.classList.add('-translate-y-full');
        return;
      }

      if (currentScroll > lastScroll && !stickyHeader.classList.contains('-translate-y-full')) {
        // Scrolling down
        stickyHeader.classList.add('-translate-y-full');
      } else if (currentScroll < lastScroll && stickyHeader.classList.contains('-translate-y-full')) {
        // Scrolling up
        stickyHeader.classList.remove('-translate-y-full');
      }

      lastScroll = currentScroll;
    });
  }
</script> 