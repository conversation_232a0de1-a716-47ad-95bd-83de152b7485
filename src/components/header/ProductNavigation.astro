---
// This component will handle navigation links for products.
// It can dynamically generate links based on product categories or individual products.
import productsData from '../../../data/product/products.json';
import type { Product } from '../../types';

const products = productsData as Product[];

const categories = ["All", "Тротуарная плитка", "Брусчатка", "Бордюры", "Водостоки", "Сту<PERSON>е<PERSON><PERSON>", "Забор<PERSON>", "Малые архитектурные формы"];

// Generate unique category slugs
const categorySlugs = new Map<string, string>();
products.forEach(product => {
  if (product.category && product.categorySlug) {
    categorySlugs.set(product.category, product.categorySlug);
  }
});

function slugify(text: string) {
  return text.toString().toLowerCase()
    .replace(/\s+/g, '-')           // Replace spaces with -
    .replace(/[^\w-]+/g, '')       // Remove all non-word chars
    .replace(/--+/g, '-')         // Replace multiple - with single -
    .replace(/^-+/g, '')          // Trim - from start of text
    .replace(/-+$/g, '');         // Trim - from end of text
}
---
<nav class="product-navigation">
  <ul>
    <li><a href="/products">Все Продукты</a></li>
    {categories.map((category) => {
      const displayCategory = category === "All" ? "Все Категории" : category;
      const href = category === "All" ? "/products" : `/products/${categorySlugs.get(category) || slugify(category)}`;
      return (
        <li>
          <a href={href}>{displayCategory}</a>
        </li>
      );
    })}
  </ul>
</nav>

<style>
  .product-navigation ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .product-navigation a {
    text-decoration: none;
    color: inherit;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    transition: background-color 0.3s ease;
    white-space: nowrap;
  }

  .product-navigation a:hover {
    background-color: #f0f0f0;
  }
</style> 