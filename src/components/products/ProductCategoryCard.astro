---
export interface Props {
  image: string;
  title: string;
  description?: string;
  link?: string;
}
const { image, title, description, link } = Astro.props;
---
<article class="relative bg-white shadow-lg overflow-hidden h-full group transition-transform hover:-translate-y-1">
  <a href={link || '#'} class="block h-full w-full focus:outline-none">
    <img src={image} alt={title} class="w-full h-56 md:h-64 object-cover object-center select-none" loading="lazy" />
    <div class="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity"></div>
    <div class="absolute left-0 bottom-0 p-6 z-10">
      <h3 class="text-xl md:text-2xl font-bold text-white mb-1">{title}</h3>
      {description && <p class="text-white text-base md:text-lg drop-shadow-md">{description}</p>}
    </div>
  </a>
</article> 