---
import type { Product } from '../../types';
import Tooltip from '../ui/Tooltip.astro';
import formatPrice from '../../utils/formatPrice.js';
import formatUnit from '../../utils/formatUnit.js';
import { getMainPrice } from '../../utils/productPricing.js';

interface Props {
  product: Product;
  attributeTypesConfig?: any;
  'data-size'?: string;
  'data-color'?: string;
  'data-price'?: string | number;
  'data-name'?: string;
}

const { product, attributeTypesConfig = {} } = Astro.props;

// Определяем главную цену товара (основной вариант или базовая цена)
const mainPrice = getMainPrice(product);

// Определяем данные для корзины (основной вариант или базовый товар)
const primaryVariant = product.variants?.find(variant =>
  variant.isPrimaryPrice === true &&
  variant.price &&
  variant.price.value > 0
);

const cartProductData = primaryVariant ? {
  id: primaryVariant.id,
  name: `${product.name} (${primaryVariant.name})`,
  price: primaryVariant.price.value,
  currency: primaryVariant.price.currency,
  unit: primaryVariant.price.unit,
  isPrimaryVariant: true
} : {
  id: product.id,
  name: product.name,
  price: product.basePrice?.value || 0,
  currency: product.basePrice?.currency || 'BYN',
  unit: product.basePrice?.unit || 'piece',
  isPrimaryVariant: false
};

// Prepare size and color display
const getSizeDisplayValue = () => {
  const sizeAttr = product.attributes?.size;
  if (!sizeAttr) return null;

  if (Array.isArray(sizeAttr)) {
    // Если массив размеров, показываем первый и количество остальных
    const firstSize = sizeAttr[0];
    if (firstSize && firstSize.length && firstSize.width && firstSize.height) {
      const sizeText = `${firstSize.length}×${firstSize.width}×${firstSize.height} мм`;
      return sizeAttr.length > 1 ? `${sizeText} (+${sizeAttr.length - 1})` : sizeText;
    }
  } else if (sizeAttr.length && sizeAttr.width && sizeAttr.height) {
    // Если одиночный размер
    return `${sizeAttr.length}×${sizeAttr.width}×${sizeAttr.height} мм`;
  } else if (sizeAttr.variants?.[0]) {
    // Старый формат с вариантами
    return `${sizeAttr.variants[0].length}×${sizeAttr.variants[0].width}×${sizeAttr.variants[0].height} мм`;
  }
  return null;
};

const mainSizeValue = getSizeDisplayValue();

// Prepare color display - универсальная функция для отображения цветов
const getColorDisplayValue = () => {
  const colors = product.attributes?.colors || [];
  const colorPigments = product.attributes?.color_pigments;

  // Если есть массив цветов, показываем их
  if (colors.length > 0) {
    const firstColor = colors[0];
    const colorText = firstColor;
    const displayText = colors.length > 1 ? `${colorText} (+${colors.length - 1})` : colorText;

    // Если есть пигменты, добавляем их информацию
    if (colorPigments) {
      // Проверяем, является ли colorPigments массивом или объектом
      if (Array.isArray(colorPigments)) {
        // Если массив, берем первый элемент
        const firstPigment = colorPigments[0];
        if (firstPigment && firstPigment.name) {
          return `${displayText} (${firstPigment.name})`;
        }
      } else if (colorPigments.name) {
        // Если объект, используем его name
        return `${displayText} (${colorPigments.name})`;
      }
    }

    return displayText;
  }

  // Если нет цветов, но есть пигменты, показываем только пигменты
  if (colorPigments) {
    // Проверяем, является ли colorPigments массивом или объектом
    if (Array.isArray(colorPigments)) {
      // Если массив, берем первый элемент
      const firstPigment = colorPigments[0];
      if (firstPigment && firstPigment.name) {
        return firstPigment.name;
      }
    } else if (colorPigments.name) {
      // Если объект, используем его name
      return colorPigments.name;
    }
  }

  return null;
};

const mainColorValue = getColorDisplayValue();
// Получаем первый пигмент для tooltip (если это массив)
const colorPigments = (() => {
  const pigments = product.attributes?.color_pigments;
  if (Array.isArray(pigments)) {
    return pigments[0]; // Берем первый элемент массива
  }
  return pigments; // Возвращаем как есть, если это объект
})();

// Функция для получения отображаемого значения атрибута
const getAttributeDisplayValue = (attributeType: string, attributeValue: any) => {
  if (!attributeValue) return null;

  switch (attributeType) {
    case 'weight':
      if (Array.isArray(attributeValue)) {
        // Если массив весов, показываем первый и количество остальных
        const firstWeight = attributeValue[0];
        const weightText = `${firstWeight.value} ${firstWeight.unit}`;
        return attributeValue.length > 1 ? `${weightText} (+${attributeValue.length - 1})` : weightText;
      } else if (typeof attributeValue === 'object' && attributeValue.value && attributeValue.unit) {
        return `${attributeValue.value} ${attributeValue.unit}`;
      } else if (typeof attributeValue === 'number') {
        return `${attributeValue} кг`;
      }
      break;

    case 'strength_classes':
      if (typeof attributeValue === 'object' && attributeValue.class) {
        return attributeValue.class;
      }
      break;

    case 'water_absorption':
    case 'frost_resistance':
      if (typeof attributeValue === 'object' && attributeValue.class) {
        return attributeValue.class;
      }
      break;

    case 'textures':
      if (Array.isArray(attributeValue)) {
        return attributeValue.join(', ');
      }
      if (typeof attributeValue === 'string') {
        return attributeValue;
      }
      break;

    case 'surfaces':
    case 'patterns':
    case 'material':
    case 'tip_pokrytiya':
      // Для объектов с полями id, name, description
      if (typeof attributeValue === 'object' && attributeValue.name) {
        return attributeValue.name;
      }
      if (Array.isArray(attributeValue)) {
        return attributeValue.map(item =>
          typeof item === 'object' && item.name ? item.name : item
        ).join(', ');
      }
      if (typeof attributeValue === 'string') {
        return attributeValue;
      }
      break;

    default:
      // Для простых строковых атрибутов
      if (typeof attributeValue === 'string' && attributeValue.trim()) {
        return attributeValue;
      }
      if (Array.isArray(attributeValue)) {
        return attributeValue.join(', ');
      }
      // Для объектов с полем name (универсальная обработка)
      if (typeof attributeValue === 'object' && attributeValue.name) {
        return attributeValue.name;
      }
      break;
  }

  return null;
};

// Функция для получения названия атрибута
const getAttributeName = (attributeType: string) => {
  const names: Record<string, string> = {
    'textures': 'Текстура',
    'strength_classes': 'Класс прочности',
    'frost_resistance': 'Морозостойкость',
    'water_absorption': 'Водопоглощение',
    'surfaces': 'Поверхность',
    'patterns': 'Рисунок',
    'weight': 'Вес',
    'material': 'Материал',
    'tip_pokrytiya': 'Тип покрытия',
    'color_pigments': 'Пигмент'
  };
  return names[attributeType] || attributeType;
};

// Получаем все атрибуты для отображения (исключая специально обработанные)
const displayAttributes = Object.entries(product.attributes || {})
  .filter(([key]) => !['size', 'colors', 'color_pigments'].includes(key))
  .map(([key, value]) => {
    // Проверяем настройку отображения в карточке товара
    if (attributeTypesConfig[key]?.showInProductCard !== true) {
      return null;
    }

    const displayValue = getAttributeDisplayValue(key, value);
    if (!displayValue) return null;

    return {
      key,
      name: getAttributeName(key),
      value: displayValue
    };
  })
  .filter(attr => attr !== null);

// Prepare size data for rendering
let sizeAttributes = null;
if (product.attributes?.size) {
  if (product.attributes.size.variants) {
    sizeAttributes = product.attributes.size.variants;
  } else if (product.attributes.size.length) {
    sizeAttributes = [{ length: product.attributes.size.length, width: product.attributes.size.width }];
  }
}

// Prepare color data for rendering
let colorAttributes = null;
if (product.attributes?.colors && product.attributes.colors.length > 0) {
    colorAttributes = product.attributes.colors;
}

---

{/* Product Card List View */}
<div class="product-card bg-white shadow-md rounded-none overflow-hidden group flex flex-col md:flex-row list-view"
     data-category={product.category}
     data-size={Astro.props['data-size']}
     data-color={Astro.props['data-color']}
     data-price={Astro.props['data-price']}
     data-name={Astro.props['data-name']}
     data-subcategory={product.subcategory ? product.subcategory.toLowerCase().replace(/\s+/g, '-') : ''}>
  {/* Image Section (clickable) */}
  <a href={`/products/${product.categorySlug}/${product.slug}`} class="relative overflow-hidden flex-shrink-0 w-full md:w-48 aspect-square block">
    <img
      src={`/product/${product.images.main}`}
      alt={product.name}
      class="w-full h-full object-cover transform group-hover:scale-105 transition-transform duration-300"
    />
  </a>

  {/* Details Section (List View) */}
  <div class="p-4 flex-grow flex flex-col">
    {/* Top row: Name and Price */}
    <div class="flex justify-between items-start mb-2">
      <h3 class="text-xl font-bold text-text-main mr-4 line-clamp-2">
        <a href={`/products/${product.categorySlug}/${product.slug}`} class="hover:underline">{product.name}</a>
      </h3>
      <p class="text-[#baa385] text-2xl font-semibold flex-shrink-0">
        {mainPrice ? formatPrice({
          amount: mainPrice.value,
          simvol: mainPrice.simvol || mainPrice.currency || 'BYN',
          format: '{amount} {simvol}',
          decimalSeparator: '.',
          thousandsSeparator: ' ',
          decimals: 2
        }) : '0 BYN'}{mainPrice?.unit ? ` / ${formatUnit(mainPrice.unit)}` : ''}
      </p>
    </div>

    {/* Article */}
    <p class="text-gray-600 text-sm mt-1 mb-1">Арт: {product.id}</p>

    {/* Size */}
    {mainSizeValue && attributeTypesConfig.size?.showInProductCard === true && (
      <p class="text-gray-700 text-sm mt-1 mb-1">
        <span class="font-semibold">Размер:</span> {mainSizeValue}
      </p>
    )}

    {/* Color */}
    {mainColorValue && attributeTypesConfig.colors?.showInProductCard === true && (
      <p class="text-gray-700 text-sm mt-1 mb-1">
        <span class="font-semibold">Цвет:</span> {mainColorValue}
        {colorPigments && (
          <Tooltip
            content={colorPigments.description}
            position="top"
            size="md"
            triggerClass="ml-1"
          />
        )}
      </p>
    )}

    {/* Other Attributes */}
    {displayAttributes.map((attr) => (
      <p class="text-gray-700 text-sm mt-1 mb-1">
        <span class="font-semibold">{attr.name}:</span> {attr.value}
      </p>
    ))}

    {/* Short Description */}
    {product.shortDescription && (
      <p class="text-gray-700 text-sm mt-2 mb-4 line-clamp-3">{product.shortDescription}</p>
    )}

    {/* Buttons */}
    <div class="flex space-x-2 mt-auto">
      <a href={`/products/${product.categorySlug}/${product.slug}`} class="flex-grow border border-[#baa385] bg-white text-[#baa385] font-medium py-2.5 sm:py-2.5 lg:py-2 text-center hover:bg-gray-50 transition-colors uppercase">Подробнее</a>
      <button
        class="add-to-cart-btn flex-grow bg-[#baa385] text-white font-medium py-2.5 sm:py-2.5 lg:py-2 text-center hover:bg-[#a89274] transition-colors border-none cursor-pointer uppercase"
        data-product-id={cartProductData.id}
        data-product-name={cartProductData.name}
        data-product-price={cartProductData.price}
        data-product-currency={cartProductData.currency}
        data-product-unit={cartProductData.unit}
        data-product-image={`/product/${product.images.main}`}
        data-product-category={product.category}
        data-product-slug={product.slug}
        data-is-primary-variant={cartProductData.isPrimaryVariant}
        data-original-text="В КОРЗИНУ"
      >
        В КОРЗИНУ
      </button>
    </div>
  </div>
</div>

<script>
  // Обработчик добавления товара в корзину для списочного вида карточек товаров
  document.addEventListener('DOMContentLoaded', () => {
    const addToCartButtons = document.querySelectorAll('.add-to-cart-btn:not([data-cart-handler])');

    addToCartButtons.forEach(button => {
      // Помечаем кнопку как обработанную
      button.setAttribute('data-cart-handler', 'true');

      button.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const isPrimaryVariant = button.dataset.isPrimaryVariant === 'true';

        const productData = {
          id: button.dataset.productId,
          name: button.dataset.productName,
          price: parseFloat(button.dataset.productPrice) || 0,
          currency: button.dataset.productCurrency || 'BYN',
          unit: button.dataset.productUnit || 'piece',
          image: button.dataset.productImage,
          category: button.dataset.productCategory,
          slug: button.dataset.productSlug,
          quantity: 1
        };

        // Если это основной вариант, добавляем информацию о варианте
        if (isPrimaryVariant) {
          productData.variant = {
            id: button.dataset.productId,
            name: button.dataset.productName.split(' (')[1]?.replace(')', '') || 'Основной вариант',
            price: parseFloat(button.dataset.productPrice) || 0,
            currency: button.dataset.productCurrency || 'BYN',
            unit: button.dataset.productUnit || 'piece'
          };
        }

        // Добавляем товар в корзину если менеджер корзины доступен
        if (window.cartManager) {
          window.cartManager.addToCart(productData);

          // Визуальная обратная связь
          const originalText = button.textContent;
          button.textContent = 'Добавлено!';
          button.style.background = '#10b981';

          setTimeout(() => {
            button.textContent = originalText;
            button.style.background = '#baa385';
          }, 1500);
        } else {
          // Fallback: перенаправляем на страницу заказа
          window.location.href = '/cart';
        }
      });
    });
  });
</script>
