---
import type { Product } from '../../types';

interface Props {
  product: Product;
}

const { product } = Astro.props;

// Prepare size options
const sizeOptions = product.attributes?.size?.variants 
  ? product.attributes.size.variants.map(variant => ({
      value: `${variant.length}x${variant.width}`,
      label: `${variant.length} x ${variant.width} мм`
    }))
  : product.attributes?.size?.length 
    ? [{
        value: `${product.attributes.size.length}x${product.attributes.size.width}`,
        label: `${product.attributes.size.length} x ${product.attributes.size.width} мм`
      }]
    : [];

// Prepare color options
const colorOptions = product.attributes?.colors || [];
---

<div class="product-variants space-y-4">
  <!-- Size Selection -->
  {sizeOptions.length > 0 && (
    <div class="size-selection">
      <h4 class="text-lg font-semibold mb-2">Размер</h4>
      <div class="flex flex-wrap gap-2">
        {sizeOptions.map((size) => (
          <label class="relative inline-flex items-center">
            <input
              type="radio"
              name="size"
              value={size.value}
              class="peer sr-only"
              data-price={product.basePrice?.value || 0}
            />
            <div class="px-4 py-2 border border-gray-300 rounded-none cursor-pointer peer-checked:border-primary peer-checked:bg-primary/10 hover:border-primary/50 transition-colors">
              {size.label}
            </div>
          </label>
        ))}
      </div>
    </div>
  )}

  <!-- Color Selection -->
  {colorOptions.length > 0 && (
    <div class="color-selection">
      <h4 class="text-lg font-semibold mb-2">Цвет</h4>
      <div class="flex flex-wrap gap-2">
        {colorOptions.map((color) => (
          <label class="relative inline-flex items-center">
            <input
              type="radio"
              name="color"
              value={color}
              class="peer sr-only"
            />
            <div 
              class="w-8 h-8 rounded-full border border-gray-300 cursor-pointer peer-checked:border-primary peer-checked:ring-2 peer-checked:ring-primary/50 hover:border-primary/50 transition-colors"
              style={`background-color: ${color.toLowerCase().replace(' ', '')}`}
              title={color}
            ></div>
          </label>
        ))}
      </div>
    </div>
  )}
</div>

<script>
  // Handle variant selection and price updates
  const sizeInputs = document.querySelectorAll('input[name="size"]');
  const colorInputs = document.querySelectorAll('input[name="color"]');
  const priceDisplay = document.querySelector('.product-price');

  function updatePrice() {
    const selectedSize = document.querySelector('input[name="size"]:checked');
    if (selectedSize && priceDisplay) {
      const basePrice = parseFloat(selectedSize.getAttribute('data-price') || '0');
      // Here you can add logic for price variations based on size/color combinations
      priceDisplay.textContent = `${basePrice.toFixed(2)} руб. / м²`;
    }
  }

  // Add event listeners for variant selection
  sizeInputs.forEach(input => {
    input.addEventListener('change', updatePrice);
  });

  colorInputs.forEach(input => {
    input.addEventListener('change', () => {
      // Add any color-specific logic here
      updatePrice();
    });
  });

  // Initialize with first option selected if available
  if (sizeInputs.length > 0) {
    (sizeInputs[0] as HTMLInputElement).checked = true;
    updatePrice();
  }
  if (colorInputs.length > 0) {
    (colorInputs[0] as HTMLInputElement).checked = true;
  }
</script> 