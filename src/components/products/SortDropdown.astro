---
const { options = [
  { value: 'default', label: 'По умолчанию' },
  { value: 'price-asc', label: 'По цене (возрастание)' },
  { value: 'price-desc', label: 'По цене (убывание)' },
  { value: 'name-asc', label: 'По названию (А-Я)' },
  { value: 'name-desc', label: 'По названию (Я-А)' },
], selected = 'default', className = '' } = Astro.props;
---
<div class={`relative ${className} sort-dropdown-root`}>
  <button id="sort-dropdown-btn" type="button"
    class="px-3 py-1.5 border rounded-none bg-white focus:outline-none focus:ring-primary focus:border-primary flex items-center justify-between min-w-[200px] w-full"
    aria-haspopup="listbox"
    aria-expanded="false"
    aria-label="Сортировка"
  >
    <span id="sort-selected-text">{options.find((opt: any) => opt.value === selected)?.label || options[0].label}</span>
    <svg class="w-4 h-4 ml-2 transition-transform duration-200" id="sort-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
    </svg>
  </button>
  <div id="sort-dropdown-menu" class="absolute top-full left-0 w-full bg-white border border-gray-300 shadow-lg z-10 hidden">
    {options.map((opt: any) => (
      <button
        type="button"
        class={`w-full px-3 py-2 text-left hover:bg-gray-100 focus:outline-none sort-option${selected === opt.value ? ' bg-primary text-white' : ''}`}
        data-value={opt.value}
        role="option"
        aria-selected={selected === opt.value}
      >
        {opt.label}
      </button>
    ))}
  </div>
</div>

<script is:inline>
(function() {
  const btn = document.getElementById('sort-dropdown-btn');
  const menu = document.getElementById('sort-dropdown-menu');
  const arrow = document.getElementById('sort-dropdown-arrow');
  const selectedText = document.getElementById('sort-selected-text');
  let isOpen = false;

  function openDropdown() {
    menu.classList.remove('hidden');
    btn.setAttribute('aria-expanded', 'true');
    arrow.classList.add('rotate-180');
    isOpen = true;
  }
  function closeDropdown() {
    menu.classList.add('hidden');
    btn.setAttribute('aria-expanded', 'false');
    arrow.classList.remove('rotate-180');
    isOpen = false;
  }
  btn.addEventListener('click', function(e) {
    e.stopPropagation();
    if (isOpen) {
      closeDropdown();
    } else {
      openDropdown();
    }
  });
  menu.querySelectorAll('.sort-option').forEach(optionBtn => {
    optionBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      const value = this.getAttribute('data-value');
      if (selectedText) selectedText.textContent = this.textContent;
      closeDropdown();
      if (window.handleSortChange) window.handleSortChange(value);
    });
  });
  document.addEventListener('click', function(e) {
    if (!btn.contains(e.target) && !menu.contains(e.target)) {
      closeDropdown();
    }
  });
})();
</script>

<style is:inline>
  .sort-option.bg-primary {
    background: #baa385 !important;
    color: #fff !important;
  }
</style> 