---
export interface Props {
  photo: string;
  name: string;
  review: string;
}
const { photo, name, review } = Astro.props;
---
<div class="flex flex-col bg-[#e9e9e9] shadow-md w-full h-full">
  <div class="w-full aspect-square bg-gray-500 flex items-center justify-center text-white text-2xl font-bold">
    {photo ? <img src={photo} alt={name} class="w-full h-full object-cover object-center" loading="lazy" /> : 'фото'}
  </div>
  <div class="p-4 flex-1">
    <div class="font-semibold text-lg mb-1">{name}</div>
    <div class="font-bold text-base mb-1">Отзыв:</div>
    <div class="text-sm text-gray-700 whitespace-pre-line">{review}</div>
  </div>
</div> 