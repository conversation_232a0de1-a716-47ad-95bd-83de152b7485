---
interface Props {
  id?: string;
}

const {
  id = 'toast-container'
} = Astro.props;
---

<!-- Toast Container -->
<div
  id={id}
  class="fixed bottom-4 right-4 z-[9999] flex flex-col gap-2 pointer-events-none max-w-sm w-full"
  style="max-width: 384px;"
>
  <!-- Toast notifications will be dynamically added here -->
</div>

<script>
  class ToastManager {
    constructor(containerId) {
      this.container = document.getElementById(containerId);
      this.toasts = new Map();
      this.toastCounter = 0;
    }

    show(options = {}) {
      const {
        message = 'Уведомление',
        type = 'success', // success, error, warning, info
        duration = 2000,
        title = null
      } = options;

      const toastId = `toast-${++this.toastCounter}`;

      // Create toast element
      const toast = this.createToastElement(toastId, message, type, title);

      // Add to container
      this.container.appendChild(toast);
      this.toasts.set(toastId, toast);

      // Trigger animation
      requestAnimationFrame(() => {
        toast.classList.remove('translate-x-full', 'opacity-0');
        toast.classList.add('translate-x-0', 'opacity-100');
      });

      // Auto remove after duration
      setTimeout(() => {
        this.remove(toastId);
      }, duration);

      return toastId;
    }

    createToastElement(id, message, type, title) {
      const toast = document.createElement('div');
      toast.id = id;
      toast.className = `
        transform translate-x-full opacity-0 transition-all duration-300 ease-out
        pointer-events-auto w-full bg-white rounded-lg shadow-lg border-l-4
        ${this.getTypeStyles(type)}
      `.replace(/\s+/g, ' ').trim();

      const iconSvg = this.getIconSvg(type);
      const titleHtml = title ? `<p class="text-sm font-semibold ${this.getTextColor(type)}">${title}</p>` : '';

      toast.innerHTML = `
        <div class="p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              ${iconSvg}
            </div>
            <div class="ml-3 w-0 flex-1">
              ${titleHtml}
              <p class="text-sm text-gray-700">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0 flex">
              <button
                class="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150"
                onclick="window.toastManager?.remove('${id}')"
              >
                <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      `;

      return toast;
    }

    getTypeStyles(type) {
      const styles = {
        success: 'border-green-400 bg-green-50',
        error: 'border-red-400 bg-red-50',
        warning: 'border-yellow-400 bg-yellow-50',
        info: 'border-blue-400 bg-blue-50'
      };
      return styles[type] || styles.info;
    }

    getTextColor(type) {
      const colors = {
        success: 'text-green-800',
        error: 'text-red-800',
        warning: 'text-yellow-800',
        info: 'text-blue-800'
      };
      return colors[type] || colors.info;
    }

    getIconSvg(type) {
      const icons = {
        success: `
          <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
        `,
        error: `
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
          </svg>
        `,
        warning: `
          <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
        `,
        info: `
          <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
          </svg>
        `
      };
      return icons[type] || icons.info;
    }

    remove(toastId) {
      const toast = this.toasts.get(toastId);
      if (toast) {
        // Animate out
        toast.classList.remove('translate-x-0', 'opacity-100');
        toast.classList.add('translate-x-full', 'opacity-0');

        // Remove from DOM after animation
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
          this.toasts.delete(toastId);
        }, 300);
      }
    }

    clear() {
      this.toasts.forEach((toast, id) => {
        this.remove(id);
      });
    }
  }

  // Initialize toast manager when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('toast-container')) {
      window.toastManager = new ToastManager('toast-container');
    }
  });
</script>

<style>
  /* Ensure toasts appear above everything */
  #toast-container {
    z-index: 9999 !important;
    pointer-events: none;
  }

  /* Mobile responsive adjustments */
  @media (max-width: 768px) {
    #toast-container {
      left: 1rem !important;
      right: 1rem !important;
      bottom: 1rem !important;
      max-width: none !important;
      width: calc(100% - 2rem) !important;
      align-items: center;
    }
  }

  /* Desktop positioning */
  @media (min-width: 769px) {
    #toast-container {
      right: 1rem !important;
      bottom: 1rem !important;
      left: auto !important;
      max-width: 384px !important;
      width: 384px !important;
    }
  }

  /* Toast element styles */
  #toast-container > div {
    pointer-events: auto;
    margin-bottom: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
</style>
