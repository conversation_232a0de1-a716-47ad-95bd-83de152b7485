---
// Переиспользуемый кастомный select-компонент
// Props: options (массив объектов { value, label }), value, onChange, placeholder, name, id, disabled
const { options = [], value = '', onChange = null, placeholder = 'Выберите...', name = '', id = '', disabled = false } = Astro.props;
let isOpen = false;
let selected = options.find((opt: any) => opt.value === value) || null;

function handleSelect(option: any) {
  if (disabled) return;
  selected = option;
  isOpen = false;
  if (onChange) onChange(option.value);
  // Для Astro: можно использовать кастомное событие
  const event = new CustomEvent('change', { detail: option.value });
  document.getElementById(id)?.dispatchEvent(event);
}

function toggleDropdown() {
  if (disabled) return;
  isOpen = !isOpen;
}

function closeDropdown(e: any) {
  if (!e.target.closest('.custom-select-container')) {
    isOpen = false;
  }
}
if (typeof window !== 'undefined') {
  window.addEventListener('click', closeDropdown);
}
---
<div class="custom-select-container" data-name="{name}" data-id="{id}" data-disabled="{disabled}" data-placeholder="{placeholder}" data-value="{value}" data-options='{JSON.stringify(options)}' tabindex="0">
  <div class="custom-select-selected">
    <span class="custom-select-value">{placeholder}</span>
    <span class="custom-select-arrow" aria-hidden="true"></span>
  </div>
  <ul class="custom-select-dropdown" style="display:none;"></ul>
  <input type="hidden" name="{name}" value="{value}" />
</div>

<style>
.custom-select-container {
  position: relative;
  width: 100%;
  min-width: 160px;
  font-size: 1rem;
  user-select: none;
}
.custom-select-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 2.2rem 0.5rem 0.75rem;
  cursor: pointer;
  transition: border 0.2s, box-shadow 0.2s, background 0.2s;
  min-height: 2.25rem;
  position: relative;
}
.custom-select-selected.open,
.custom-select-selected:focus,
.custom-select-selected:hover {
  border-color: #3b82f6;
  background: #f3f4f6;
}
.custom-select-selected.disabled {
  background: #f1f5f9;
  color: #a1a1aa;
  cursor: not-allowed;
}
.custom-select-value {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.custom-select-arrow {
  position: absolute;
  right: 0.9rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.1rem;
  height: 1.1rem;
  pointer-events: none;
  background: url('data:image/svg+xml;utf8,<svg fill=\"none\" stroke=\"%233b82f6\" stroke-width=\"2\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\"><path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M19 9l-7 7-7-7\"/></svg>') no-repeat center/contain;
}
.custom-select-dropdown {
  position: absolute;
  z-index: 20;
  left: 0;
  right: 0;
  margin-top: 0.25rem;
  background: #fff;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 8px 24px -4px rgba(0,0,0,0.08);
  padding: 0.25rem 0;
  max-height: 16rem;
  overflow-y: auto;
  animation: fadeIn 0.18s;
}
.custom-select-option {
  padding: 0.65rem 1.2rem;
  cursor: pointer;
  transition: background 0.15s, color 0.15s;
  border-radius: 0.375rem;
  margin: 0.1rem 0.25rem;
  font-size: 1rem;
  line-height: 1.7;
}
.custom-select-option:hover,
.custom-select-option:focus {
  background: #f3f4f6;
  color: #2563eb;
}
.custom-select-option.selected {
  background: #e0e7ff;
  color: #1e40af;
  font-weight: 500;
}
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-8px); }
  to { opacity: 1; transform: translateY(0); }
}
</style> 