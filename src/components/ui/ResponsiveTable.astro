---
export interface Column {
  key: string;
  label: string;
  sortable?: boolean;
  className?: string;
}

export interface Props {
  columns: Column[];
  data: any[];
  class?: string;
  mobileBreakpoint?: 'sm' | 'md' | 'lg';
}

const { 
  columns, 
  data, 
  class: className = '',
  mobileBreakpoint = 'md'
} = Astro.props;

const breakpointClasses = {
  sm: 'sm:table',
  md: 'md:table',
  lg: 'lg:table'
};
---

<div class={`${className}`}>
  <!-- Desktop Table -->
  <div class={`hidden ${breakpointClasses[mobileBreakpoint]} overflow-x-auto`}>
    <table class="min-w-full divide-y divide-gray-200">
      <thead>
        <tr class="bg-gray-50">
          {columns.map(column => (
            <th scope="col" class={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.className || ''}`}>
              {column.label}
            </th>
          ))}
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <slot />
      </tbody>
    </table>
  </div>

  <!-- Mobile Cards -->
  <div class={`${breakpointClasses[mobileBreakpoint].replace('table', 'hidden')} space-y-4`}>
    {data.map(item => (
      <div class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
        {columns.map(column => (
          <div class="flex justify-between py-2 border-b border-gray-100 last:border-b-0">
            <span class="text-sm font-medium text-gray-500">{column.label}:</span>
            <span class="text-sm text-gray-900">
              <slot name="mobile-cell" {item} {column} />
            </span>
          </div>
        ))}
      </div>
    ))}
  </div>
</div>
