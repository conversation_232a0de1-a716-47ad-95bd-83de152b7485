---
export interface Props {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline' | 'success' | 'warning';
  class?: string;
}

const { variant = 'default', class: className = '' } = Astro.props;

const baseClasses = 'inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset';

const variantClasses = {
  default: 'bg-blue-50 text-blue-700 ring-blue-600/20',
  secondary: 'bg-gray-50 text-gray-600 ring-gray-500/10',
  destructive: 'bg-red-50 text-red-700 ring-red-600/20',
  outline: 'text-gray-900 ring-gray-300',
  success: 'bg-green-50 text-green-700 ring-green-600/20',
  warning: 'bg-yellow-50 text-yellow-800 ring-yellow-600/20'
};

const classes = `${baseClasses} ${variantClasses[variant]} ${className}`;
---

<span class={classes}>
  <slot />
</span>
