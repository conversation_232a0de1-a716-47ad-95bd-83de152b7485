---
export interface Props {
  type?: 'text' | 'email' | 'password' | 'number' | 'search' | 'tel' | 'url';
  placeholder?: string;
  value?: string;
  name?: string;
  id?: string;
  class?: string;
  disabled?: boolean;
  required?: boolean;
  readonly?: boolean;
  autocomplete?: string;
}

const {
  type = 'text',
  placeholder,
  value,
  name,
  id,
  class: className = '',
  disabled = false,
  required = false,
  readonly = false,
  autocomplete
} = Astro.props;

const baseClasses = 'flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-colors';

const classes = `${baseClasses} ${className}`;
---

<input
  type={type}
  placeholder={placeholder}
  value={value}
  name={name}
  id={id}
  class={classes}
  disabled={disabled}
  required={required}
  readonly={readonly}
  autocomplete={autocomplete}
/>
