---
import QuantitySelector from './QuantitySelector.astro';

export interface Props {
  productId?: string;
  productSku?: string;
  productName?: string;
  productPrice?: number;
  productCurrency?: string;
  productUnit?: string;
  productSize?: any;
  isPhysical?: boolean;
}

const {
  productId = '',
  productSku = '',
  productName = '',
  productPrice = 0,
  productCurrency = 'BYN',
  productUnit = 'piece',
  productSize = null,
  isPhysical = true
} = Astro.props;
---

<!-- Modal Overlay -->
<div id="quick-order-modal" class="quick-order-modal" style="display: none;">
  <div class="modal-overlay" data-close-modal></div>
  <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">Быстрое оформление заявки</h3>
      <button type="button" class="modal-close" data-close-modal aria-label="Закрыть">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </button>
    </div>

    <form id="quick-order-form" class="quick-order-form">
      <!-- Product Info -->
      <div class="form-group">
        <label class="form-label">Товар:</label>
        <div class="product-info">
          <span class="product-name" data-product-name>{productName}</span>
          <span class="product-article">(арт. <span data-product-sku>{productSku}</span>)</span>
        </div>
      </div>

      <!-- Quantity -->
      <div class="form-group">
        <label class="form-label">Количество:</label>
        <div class="quantity-row">
          <QuantitySelector 
            id="quick-order-quantity"
            class="quantity-selector-modal"
            value={1}
            min={1}
            max={999}
          />
          <span class="quantity-info" data-quantity-info>
            {isPhysical && productSize ? '(1 шт = 0.00 м²)' : ''}
          </span>
        </div>
      </div>

      <!-- Customer Name -->
      <div class="form-group">
        <label for="quick-order-name" class="form-label">Имя заказчика <span class="required">*</span></label>
        <input 
          type="text" 
          id="quick-order-name" 
          name="customerName" 
          class="form-input" 
          placeholder="Введите ваше имя"
          required 
          minlength="2"
        />
      </div>

      <!-- Phone -->
      <div class="form-group">
        <label for="quick-order-phone" class="form-label">Телефон <span class="required">*</span></label>
        <input 
          type="tel" 
          id="quick-order-phone" 
          name="phone" 
          class="form-input" 
          placeholder="+375 (XX) XXX-XX-XX"
          required 
        />
      </div>

      <!-- Comment -->
      <div class="form-group">
        <label for="quick-order-comment" class="form-label">Комментарий</label>
        <textarea 
          id="quick-order-comment" 
          name="comment" 
          class="form-textarea" 
          placeholder="Дополнительная информация к заказу (до 500 символов)"
          maxlength="500"
          rows="3"
        ></textarea>
        <div class="character-counter">
          <span id="quick-order-comment-counter">0</span>/500 символов
        </div>
      </div>

      <!-- Submit Button -->
      <div class="form-group">
        <button type="submit" class="submit-btn" id="quick-order-submit">
          Отправить заявку
        </button>
      </div>
    </form>
  </div>
</div>

<style>
  .quick-order-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
  }

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
  }

  .modal-content {
    position: relative;
    background: white;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e5e7eb;
  }

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }

  .modal-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    color: #6b7280;
    transition: color 0.2s;
  }

  .modal-close:hover {
    color: #374151;
  }

  .quick-order-form {
    padding: 20px 24px 24px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 6px;
  }

  .required {
    color: #dc2626;
  }

  .product-info {
    padding: 12px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    font-size: 14px;
  }

  .product-name {
    font-weight: 600;
    color: #111827;
  }

  .product-article {
    color: #6b7280;
    font-size: 13px;
    margin-left: 8px;
  }

  .quantity-row {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .quantity-info {
    font-size: 13px;
    color: #6b7280;
  }

  .form-input,
  .form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #9ca3af;
    background: white;
    color: #374151;
    font-size: 14px;
    transition: border-color 0.2s;
    height: 36px;
  }

  .form-input:focus,
  .form-textarea:focus {
    outline: none;
    border-color: #6b7280;
  }

  .form-textarea {
    resize: vertical;
    min-height: 80px;
    height: auto;
    padding: 8px 12px;
  }

  .character-counter {
    text-align: right;
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
  }

  .submit-btn {
    width: 100%;
    background: #baa385;
    color: white;
    border: none;
    padding: 12px 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .submit-btn:hover:not(:disabled) {
    background: #a89274;
  }

  .submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Mobile adjustments */
  @media (max-width: 640px) {
    .quick-order-modal {
      padding: 10px;
    }

    .modal-content {
      max-height: 95vh;
    }

    .modal-header {
      padding: 16px 20px 12px;
    }

    .quick-order-form {
      padding: 16px 20px 20px;
    }

    .modal-title {
      font-size: 16px;
    }
  }
</style>

<script>
  // Quick Order Modal functionality
  class QuickOrderModal {
    constructor() {
      this.modal = null;
      this.form = null;
      this.phoneInput = null;
      this.commentTextarea = null;
      this.commentCounter = null;
      this.submitBtn = null;
      this.quantitySelector = null;
      this.productData = {};

      this.init();
    }

    init() {
      // Wait for DOM to be ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.setupElements());
      } else {
        this.setupElements();
      }
    }

    setupElements() {
      this.modal = document.getElementById('quick-order-modal');
      this.form = document.getElementById('quick-order-form');
      this.phoneInput = document.getElementById('quick-order-phone');
      this.commentTextarea = document.getElementById('quick-order-comment');
      this.commentCounter = document.getElementById('quick-order-comment-counter');
      this.submitBtn = document.getElementById('quick-order-submit');

      if (!this.modal || !this.form) return;

      this.setupEventListeners();
      this.setupPhoneMask();
      this.setupCommentCounter();
    }

    setupEventListeners() {
      // Close modal events
      const closeButtons = this.modal.querySelectorAll('[data-close-modal]');
      closeButtons.forEach(btn => {
        btn.addEventListener('click', () => this.close());
      });

      // Form submission
      this.form.addEventListener('submit', (e) => this.handleSubmit(e));

      // Quantity change event
      document.addEventListener('quantitychange', (e) => {
        if (e.target.closest('#quick-order-modal')) {
          this.updateQuantityInfo(e.detail.value);
        }
      });

      // Escape key to close
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && this.isOpen()) {
          this.close();
        }
      });
    }

    setupPhoneMask() {
      if (!this.phoneInput) return;

      this.phoneInput.addEventListener('input', (e) => {
        let input = e.target.value.replace(/\D/g, '');
        let formattedInput = '';

        if (!input.startsWith('375')) {
          if (input.startsWith('8')) {
            input = input.substring(1);
          } else if (input.startsWith('0')) {
            input = input.substring(1);
          }
          formattedInput = '+375' + input;
        } else {
          formattedInput = '+375' + input.substring(3);
        }

        // Limit to 12 digits total (375 + 9 digits)
        if (formattedInput.length > 13) {
          formattedInput = formattedInput.substring(0, 13);
        }

        // Format: +375 (XX) XXX-XX-XX
        if (formattedInput.length >= 8) {
          formattedInput = formattedInput.replace(/(\+375)(\d{2})(\d{3})(\d{2})(\d{2})/, '$1 ($2) $3-$4-$5');
        } else if (formattedInput.length >= 6) {
          formattedInput = formattedInput.replace(/(\+375)(\d{2})(\d+)/, '$1 ($2) $3');
        } else if (formattedInput.length >= 4) {
          formattedInput = formattedInput.replace(/(\+375)(\d+)/, '$1 ($2');
        }

        e.target.value = formattedInput;
      });
    }

    setupCommentCounter() {
      if (!this.commentTextarea || !this.commentCounter) return;

      this.commentTextarea.addEventListener('input', (e) => {
        const length = e.target.value.length;
        this.commentCounter.textContent = length;

        if (length > 450) {
          this.commentCounter.style.color = '#dc2626';
        } else {
          this.commentCounter.style.color = '#6b7280';
        }
      });
    }

    open(productData = {}) {
      if (!this.modal) return;

      this.productData = productData;
      this.populateProductInfo();
      this.modal.style.display = 'flex';
      document.body.style.overflow = 'hidden';

      // Focus first input
      setTimeout(() => {
        const nameInput = document.getElementById('quick-order-name');
        if (nameInput) nameInput.focus();
      }, 100);
    }

    close() {
      if (!this.modal) return;

      this.modal.style.display = 'none';
      document.body.style.overflow = '';
      this.resetForm();
    }

    isOpen() {
      return this.modal && this.modal.style.display === 'flex';
    }

    populateProductInfo() {
      const nameElement = this.modal.querySelector('[data-product-name]');
      const idElement = this.modal.querySelector('[data-product-id]');
      const skuElement = this.modal.querySelector('[data-product-sku]');

      if (nameElement) nameElement.textContent = this.productData.name || '';
      if (idElement) idElement.textContent = this.productData.id || '';
      if (skuElement) skuElement.textContent = this.productData.sku || '';

      // Устанавливаем количество из productData, если есть
      const quantity = this.productData.quantity ? parseInt(this.productData.quantity) || 1 : 1;
      // Обновляем input и отображение
      const qtyInput = this.modal.querySelector('#quick-order-quantity') as HTMLInputElement | null;
      if (qtyInput) {
        qtyInput.value = String(quantity);
        // Обновляем отображение цифры, если есть
        const valueSpan = qtyInput.closest('[data-quantity-selector]')?.querySelector('.quantity-value');
        if (valueSpan) valueSpan.textContent = String(quantity);
      }
      this.updateQuantityInfo(quantity);
    }

    updateQuantityInfo(quantity) {
      const infoElement = this.modal.querySelector('[data-quantity-info]');
      if (!infoElement || !this.productData.size) return;

      // Calculate area if product has size
      if (this.productData.size && this.productData.size.length && this.productData.size.width) {
        const areaPerPiece = (this.productData.size.length * this.productData.size.width) / 1000000; // Convert mm² to m²
        const totalArea = (areaPerPiece * quantity).toFixed(2);
        infoElement.textContent = `(${quantity} шт = ${totalArea} м²)`;
      } else {
        infoElement.textContent = '';
      }
    }

    validateBelarusianPhone(phone) {
      const digitsOnly = phone.replace(/\D/g, '');

      if (digitsOnly.startsWith('375')) {
        return digitsOnly.length === 12;
      } else if (digitsOnly.length === 9) {
        const operatorCodes = ['25', '29', '33', '44'];
        return operatorCodes.some(code => digitsOnly.startsWith(code));
      }

      return false;
    }

    async handleSubmit(e) {
      e.preventDefault();

      const formData = new FormData(this.form);
      const customerName = formData.get('customerName');
      const phone = formData.get('phone');
      const comment = formData.get('comment');
      const quantity = parseInt(formData.get('quantity')) || 1;

      // Validation
      if (!customerName || customerName.trim().length < 2) {
        alert('Пожалуйста, введите корректное имя (минимум 2 символа)');
        return;
      }

      if (!phone || !this.validateBelarusianPhone(phone)) {
        alert('Пожалуйста, введите корректный белорусский номер телефона\nПример: +375 (29) 123-45-67');
        return;
      }

      // Prepare order data
      const orderData = {
        type: 'quick_order',
        product: {
          id: this.productData.id,
          name: this.productData.name,
          price: this.productData.price,
          currency: this.productData.currency,
          unit: this.productData.unit
        },
        quantity: quantity,
        customer: {
          name: customerName.trim(),
          phone: phone
        },
        comment: comment || '',
        orderDate: new Date().toISOString()
      };

      // Show loading state
      const originalText = this.submitBtn.textContent;
      this.submitBtn.disabled = true;
      this.submitBtn.textContent = 'Отправляем...';

      try {
        // Send order
        const response = await fetch('/api/orders/quick', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(orderData)
        });

        const result = await response.json();

        if (result.success) {
          alert(`Заявка успешно отправлена! Номер заявки: ${result.orderId}\nМы свяжемся с вами в ближайшее время.`);
          this.close();
        } else {
          throw new Error(result.error || 'Ошибка при отправке заявки');
        }
      } catch (error) {
        console.error('Error submitting quick order:', error);
        alert('Произошла ошибка при отправке заявки. Пожалуйста, попробуйте еще раз или свяжитесь с нами по телефону.');
      } finally {
        // Restore button state
        this.submitBtn.disabled = false;
        this.submitBtn.textContent = originalText;
      }
    }

    resetForm() {
      if (this.form) {
        this.form.reset();
        if (this.commentCounter) {
          this.commentCounter.textContent = '0';
          this.commentCounter.style.color = '#6b7280';
        }
      }
    }
  }

  // Initialize modal when DOM is ready
  document.addEventListener('DOMContentLoaded', () => {
    window.quickOrderModal = new QuickOrderModal();
  });
</script>
