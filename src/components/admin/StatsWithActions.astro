---
import Card from '../ui/Card.astro';
import Button from '../ui/Button.astro';
import Badge from '../ui/Badge.astro';

export interface StatWithAction {
  title: string;
  value: string | number;
  description?: string;
  icon: string;
  iconColor: string;
  change?: {
    value: number;
    isPositive: boolean;
  };
  action: {
    title: string;
    href: string;
    icon: string;
  };
}

export interface Props {
  stats: StatWithAction[];
  class?: string;
}

const { stats, class: className = '' } = Astro.props;
---

<div class={`grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 sm:gap-6 ${className}`}>
  {stats.map(stat => (
    <Card class="hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
      <div class="p-4 sm:p-5">
        <!-- Верхняя часть с иконкой и статистикой -->
        <div class="flex items-center space-x-3 sm:space-x-4 mb-3 sm:mb-4">
          <div class={`rounded-xl ${stat.iconColor} p-2 sm:p-3 shadow-sm flex-shrink-0`}>
            <Fragment set:html={stat.icon} />
          </div>
          <div class="flex-1 min-w-0">
            <div class="flex items-baseline space-x-2 flex-wrap">
              <span class="text-2xl sm:text-3xl font-bold text-gray-900">{stat.value}</span>
              <h3 class="text-sm sm:text-base font-medium text-gray-700">{stat.title}</h3>
              {stat.change && (
                <Badge
                  variant={stat.change.isPositive ? 'success' : 'destructive'}
                  class="text-xs"
                >
                  <svg class={`h-3 w-3 mr-1 ${stat.change.isPositive ? 'rotate-0' : 'rotate-180'}`} fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  {Math.abs(stat.change.value)}%
                </Badge>
              )}
            </div>
            {stat.description && (
              <p class="text-xs text-gray-500 mt-1 leading-relaxed">{stat.description}</p>
            )}
          </div>
        </div>

        <!-- Кнопка действия -->
        <div class="border-t border-gray-100 pt-3 -mx-4 sm:-mx-6 px-4 sm:px-6">
          <Button
            href={stat.action.href}
            variant="outline"
            class="w-full justify-center py-2 text-xs sm:text-sm font-medium hover:bg-gray-50 transition-colors"
          >
            <Fragment set:html={stat.action.icon} />
            <span class="ml-1.5 sm:ml-2">{stat.action.title}</span>
          </Button>
        </div>
      </div>
    </Card>
  ))}
</div>
