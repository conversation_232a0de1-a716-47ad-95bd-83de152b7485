---
import Card from '../ui/Card.astro';
import CardHeader from '../ui/CardHeader.astro';
import CardTitle from '../ui/CardTitle.astro';
import CardDescription from '../ui/CardDescription.astro';
import CardContent from '../ui/CardContent.astro';
import Button from '../ui/Button.astro';

export interface ActionItem {
  title: string;
  description: string;
  href: string;
  icon: string;
  iconColor: string;
}

export interface Props {
  actions: ActionItem[];
  class?: string;
}

const { actions, class: className = '' } = Astro.props;
---

<Card class={className}>
  <CardHeader>
    <CardTitle>Быстрые действия</CardTitle>
    <CardDescription>Основные операции управления</CardDescription>
  </CardHeader>
  <CardContent>
    <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
      {actions.map(action => (
        <Button 
          href={action.href} 
          variant="outline" 
          class="h-auto p-4 flex-col items-start hover:shadow-md transition-all duration-200"
        >
          <div class="flex items-center w-full mb-2">
            <div class={`flex h-8 w-8 items-center justify-center rounded-lg ${action.iconColor} mr-3 shadow-sm`}>
              <Fragment set:html={action.icon} />
            </div>
            <span class="text-sm font-medium text-left">{action.title}</span>
          </div>
          <p class="text-xs text-gray-500 text-left w-full">{action.description}</p>
        </Button>
      ))}
    </div>
  </CardContent>
</Card>
