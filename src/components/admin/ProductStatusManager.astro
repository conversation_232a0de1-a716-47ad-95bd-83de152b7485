---
import type { ProductStatus } from '../../types';

interface Props {
  currentStatus?: ProductStatus;
  productId?: string;
  disabled?: boolean;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const { 
  currentStatus = 'draft', 
  productId = '', 
  disabled = false, 
  showLabel = true,
  size = 'md',
  className = ''
} = Astro.props;

// Конфигурация статусов
const statusConfig = {
  draft: {
    label: 'Черновик',
    description: 'Товар создается, но не отображается для пользователей',
    color: 'gray',
    bgColor: 'bg-gray-100',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-300'
  },
  published: {
    label: 'Опубликован',
    description: 'Товар доступен для просмотра на сайте',
    color: 'green',
    bgColor: 'bg-green-100',
    textColor: 'text-green-800',
    borderColor: 'border-green-300'
  },
  unpublished: {
    label: 'Не опубликован',
    description: 'Товар не отображается на сайте',
    color: 'red',
    bgColor: 'bg-red-100',
    textColor: 'text-red-800',
    borderColor: 'border-red-300'
  }
};

// Размеры компонента
const sizeClasses = {
  sm: 'text-xs px-2 py-1',
  md: 'text-sm px-3 py-2',
  lg: 'text-base px-4 py-3'
};

const currentConfig = statusConfig[currentStatus];
---

<div class={`product-status-manager ${className}`}>
  {showLabel && (
    <label class="block text-sm font-medium text-gray-700 mb-2">
      Статус товара
    </label>
  )}
  
  <div class="relative">
    <!-- Текущий статус (кнопка-триггер) -->
    <button
      type="button"
      id={`status-trigger-${productId}`}
      class={`
        status-badge w-full flex items-center justify-between
        ${sizeClasses[size]}
        ${currentConfig.bgColor} ${currentConfig.textColor} ${currentConfig.borderColor}
        border rounded-lg font-medium
        hover:opacity-80 transition-opacity
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
        ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
      `}
      data-status={currentStatus}
      data-product-id={productId}
      disabled={disabled}
    >
      <div class="flex items-center">
        <span class="status-text">{currentConfig.label}</span>
      </div>
      <svg class="w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>

    <!-- Выпадающий список -->
    <div
      id={`status-dropdown-${productId}`}
      class="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 hidden"
    >
      {Object.entries(statusConfig).map(([status, config]) => (
        <button
          type="button"
          class={`
            w-full flex items-center space-x-3 px-3 py-2 text-left
            hover:bg-gray-50 transition-colors
            ${status === currentStatus ? 'bg-blue-50 border-l-4 border-blue-500' : ''}
            ${status === 'draft' ? 'rounded-t-lg' : ''}
            ${status === 'unpublished' ? 'rounded-b-lg' : ''}
          `}
          data-status={status}
          data-product-id={productId}
        >
          <div class="flex-1">
            <div class={`font-medium ${config.textColor}`}>
              {config.label}
            </div>
            <div class="text-xs text-gray-500 mt-0.5">
              {config.description}
            </div>
          </div>
          {status === currentStatus && (
            <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
          )}
        </button>
      ))}
    </div>
  </div>

  <!-- Скрытое поле для формы -->
  <input
    type="hidden"
    name="status"
    id={`status-input-${productId}`}
    value={currentStatus}
  />
</div>

<style>
  .product-status-manager {
    position: relative;
  }
  
  .product-status-manager .status-dropdown.show {
    display: block;
  }
</style>

<script>
  // Функция для обновления визуального отображения статуса
  function updateStatusDisplay(trigger, newStatus) {
    const statusConfig = {
      'draft': {
        text: 'Черновик',
        bgClass: 'bg-gray-100',
        textClass: 'text-gray-800'
      },
      'published': {
        text: 'Опубликован',
        bgClass: 'bg-green-100',
        textClass: 'text-green-800'
      },
      'unpublished': {
        text: 'Не опубликован',
        bgClass: 'bg-red-100',
        textClass: 'text-red-800'
      }
    };

    const config = statusConfig[newStatus];
    if (!config) return;

    // Обновляем текст статуса
    const statusText = trigger.querySelector('.status-text');
    if (statusText) {
      statusText.textContent = config.text;
    }

    // Обновляем классы фона и текста
    const statusBadge = trigger.querySelector('.status-badge');
    if (statusBadge) {
      // Удаляем старые классы
      statusBadge.classList.remove('bg-gray-100', 'bg-green-100', 'bg-red-100');
      statusBadge.classList.remove('text-gray-800', 'text-green-800', 'text-red-800');

      // Добавляем новые классы
      statusBadge.classList.add(config.bgClass, config.textClass);
    }


  }

  // Инициализация компонента управления статусом
  document.addEventListener('DOMContentLoaded', function() {
    // Находим все компоненты статуса на странице
    const statusManagers = document.querySelectorAll('.product-status-manager');
    
    statusManagers.forEach(manager => {
      const trigger = manager.querySelector('[id^="status-trigger-"]');
      const dropdown = manager.querySelector('[id^="status-dropdown-"]');
      const hiddenInput = manager.querySelector('[id^="status-input-"]');
      
      if (!trigger || !dropdown || !hiddenInput) return;
      
      const productId = trigger.dataset.productId;
      
      // Открытие/закрытие выпадающего списка
      trigger.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        
        if (trigger.disabled) return;
        
        // Закрываем все другие выпадающие списки
        document.querySelectorAll('[id^="status-dropdown-"]').forEach(otherDropdown => {
          if (otherDropdown !== dropdown) {
            otherDropdown.classList.add('hidden');
          }
        });
        
        // Переключаем текущий
        dropdown.classList.toggle('hidden');
        
        // Поворачиваем стрелку
        const arrow = trigger.querySelector('svg');
        if (arrow) {
          arrow.style.transform = dropdown.classList.contains('hidden') ? 'rotate(0deg)' : 'rotate(180deg)';
        }
      });
      
      // Выбор статуса
      dropdown.addEventListener('click', function(e) {
        const statusButton = e.target.closest('[data-status]');
        if (!statusButton) return;
        
        e.preventDefault();
        e.stopPropagation();
        
        const newStatus = statusButton.dataset.status;
        if (!newStatus) return;
        
        // Обновляем скрытое поле
        hiddenInput.value = newStatus;
        
        // Создаем событие изменения для уведомления других компонентов
        const changeEvent = new CustomEvent('statusChanged', {
          detail: {
            productId: productId,
            oldStatus: trigger.dataset.status,
            newStatus: newStatus
          }
        });
        
        // Обновляем данные триггера
        trigger.dataset.status = newStatus;

        // Обновляем визуальное отображение статуса
        updateStatusDisplay(trigger, newStatus);

        // Закрываем выпадающий список
        dropdown.classList.add('hidden');

        // Поворачиваем стрелку обратно
        const arrow = trigger.querySelector('svg');
        if (arrow) {
          arrow.style.transform = 'rotate(0deg)';
        }

        // Отправляем событие
        document.dispatchEvent(changeEvent);
      });
    });
    
    // Закрытие выпадающих списков при клике вне их
    document.addEventListener('click', function(e) {
      if (!e.target.closest('.product-status-manager')) {
        document.querySelectorAll('[id^="status-dropdown-"]').forEach(dropdown => {
          dropdown.classList.add('hidden');
        });
        
        // Возвращаем стрелки в исходное положение
        document.querySelectorAll('[id^="status-trigger-"] svg').forEach(arrow => {
          arrow.style.transform = 'rotate(0deg)';
        });
      }
    });
  });
</script>
