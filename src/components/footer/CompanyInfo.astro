---
interface Props {
  description?: string;
  class?: string;
}

const { description = "ИП Лисичкин", class: className } = Astro.props;
---

<div class="flex flex-col">
  <div class="mb-6">
    <!-- Placeholder for logo -->
    <div class="text-2xl font-bold text-white mb-2">ЛОГОТИП</div>
  </div>
  <div class="text-text-light text-base leading-relaxed font-barlow">
    <p>ИП Лисичкин</p>
    <p>УНП 156156156</p>
    <p>ИНН 15615615616</p>
  </div>
  <div class="text-text-light text-base leading-relaxed font-barlow mt-4">
    <p>Регистрация от 15.03.15</p>
  </div>
</div> 