---
const currentYear = new Date().getFullYear();
const footerLinks = [
  { text: "Privacy Policy", url: "#" },
  { text: "Conditions & Terms", url: "#" },
  { text: "FAQ's", url: "/faqs" }
];
---

<div class="bg-dark-footer py-6 border-t border-border-dark">
  <div class="container mx-auto px-4 flex flex-col md:flex-row justify-between items-center text-text-light text-sm font-barlow">
    <div class="mb-4 md:mb-0 text-gray-400">
      &copy; {currentYear} Aizen Architecture. All Rights Reserved.
    </div>
    <div class="flex space-x-4 md:space-x-0">
      {footerLinks.map((link, index) => (
        <a 
          href={link.url} 
          class="text-gray-400 hover:text-primary transition-colors duration-200 md:px-3"
          class:list={{'md:border-r md:border-gray-400': index < footerLinks.length - 1}}
        >
           {link.text}
        </a>
      ))}
    </div>
  </div>
</div> 