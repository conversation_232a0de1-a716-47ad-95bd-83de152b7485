---
import PageLayout from '../layouts/PageLayout.astro';
import productsData from '../../data/product/products.json';
import categoriesData from '../../data/product/categories.json';
import ProductCardGrid from '../components/products/ProductCardGrid.astro';
import ProductCardList from '../components/products/ProductCardList.astro';
import type { Product } from '../types';
import SortDropdown from '../components/products/SortDropdown.astro';
import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';

// Загружаем конфигурацию типов атрибутов
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const configPath = path.join(__dirname, '../../data/product/attribute-types-config.json');

let attributeTypesConfig = {};
try {
  const configData = await fs.readFile(configPath, 'utf-8');
  attributeTypesConfig = JSON.parse(configData);
} catch (error) {
  console.error('Ошибка загрузки конфигурации типов атрибутов:', error);
}

// Динамически загружаем категории из JSON файла, фильтруем только видимые
const visibleCategories = categoriesData.categories.filter(cat => cat.visibleInCatalog !== false);
const categories = ["All", ...visibleCategories.map(cat => cat.name)];

// Фильтруем товары, показываем только товары из видимых категорий и опубликованные товары
const visibleCategoryNames = visibleCategories.map(cat => cat.name);
const products = (productsData as Product[]).filter(product =>
  visibleCategoryNames.includes(product.category) &&
  (product.status === 'published' || (!product.status && product.inStock))
);


// Extract unique sizes and colors
const uniqueSizes = new Set<string>();
const uniqueColors = new Set<string>();
let minPrice = Infinity;
let maxPrice = 0;

products.forEach(product => {
    // Track min/max prices
    if (product.basePrice?.value && product.basePrice.value < minPrice) minPrice = product.basePrice.value;
    if (product.basePrice?.value && product.basePrice.value > maxPrice) maxPrice = product.basePrice.value;

    if (product.attributes.size) {
        if (product.attributes.size.variants && product.attributes.size.variants.length > 0) {
            product.attributes.size.variants.forEach(variant => {
                uniqueSizes.add(`${variant.length} x ${variant.width} мм`);
            });
        } else if (product.attributes.size.length) {
            uniqueSizes.add(`${product.attributes.size.length} x ${product.attributes.size.width} мм`);
        }
    }
    if (product.attributes.colors && product.attributes.colors.length > 0) {
        product.attributes.colors.forEach(color => {
            uniqueColors.add(color);
        });
    }
});

const sortedUniqueSizes = Array.from(uniqueSizes).sort();
const sortedUniqueColors = Array.from(uniqueColors).sort();

// Round min/max prices to nearest 100
minPrice = Math.floor(minPrice / 100) * 100;
maxPrice = Math.ceil(maxPrice / 100) * 100;

// Placeholder sorting and filtering logic (will be implemented in JS)
let filteredProducts = products;

declare global {
  interface Window {
    selectedSort: string;
    handleSortChange: (value: string) => void;
  }
}

---

<PageLayout
  title="Наша Продукция - Aizen Architecture"
  pageTitle="ПРОДУКЦИЯ"
  breadcrumbs={[
    { text: 'Продукция', url: '/products' }
  ]}
>

  <section class="py-16">
    <div class="container mx-auto px-4">
      <!-- Mobile Categories -->
      <div class="lg:hidden mb-4 overflow-x-auto">
        <div class="flex space-x-2 pb-2 min-w-max">
          {categories.map((category) => (
            <button
              class="category-btn px-4 py-2 text-sm whitespace-nowrap border border-gray-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary"
              data-category={category === "All" ? "" : category}
            >
              {category === "All" ? "Все" : category}
            </button>
          ))}
        </div>
      </div>

      <!-- Mobile Filter Button -->
      <div class="lg:hidden mb-6">
        <button id="filter-toggle" class="w-full px-4 py-2 bg-gray-200 text-gray-800 font-semibold rounded-none flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L11 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 016 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
          </svg>
          Фильтры
        </button>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">

        <!-- Sidebar (Filters) -->
        <aside id="product-sidebar" class="lg:col-span-1 hidden lg:block">
          <div class="bg-white p-4 lg:p-6 shadow-md rounded-none">
            <!-- Categories List (desktop only) -->
            <div class="hidden lg:block mb-6 border-b border-gray-200 pb-4">
              <h3 class="text-lg lg:text-xl font-bold mb-3">Категории</h3>
              <ul class="space-y-2">
                {categories.map((category) => (
                  <li>
                    <button
                      class="w-full text-left px-2 py-1.5 text-sm lg:text-sm xl:text-base hover:bg-gray-50 transition-colors category-sidebar-btn"
                      data-category={category === "All" ? "" : category}
                    >
                      {category === "All" ? "Все" : category}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            <!-- Filters Section -->
            <div id="filters-section">
              <h3 class="text-lg lg:text-xl font-bold mb-4 border-b border-gray-200 pb-2">Фильтры</h3>

              <!-- Price Filter -->
              <div class="mb-4 lg:mb-6">
                <h4 class="font-semibold mb-2 lg:mb-3 text-sm lg:text-base">Цена</h4>
                <div class="space-y-3 lg:space-y-4">
                  <div class="flex items-center space-x-2 lg:space-x-4">
                    <input type="number" id="min-price" class="w-full px-2 lg:px-3 py-1 lg:py-2 border rounded-none text-sm" placeholder="От" min={minPrice} max={maxPrice} value={minPrice} />
                    <input type="number" id="max-price" class="w-full px-2 lg:px-3 py-1 lg:py-2 border rounded-none text-sm" placeholder="До" min={minPrice} max={maxPrice} value={maxPrice} />
                  </div>
                  <div class="relative pt-1">
                    <input type="range" id="price-range" class="w-full" min={minPrice} max={maxPrice} step="100" value={maxPrice} />
                    <div class="flex justify-between text-xs text-gray-600 mt-1">
                      <span>{minPrice} ₽</span>
                      <span>{maxPrice} ₽</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Size Filter -->
              <div class="mb-4 lg:mb-6">
                <h4 class="font-semibold mb-2 lg:mb-3 text-sm lg:text-base">Размеры</h4>
                <ul class="space-y-1 lg:space-y-2">
                  {sortedUniqueSizes.map((size) => (
                    <li>
                      <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-primary rounded-none focus:ring-primary product-size-checkbox" value={size} />
                        <span class="ml-2 text-gray-700 text-sm lg:text-base">{size}</span>
                      </label>
                    </li>
                  ))}
                </ul>
              </div>

              <!-- Color Filter -->
              <div class="mb-4 lg:mb-6">
                <h4 class="font-semibold mb-2 lg:mb-3 text-sm lg:text-base">Цвета</h4>
                <ul class="space-y-1 lg:space-y-2">
                  {sortedUniqueColors.map((color) => (
                    <li>
                      <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-primary rounded-none focus:ring-primary product-color-checkbox" value={color} />
                        <span class="ml-2 text-gray-700 text-sm lg:text-base">{color}</span>
                      </label>
                    </li>
                  ))}
                </ul>
              </div>

            </div>
          </div>
        </aside>

        <!-- Products List Area -->
        <div class="lg:col-span-3">
          <!-- Sorting and View Options -->
          <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
              <span class="text-gray-700">Сортировка:</span>
              <SortDropdown
                options={[
                  { value: 'default', label: 'По умолчанию' },
                  { value: 'price-asc', label: 'По цене (возрастание)' },
                  { value: 'price-desc', label: 'По цене (убывание)' },
                  { value: 'name-asc', label: 'По названию (А-Я)' },
                  { value: 'name-desc', label: 'По названию (Я-А)' },
                ]}
                selected={typeof window !== 'undefined' && window.selectedSort ? window.selectedSort : 'default'}
                onChange={(value: string) => { if (typeof window !== 'undefined') window.handleSortChange(value); }}
              />
            </div>
            <div class="flex space-x-2">
              <button id="grid-view-btn" class="px-3 py-2 border rounded-none bg-gray-200 text-gray-800 focus:outline-none hover:bg-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM13 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2h-2zM13 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2z" />
                </svg>
              </button>
              <button id="list-view-btn" class="px-3 py-2 border rounded-none bg-white text-gray-700 focus:outline-none hover:bg-gray-100">
                 <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM9 15a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Products Grid/List -->
          <div id="products-list" class="lg:col-span-3 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredProducts.map((product) => {

              // Prepare size data string for data attribute
              let sizeData = '';
              if (product.attributes?.size) {
                  if (product.attributes.size.variants && product.attributes.size.variants.length > 0) {
                      sizeData = product.attributes.size.variants.map(variant => `${variant.length} x ${variant.width} мм`).join(',');
                  } else if (product.attributes.size.length) {
                       sizeData = `${product.attributes.size.length} x ${product.attributes.size.width} мм`;
                  }
              }

              // Prepare color data string for data attribute
              let colorData = '';
              if (product.attributes?.colors && product.attributes.colors.length > 0) {
                  colorData = product.attributes.colors.join(',');
              }

              return (
                <>
                   <ProductCardGrid product={product} attributeTypesConfig={attributeTypesConfig} data-size={sizeData} data-color={colorData} data-price={product.basePrice?.value || 0} data-name={product.name} />
                   <ProductCardList product={product} attributeTypesConfig={attributeTypesConfig} data-size={sizeData} data-color={colorData} data-price={product.basePrice?.value || 0} data-name={product.name} />
                </>
              );
            })}
          </div>
        </div>

      </div> {/* End Main Content Grid */}

    </div> {/* End Container */}
  </section>

</PageLayout>

<script is:inline define:vars={{ categoriesData }}>
  // Make categories data available globally for dynamic slug mapping
  window.categoriesData = categoriesData;

  const filterToggleBtn = document.getElementById('filter-toggle');
  const sidebar = document.getElementById('product-sidebar');
  const productList = document.getElementById('products-list');
  const sortSelect = document.getElementById('sort-select');
  const gridViewBtn = document.getElementById('grid-view-btn');
  const listViewBtn = document.getElementById('list-view-btn');
  const categoryCheckboxes = document.querySelectorAll('#product-sidebar input[type="checkbox"]');
  const sizeCheckboxes = document.querySelectorAll('#product-sidebar .product-size-checkbox');
  const colorCheckboxes = document.querySelectorAll('#product-sidebar .product-color-checkbox');
  const minPriceInput = document.getElementById('min-price');
  const maxPriceInput = document.getElementById('max-price');
  const priceRangeInput = document.getElementById('price-range');
  const categoryButtons = document.querySelectorAll('.category-btn');
  const categorySidebarButtons = document.querySelectorAll('.category-sidebar-btn');
  let activeCategory = '';
  let selectedSort = 'default';

  // Function to update active category state
  function updateActiveCategory(button, isSidebar = false) {
    // Remove active state from all visible buttons
    categoryButtons.forEach(btn => {
      // Only update buttons for visible categories
      const category = btn.dataset.category;
      if (category === '' || isVisibleCategory(category)) {
        btn.classList.remove('bg-primary', 'text-white', 'border-primary');
        btn.classList.add('border-gray-200', 'hover:bg-gray-100');
      }
    });
    categorySidebarButtons.forEach(btn => {
      // Only update buttons for visible categories
      const category = btn.dataset.category;
      if (category === '' || isVisibleCategory(category)) {
        btn.classList.remove('bg-primary', 'text-white');
        btn.classList.add('hover:bg-gray-50');
      }
    });

    // Add active state to clicked button
    if (isSidebar) {
      button.classList.remove('hover:bg-gray-50');
      button.classList.add('bg-primary', 'text-white');
    } else {
      button.classList.remove('border-gray-200', 'hover:bg-gray-100');
      button.classList.add('bg-primary', 'text-white', 'border-primary');
    }

    // Update active category
    activeCategory = button.dataset.category;
    filterProducts();
  }

  // Function to check if category is visible in catalog
  function isVisibleCategory(categoryName) {
    if (!categoryName) return true; // "All" category is always visible
    const categoryData = window.categoriesData?.categories?.find(cat => cat.name === categoryName);
    return !categoryData || categoryData.visibleInCatalog !== false;
  }

  // Function to hide invisible category buttons
  function hideInvisibleCategoryButtons() {
    // Hide invisible category buttons in mobile navigation
    categoryButtons.forEach(btn => {
      const category = btn.dataset.category;
      if (category && category !== '' && !isVisibleCategory(category)) {
        btn.style.display = 'none';
      }
    });

    // Hide invisible category buttons in sidebar
    categorySidebarButtons.forEach(btn => {
      const category = btn.dataset.category;
      if (category && category !== '' && !isVisibleCategory(category)) {
        btn.closest('li').style.display = 'none';
      }
    });
  }

  // Initialize active category on page load
  function initializeActiveCategory() {
    // Hide invisible categories first
    hideInvisibleCategoryButtons();

    // Find the "All" category button in both mobile and sidebar
    const mobileAllButton = Array.from(categoryButtons).find(btn => btn.dataset.category === '');
    const sidebarAllButton = Array.from(categorySidebarButtons).find(btn => btn.dataset.category === '');

    // Set active state for both buttons
    if (mobileAllButton) {
      mobileAllButton.classList.remove('border-gray-200', 'hover:bg-gray-100');
      mobileAllButton.classList.add('bg-primary', 'text-white', 'border-primary');
    }
    if (sidebarAllButton) {
      sidebarAllButton.classList.remove('hover:bg-gray-50');
      sidebarAllButton.classList.add('bg-primary', 'text-white');
    }

    // Set initial active category
    activeCategory = '';
  }

  // Add event listeners for mobile category buttons
  categoryButtons.forEach(button => {
    button.addEventListener('click', () => {
      const category = button.dataset.category;
      if (category && category !== '') {
        // Check if category is visible in catalog
        const categoryData = window.categoriesData?.categories?.find(cat => cat.name === category);
        if (categoryData && categoryData.visibleInCatalog === false) {
          // Don't allow navigation to invisible categories
          return;
        }
        // Find the category slug from the loaded categories data
        const slug = categoryData?.slug || category.toLowerCase().replace(/\s+/g, '-');
        window.location.href = `/products/${slug}`;
      } else {
        // Stay on products page for "All" category
        updateActiveCategory(button);
      }
    });
  });

  // Add event listeners for sidebar category buttons
  categorySidebarButtons.forEach(button => {
    button.addEventListener('click', () => {
      const category = button.dataset.category;
      if (category && category !== '') {
        // Check if category is visible in catalog
        const categoryData = window.categoriesData?.categories?.find(cat => cat.name === category);
        if (categoryData && categoryData.visibleInCatalog === false) {
          // Don't allow navigation to invisible categories
          return;
        }
        // Find the category slug from the loaded categories data
        const slug = categoryData?.slug || category.toLowerCase().replace(/\s+/g, '-');
        window.location.href = `/products/${slug}`;
      } else {
        // Stay on products page for "All" category
        updateActiveCategory(button, true);
      }
    });
  });

  // Function to hide invisible categories
  function hideInvisibleCategories() {
    // Hide invisible category buttons on every filter update
    hideInvisibleCategoryButtons();
  }

  // Initialize active category when DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    hideInvisibleCategories();
    initializeActiveCategory();
    // Always start with hidden sidebar on mobile/tablet
    if (window.innerWidth < 1024) {
      sidebar.classList.add('hidden');
    } else {
      sidebar.classList.remove('hidden');
    }
    handleSidebarVisibility();
  });

  // Function to handle sidebar visibility on load and resize
  const handleSidebarVisibility = () => {
    const isMobile = window.innerWidth < 1024;
    const categoriesSection = sidebar.querySelector('.hidden.lg\\:block');
    const filtersSection = document.getElementById('filters-section');

    if (isMobile) {
      // On mobile/tablet:
      // Hide the entire sidebar by default
      sidebar.classList.add('hidden');

      // Hide categories section in sidebar
      if (categoriesSection) {
        categoriesSection.classList.add('hidden');
      }

      // Show only filters section when toggled
      if (filtersSection) {
        filtersSection.classList.remove('hidden');
      }
    } else {
      // On desktop:
      // Show the sidebar
      sidebar.classList.remove('hidden');

      // Show categories section
      if (categoriesSection) {
        categoriesSection.classList.remove('hidden');
      }

      // Show filters section
      if (filtersSection) {
        filtersSection.classList.remove('hidden');
      }
    }
  };

  // Toggle sidebar visibility on button click
  if (filterToggleBtn && sidebar) {
    filterToggleBtn.addEventListener('click', () => {
      const isMobile = window.innerWidth < 1024;
      const categoriesSection = sidebar.querySelector('.hidden.lg\\:block');
      const filtersSection = document.getElementById('filters-section');

      if (isMobile) {
        // On mobile/tablet:
        // Toggle only the filters section
        if (filtersSection) {
          filtersSection.classList.remove('hidden');
        }
        if (categoriesSection) {
          categoriesSection.classList.add('hidden');
        }
      }

      // Toggle the entire sidebar
      sidebar.classList.toggle('hidden');
    });
  }

  // Update state on resize
  window.addEventListener('resize', handleSidebarVisibility);

  // --- Price Range Filter ---
  function updatePriceInputs() {
    const rangeValue = parseInt(priceRangeInput.value);
    maxPriceInput.value = rangeValue.toString();
    filterProducts();
  }

  if (priceRangeInput && minPriceInput && maxPriceInput) {
    priceRangeInput.addEventListener('input', updatePriceInputs);
    minPriceInput.addEventListener('change', filterProducts);
    maxPriceInput.addEventListener('change', filterProducts);
  }

  // --- Filtering Logic ---
  function filterProducts() {
     // Hide invisible categories on every filter update
     hideInvisibleCategories();

     const selectedSizes = Array.from(sizeCheckboxes)
         .filter(checkbox => checkbox.checked)
         .map(checkbox => checkbox.value);

     const selectedColors = Array.from(colorCheckboxes)
         .filter(checkbox => checkbox.checked)
         .map(checkbox => checkbox.value);

     const minPrice = parseInt(minPriceInput?.value || '0');
     const maxPrice = parseInt(maxPriceInput?.value || '0');

     productList?.querySelectorAll('.product-card').forEach(card => {
         const category = card.dataset.category;
         const cardSizes = card.dataset.size ? card.dataset.size.split(',') : [];
         const cardColors = card.dataset.color ? card.dataset.color.split(',') : [];
         const price = parseFloat(card.dataset.price || '0');

         // Check if category is visible in catalog
         const categoryData = window.categoriesData?.categories?.find(cat => cat.name === category);
         const categoryVisible = !categoryData || categoryData.visibleInCatalog !== false;

         // Check category filter
         const categoryMatch = !activeCategory || (category && category === activeCategory);

         // Check size filter
         const sizeMatch = selectedSizes.length === 0 || selectedSizes.some(selectedSize => cardSizes.includes(selectedSize));

         // Check color filter
         const colorMatch = selectedColors.length === 0 || selectedColors.some(selectedColor => cardColors.includes(selectedColor));

         // Check price filter
         const priceMatch = price >= minPrice && price <= maxPrice;

         // Show card only if all filters match AND category is visible
         if (categoryVisible && categoryMatch && sizeMatch && colorMatch && priceMatch) {
             card.style.display = '';
         } else {
             card.style.display = 'none';
         }
     });
  }

  // Add event listeners for all filters
  categoryCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', filterProducts);
  });

  sizeCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', filterProducts);
  });

  colorCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('change', filterProducts);
  });

  // --- Sorting Logic (Placeholder) ---
  if (sortSelect) {
    sortSelect.addEventListener('change', (event) => {
      const target = event.target;
      if (target && productList) {
        const sortBy = target.value;
        const productCards = Array.from(productList.querySelectorAll('.product-card'));

        productCards.sort((a, b) => {
          const priceA = parseFloat(a.getAttribute('data-price') || '0');
          const priceB = parseFloat(b.getAttribute('data-price') || '0');
          const nameA = a.getAttribute('data-name') || '';
          const nameB = b.getAttribute('data-name') || '';

          switch (sortBy) {
            case 'price-asc':
              return priceA - priceB;
            case 'price-desc':
              return priceB - priceA;
            case 'name-asc':
              return nameA.localeCompare(nameB);
            case 'name-desc':
              return nameB.localeCompare(nameA);
            default:
              return 0;
          }
        });

        // Reorder the cards in the DOM
        productCards.forEach(card => {
          productList.appendChild(card);
        });
      }
    });
  }

  // --- View Toggling Logic ---
  function setView(view) {
      if (!productList) return;

      const productCards = productList.querySelectorAll('.product-card');

      if (view === 'grid') {
          productList.classList.remove('flex', 'flex-col', 'space-y-4');
          productList.classList.add('grid', 'grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-3', 'xl:grid-cols-4', 'gap-4');
          gridViewBtn?.classList.add('bg-gray-200', 'text-gray-800');
          listViewBtn?.classList.remove('bg-gray-200', 'text-gray-800');
          gridViewBtn?.classList.remove('bg-white', 'text-gray-700', 'hover:bg-gray-100');
          listViewBtn?.classList.add('bg-white', 'text-gray-700', 'hover:bg-gray-100');

          productCards.forEach(card => {
              // Hide list view, show grid view
              if (card.classList.contains('list-view')) {
                  card.classList.add('hidden');
              } else {
                  card.classList.remove('hidden');
              }
          });

      } else if (view === 'list') {
          productList.classList.remove('grid', 'grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-3', 'xl:grid-cols-4', 'gap-4');
          productList.classList.add('flex', 'flex-col', 'space-y-4');
          listViewBtn?.classList.add('bg-gray-200', 'text-gray-800');
          gridViewBtn?.classList.remove('bg-gray-200', 'text-gray-800');
          listViewBtn?.classList.remove('bg-white', 'text-gray-700', 'hover:bg-gray-100');
          gridViewBtn?.classList.add('bg-white', 'text-gray-700', 'hover:bg-gray-100');

          productCards.forEach(card => {
              // Hide grid view, show list view
              if (!card.classList.contains('list-view')) {
                  card.classList.add('hidden');
              } else {
                  card.classList.remove('hidden');
              }
          });
      }
  }

  if (gridViewBtn && listViewBtn) {
      gridViewBtn.addEventListener('click', () => setView('grid'));
      listViewBtn.addEventListener('click', () => setView('list'));

      // Set initial view on load
      setView('grid'); // Default to grid view
  }

  // Initial filter application
   filterProducts();

  window.sortProducts = function(sortBy) {
    if (!productList) return;
    const productCards = Array.from(productList.querySelectorAll('.product-card'));
    productCards.sort((a, b) => {
      const priceA = parseFloat(a.getAttribute('data-price') || '0');
      const priceB = parseFloat(b.getAttribute('data-price') || '0');
      const nameA = a.getAttribute('data-name') || '';
      const nameB = b.getAttribute('data-name') || '';
      switch (sortBy) {
        case 'price-asc': return priceA - priceB;
        case 'price-desc': return priceB - priceA;
        case 'name-asc': return nameA.localeCompare(nameB);
        case 'name-desc': return nameB.localeCompare(nameA);
        default: return 0;
      }
    });
    productCards.forEach(card => productList.appendChild(card));
  };

  window.handleSortChange = function(value) {
    selectedSort = value;
    window.sortProducts(value);
  };

  document.addEventListener('DOMContentLoaded', () => {
    window.sortProducts(selectedSort);
  });

</script>

<style is:inline>
  /* Ensure product elements with 'hidden' class are display: none */
  .product-card.hidden,
  #product-sidebar.hidden,
  #mobile-menu-overlay.hidden,
  #more-menu-dropdown.hidden,
  #filters-section.hidden {
    display: none !important;
  }

  /* Ensure list view card is hidden in grid view */
  #products-list:not(.flex-col) .product-card.list-view {
    display: none !important;
  }

  /* Price range slider styles */
  input[type="range"] {
    -webkit-appearance: none;
    width: 100%;
    height: 2px;
    background: #ddd;
    border-radius: 2px;
    outline: none;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: var(--color-primary);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
  }

  /* Number input styles */
  input[type="number"] {
    -moz-appearance: textfield;
  }

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
</style>
