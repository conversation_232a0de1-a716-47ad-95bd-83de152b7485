---
import { loadPageSettings } from '../settings/utils/settingsLoader.js';
import PageLayout from '../layouts/PageLayout.astro';

const { slug } = Astro.params;
const settings = await loadPageSettings();
const pages = settings.pages || [];

// Найти страницу по slug (поиск по url.ru и url.en)
const page = pages.find(p => Object.values(p.url).includes('/' + slug));

if (!page) {
  return Astro.redirect('/404');
}

const lang = 'ru'; // TODO: определить язык пользователя/сайта
const seo = page.seo || {};
const breadcrumbs = [
  { text: 'Главная', url: '/' },
  { text: seo.title?.[lang] || page.id, url: '/' + slug }
];
---

<PageLayout title={seo.title?.[lang] || page.id} pageTitle={seo.title?.[lang] || page.id} breadcrumbs={breadcrumbs}>
  {page.blocks?.map(block => (
    block.enabled && block.type === 'text' && (
      <section>
        <h2>{block.content?.[lang]?.title}</h2>
        <div set:html={block.content?.[lang]?.body}></div>
      </section>
    )
  ))}
</PageLayout> 