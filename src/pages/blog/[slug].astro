---
import BlogLayout from '../../layouts/BlogLayout.astro';

// This would typically come from a CMS or database
const getPost = (slug: string) => {
  const posts = {
    'the-future-of-sustainable-architecture': {
      title: "The Future of Sustainable Architecture",
      content: `
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        
        <h2>The Rise of Green Building</h2>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        
        <h2>Innovative Materials</h2>
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        
        <h2>Energy Efficiency</h2>
        <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
      `,
      image: "/images/blog/post-1.jpg",
      date: "March 15, 2024",
      author: "John Smith",
      category: "Sustainability",
      tags: ["Sustainability", "Green Building", "Architecture"]
    }
  };
  
  return posts[slug as keyof typeof posts];
};

export async function getStaticPaths() {
  return [
    { params: { slug: 'the-future-of-sustainable-architecture' } }
  ];
}

const { slug } = Astro.params;
const post = getPost(slug!);

if (!post) {
  return Astro.redirect('/404');
}
---

<BlogLayout 
  title={`${post.title} - Aizen Architecture Blog`}
  pageTitle={post.title}
  breadcrumbs={[
    { text: 'БЛОГ', url: '/blog' },
    { text: post.title, url: `/blog/${slug}` } // Текущая страница
  ]}
>
  <!-- Article Header -->
  <section class="bg-gray-100 py-16">
    <div class="container">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
          <span class="text-blue-600 font-medium">{post.category}</span>
          <h1 class="text-4xl font-bold mt-4 mb-6">{post.title}</h1>
          <div class="flex items-center justify-center text-gray-600">
            <span>{post.date}</span>
            <span class="mx-2">•</span>
            <span>By {post.author}</span>
          </div>
        </div>
        <img 
          src={post.image} 
          alt={post.title}
          class="w-full h-[400px] object-cover rounded-lg shadow-md"
        >
      </div>
    </div>
  </section>

  <!-- Article Content -->
  <section class="py-16">
    <div class="container">
      <div class="max-w-4xl mx-auto">
        <article class="prose prose-lg max-w-none">
          <Fragment set:html={post.content} />
        </article>

        <!-- Tags -->
        <div class="mt-12 pt-8 border-t">
          <h3 class="text-lg font-bold mb-4">Tags</h3>
          <div class="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <a 
                href={`/blog/tag/${tag.toLowerCase()}`}
                class="px-4 py-2 bg-gray-100 rounded-full text-sm hover:bg-blue-50 hover:text-blue-600 transition-colors"
              >
                {tag}
              </a>
            ))}
          </div>
        </div>

        <!-- Share -->
        <div class="mt-8 pt-8 border-t">
          <h3 class="text-lg font-bold mb-4">Share This Post</h3>
          <div class="flex gap-4">
            <a 
              href={`https://twitter.com/intent/tweet?text=${encodeURIComponent(post.title)}&url=${encodeURIComponent(Astro.url.href)}`}
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-600 hover:text-blue-600 transition-colors"
            >
              <iconify-icon icon="mdi:twitter" class="text-2xl"></iconify-icon>
            </a>
            <a 
              href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(Astro.url.href)}`}
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-600 hover:text-blue-600 transition-colors"
            >
              <iconify-icon icon="mdi:facebook" class="text-2xl"></iconify-icon>
            </a>
            <a 
              href={`https://www.linkedin.com/shareArticle?mini=true&url=${encodeURIComponent(Astro.url.href)}&title=${encodeURIComponent(post.title)}`}
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-600 hover:text-blue-600 transition-colors"
            >
              <iconify-icon icon="mdi:linkedin" class="text-2xl"></iconify-icon>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Related Posts -->
  <section class="py-16 bg-gray-50">
    <div class="container">
      <div class="max-w-4xl mx-auto">
        <h2 class="text-3xl font-bold mb-8">Related Posts</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <article class="bg-white rounded-lg shadow-md overflow-hidden">
            <img 
              src="/images/blog/post-2.jpg" 
              alt="Modern Interior Design Trends"
              class="w-full h-48 object-cover"
            >
            <div class="p-6">
              <h3 class="text-xl font-bold mb-2">
                <a href="/blog/modern-interior-design-trends" class="hover:text-blue-600 transition-colors">
                  Modern Interior Design Trends
                </a>
              </h3>
              <p class="text-gray-600">
                Discover the latest trends in interior design that are shaping the future of living spaces.
              </p>
            </div>
          </article>
          <article class="bg-white rounded-lg shadow-md overflow-hidden">
            <img 
              src="/images/blog/post-3.jpg" 
              alt="Urban Planning in the Digital Age"
              class="w-full h-48 object-cover"
            >
            <div class="p-6">
              <h3 class="text-xl font-bold mb-2">
                <a href="/blog/urban-planning-in-the-digital-age" class="hover:text-blue-600 transition-colors">
                  Urban Planning in the Digital Age
                </a>
              </h3>
              <p class="text-gray-600">
                How technology is revolutionizing urban planning and city development.
              </p>
            </div>
          </article>
        </div>
      </div>
    </div>
  </section>
</BlogLayout> 