---
import PageLayout from '../layouts/PageLayout.astro';

const blogPosts = [
  {
    title: "The Future of Sustainable Architecture",
    excerpt: "Exploring innovative approaches to sustainable design and construction in modern architecture.",
    image: "/images/blog/post-1.jpg",
    date: "March 15, 2024",
    author: "<PERSON>",
    category: "Sustainability"
  },
  {
    title: "Modern Interior Design Trends",
    excerpt: "Discover the latest trends in interior design that are shaping the future of living spaces.",
    image: "/images/blog/post-2.jpg",
    date: "March 10, 2024",
    author: "<PERSON>",
    category: "Interior Design"
  },
  {
    title: "Urban Planning in the Digital Age",
    excerpt: "How technology is revolutionizing urban planning and city development.",
    image: "/images/blog/post-3.jpg",
    date: "March 5, 2024",
    author: "<PERSON>",
    category: "Urban Planning"
  },
  {
    title: "The Art of Architectural Photography",
    excerpt: "Tips and techniques for capturing the essence of architectural masterpieces.",
    image: "/images/blog/post-4.jpg",
    date: "February 28, 2024",
    author: "<PERSON>",
    category: "Photography"
  },
  {
    title: "Smart Home Integration in Modern Architecture",
    excerpt: "How smart technology is being seamlessly integrated into contemporary architectural designs.",
    image: "/images/blog/post-5.jpg",
    date: "February 20, 2024",
    author: "<PERSON> <PERSON>",
    category: "Technology"
  },
  {
    title: "Preserving Historical Architecture",
    excerpt: "The importance and challenges of preserving historical buildings in modern cities.",
    image: "/images/blog/post-6.jpg",
    date: "February 15, 2024",
    author: "<PERSON>",
    category: "Heritage"
  }
];

const categories = ["All", "Sustainability", "Interior Design", "Urban Planning", "Photography", "Technology", "Heritage"];
---

<PageLayout 
  title="Blog - Aizen Architecture"
  pageTitle="БЛОГ"
  breadcrumbs={[
    { text: 'Блог', url: '/blog' }
  ]}
>

  <!-- Blog Content -->
  <section class="py-16">
    <div class="container">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Categories -->
          <div class="bg-white p-6 rounded-lg shadow-md mb-8">
            <h3 class="text-xl font-bold mb-4">Categories</h3>
            <ul class="space-y-2">
              {categories.map((category) => (
                <li>
                  <button 
                    class="w-full text-left px-4 py-2 rounded-md hover:bg-blue-50 hover:text-blue-600 transition-colors"
                    data-category={category === "All" ? "" : category}
                  >
                    {category}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          <!-- Recent Posts -->
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-bold mb-4">Recent Posts</h3>
            <ul class="space-y-4">
              {blogPosts.slice(0, 3).map((post) => (
                <li class="flex items-start space-x-4">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    class="w-20 h-20 object-cover rounded-md"
                  >
                  <div>
                    <h4 class="font-medium hover:text-blue-600">
                      <a href={`/blog/${post.title.toLowerCase().replace(/\s+/g, '-')}`}>
                        {post.title}
                      </a>
                    </h4>
                    <p class="text-sm text-gray-600">{post.date}</p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>

        <!-- Blog Posts -->
        <div class="lg:col-span-3">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            {blogPosts.map((post) => (
              <article class="bg-white rounded-lg shadow-md overflow-hidden" data-category={post.category}>
                <img 
                  src={post.image} 
                  alt={post.title}
                  class="w-full h-48 object-cover"
                >
                <div class="p-6">
                  <div class="flex items-center text-sm text-gray-600 mb-2">
                    <span>{post.date}</span>
                    <span class="mx-2">•</span>
                    <span>{post.author}</span>
                  </div>
                  <h2 class="text-xl font-bold mb-2">
                    <a 
                      href={`/blog/${post.title.toLowerCase().replace(/\s+/g, '-')}`}
                      class="hover:text-blue-600 transition-colors"
                    >
                      {post.title}
                    </a>
                  </h2>
                  <p class="text-gray-600 mb-4">
                    {post.excerpt}
                  </p>
                  <a 
                    href={`/blog/${post.title.toLowerCase().replace(/\s+/g, '-')}`}
                    class="text-blue-600 font-medium hover:text-blue-700"
                  >
                    Read More →
                  </a>
                </div>
              </article>
            ))}
          </div>

          <!-- Pagination -->
          <div class="mt-12 flex justify-center">
            <nav class="flex items-center space-x-2">
              <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-blue-50 hover:text-blue-600 transition-colors">
                Previous
              </button>
              <button class="px-4 py-2 bg-blue-600 text-white rounded-md">
                1
              </button>
              <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-blue-50 hover:text-blue-600 transition-colors">
                2
              </button>
              <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-blue-50 hover:text-blue-600 transition-colors">
                3
              </button>
              <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-blue-50 hover:text-blue-600 transition-colors">
                Next
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </section>
</PageLayout>

<script>
  // Blog post filtering
  const categoryButtons = document.querySelectorAll<HTMLButtonElement>('[data-category]');
  const blogPosts = document.querySelectorAll<HTMLElement>('[data-category]');

  categoryButtons.forEach(button => {
    button.addEventListener('click', () => {
      const category = button.dataset.category;
      
      // Update active button
      categoryButtons.forEach(btn => {
        btn.classList.remove('bg-blue-50', 'text-blue-600');
        if (btn === button) {
          btn.classList.add('bg-blue-50', 'text-blue-600');
        }
      });
      
      // Filter blog posts
      blogPosts.forEach(post => {
        if (category === '' || post.dataset.category === category) {
          post.style.display = 'block';
        } else {
          post.style.display = 'none';
        }
      });
    });
  });
</script> 