---
import PageLayout from '../../layouts/PageLayout.astro';

// This would typically come from a CMS or database
const getService = (slug: string) => {
  const services = {
    'architecture-design': {
      title: "Architecture Design",
      description: "Creating innovative and sustainable architectural solutions that transform spaces and enhance lives.",
      content: `
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        
        <h2>Our Approach</h2>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
        
        <h2>Design Process</h2>
        <p>Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.</p>
        
        <h2>Why Choose Us</h2>
        <p>Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt.</p>
      `,
      image: "/images/services/service-1.jpg",
      features: [
        {
          title: "Innovative Design",
          description: "Creating unique and forward-thinking architectural solutions."
        },
        {
          title: "Sustainable Approach",
          description: "Incorporating eco-friendly practices and materials."
        },
        {
          title: "Client Collaboration",
          description: "Working closely with clients to understand their vision."
        },
        {
          title: "Technical Excellence",
          description: "Utilizing cutting-edge technology and best practices."
        }
      ],
      process: [
        {
          title: "Initial Consultation",
          description: "Understanding your needs and vision"
        },
        {
          title: "Concept Development",
          description: "Creating preliminary designs and concepts"
        },
        {
          title: "Design Refinement",
          description: "Refining and perfecting the design"
        },
        {
          title: "Final Delivery",
          description: "Completing the project to your satisfaction"
        }
      ]
    }
  };
  
  return services[slug as keyof typeof services];
};

export async function getStaticPaths() {
  return [
    { params: { slug: 'architecture-design' } }
  ];
}

const { slug } = Astro.params;
const service = getService(slug!);

if (!service) {
  return Astro.redirect('/404');
}
---

<PageLayout 
  title={`${service.title} - Aizen Architecture Services`}
  pageTitle={service.title}
  breadcrumbs={[
    { text: 'УСЛУГИ', url: '/services' },
    { text: service.title, url: `/services/${slug}` } // Текущая страница
  ]}
>
  <!-- Service Header -->
  <section class="bg-gray-100 py-16">
    <div class="container">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
          <h1 class="text-4xl font-bold mb-6">{service.title}</h1>
          <p class="text-gray-600 text-lg">
            {service.description}
          </p>
        </div>
        <img 
          src={service.image} 
          alt={service.title}
          class="w-full h-[400px] object-cover rounded-lg shadow-md"
        >
      </div>
    </div>
  </section>

  <!-- Service Content -->
  <section class="py-16">
    <div class="container">
      <div class="max-w-4xl mx-auto">
        <article class="prose prose-lg max-w-none mb-16">
          <Fragment set:html={service.content} />
        </article>

        <!-- Features -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {service.features.map((feature) => (
            <div class="bg-white p-6 rounded-lg shadow-md">
              <h3 class="text-xl font-bold mb-4">{feature.title}</h3>
              <p class="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>

        <!-- Process -->
        <div class="bg-gray-50 p-8 rounded-lg">
          <h2 class="text-3xl font-bold mb-8 text-center">Our Process</h2>
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            {service.process.map((step, index) => (
              <div class="text-center">
                <div class="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {index + 1}
                </div>
                <h3 class="text-lg font-bold mb-2">{step.title}</h3>
                <p class="text-gray-600">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Call to Action -->
  <section class="py-16 bg-blue-600 text-white">
    <div class="container">
      <div class="max-w-4xl mx-auto text-center">
        <h2 class="text-3xl font-bold mb-4">Ready to Start Your Project?</h2>
        <p class="text-lg mb-8">
          Let's work together to create something extraordinary. Contact us today to discuss your vision.
        </p>
        <a href="/contact" class="btn-secondary bg-white text-blue-600 hover:bg-gray-100">
          Get in Touch
        </a>
      </div>
    </div>
  </section>
</PageLayout> 