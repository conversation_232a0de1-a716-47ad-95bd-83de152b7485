---
import PageLayout from '../../layouts/PageLayout.astro';
import ProductCardGrid from '../../components/products/ProductCardGrid.astro';

import type { Product } from '../../types';
import SortDropdown from '../../components/products/SortDropdown.astro';
import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

interface Category {
  id: string;
  name: string;
  slug: string;
  subcategories: string[];
  visibleInCatalog?: boolean;
  activeForProducts?: boolean;
}

declare global {
  interface Window {
    selectedSort: string;
    handleSortChange: (value: string) => void;
  }
}

// Получаем slug категории из URL
const categorySlug = Astro.params.category;

// Загружаем данные динамически
let categories: { categories: Category[] };
let productsData: Product[];
let category: Category | undefined;
let attributeTypesConfig = {};

try {
  const categoriesPath = path.join(__dirname, '../../../data/product/categories.json');
  const productsPath = path.join(__dirname, '../../../data/product/products.json');
  const configPath = path.join(__dirname, '../../../data/product/attribute-types-config.json');

  const categoriesFile = await fs.readFile(categoriesPath, 'utf-8');
  const productsFile = await fs.readFile(productsPath, 'utf-8');
  const configFile = await fs.readFile(configPath, 'utf-8');

  categories = JSON.parse(categoriesFile);
  productsData = JSON.parse(productsFile);
  attributeTypesConfig = JSON.parse(configFile);

  // Находим категорию по slug
  category = categories.categories.find(cat => cat.slug === categorySlug);
} catch (error) {
  console.error('Ошибка загрузки данных:', error);
  return Astro.redirect('/products');
}

// Проверяем, что категория существует и видима в каталоге
if (!category || category.visibleInCatalog === false) {
  return Astro.redirect('/products');
}

// Get products for this category, only published products
const categoryProducts = productsData.filter(product =>
  product.categorySlug === category.slug &&
  (product.status === 'published' || (!product.status && product.inStock))
);

// Extract unique attributes for filters
const filterData = {
  sizes: [...new Set(categoryProducts.flatMap(product => {
    if (product.attributes.size && Array.isArray(product.attributes.size.variants) && product.attributes.size.variants.length > 0) {
      return product.attributes.size.variants.map(v => `${v.length}x${v.width} мм`);
    }
    if (product.attributes.size && typeof product.attributes.size.length === 'number' && typeof product.attributes.size.width === 'number') {
      return [`${product.attributes.size.length}x${product.attributes.size.width} мм`];
    }
    return [];
  }))],
  colors: [...new Set(categoryProducts.flatMap(product =>
    product.attributes.colors || []
  ))],
  minPrice: Math.min(...categoryProducts.map(p => p.basePrice?.value || 0)),
  maxPrice: Math.max(...categoryProducts.map(p => p.basePrice?.value || 0))
};
---

<PageLayout
  title={`${category.name} - Aizen Architecture`}
  pageTitle={category.name.toUpperCase()}
  breadcrumbs={[
    { text: 'Продукция', url: '/products' },
    { text: category.name, url: `/products/${category.slug}` }
  ]}
>
  <section class="py-16">
    <div class="container mx-auto px-4">

      <!-- Mobile Categories Navigation -->
      <div class="lg:hidden mb-4 overflow-x-auto">
        <div class="flex space-x-2 pb-2 min-w-max">
          <a href="/products" class="px-4 py-2 text-sm whitespace-nowrap border border-gray-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary">
            Все
          </a>
          {categories.categories.filter(cat => cat.visibleInCatalog !== false).map((cat) => (
            <a
              href={`/products/${cat.slug}`}
              class={`px-4 py-2 text-sm whitespace-nowrap border focus:outline-none focus:ring-2 focus:ring-primary ${
                cat.slug === category.slug
                  ? 'bg-primary text-white border-primary'
                  : 'border-gray-200 hover:bg-gray-100'
              }`}
            >
              {cat.name}
            </a>
          ))}
        </div>
      </div>

      <!-- Mobile Filter Toggle -->
      <div class="lg:hidden mb-6">
        <button id="filter-toggle" class="w-full px-4 py-2 bg-gray-200 text-gray-800 font-semibold rounded-none flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L11 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 016 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd" />
          </svg>
          Фильтры
        </button>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <!-- Sidebar (Filters) -->
        <aside id="product-sidebar" class="lg:col-span-1 hidden lg:block">
          <div class="bg-white p-4 lg:p-6 shadow-md rounded-none">

            <!-- Categories Navigation -->
            <div class="mb-6 border-b border-gray-200 pb-4">
              <h3 class="text-lg lg:text-xl font-bold mb-3">Категории</h3>
              <ul class="space-y-2">
                <li>
                  <a href="/products" class="block w-full text-left px-2 py-1.5 text-sm lg:text-sm xl:text-base hover:bg-gray-50 transition-colors">
                    Все категории
                  </a>
                </li>
                {categories.categories.filter(cat => cat.visibleInCatalog !== false).map((cat) => (
                  <li>
                    <a
                      href={`/products/${cat.slug}`}
                      class={`block w-full text-left px-2 py-1.5 text-sm lg:text-sm xl:text-base transition-colors ${
                        cat.slug === category.slug
                          ? 'bg-primary text-white'
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      {cat.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            <!-- Filters Section -->
            <div id="filters-section">
              <h3 class="text-lg lg:text-xl font-bold mb-4 border-b border-gray-200 pb-2">Фильтры</h3>

              <!-- Price Filter -->
              <div class="mb-4 lg:mb-6">
                <h4 class="font-semibold mb-2 lg:mb-3 text-sm lg:text-base">Цена</h4>
                <div class="space-y-3 lg:space-y-4">
                  <div class="flex items-center space-x-2 lg:space-x-4">
                    <input type="number" id="min-price" class="w-full px-2 lg:px-3 py-1 lg:py-2 border rounded-none text-sm" placeholder="От" min={filterData.minPrice} max={filterData.maxPrice} value={filterData.minPrice} />
                    <input type="number" id="max-price" class="w-full px-2 lg:px-3 py-1 lg:py-2 border rounded-none text-sm" placeholder="До" min={filterData.minPrice} max={filterData.maxPrice} value={filterData.maxPrice} />
                  </div>
                  <div class="relative pt-1">
                    <input type="range" id="price-range" class="w-full" min={filterData.minPrice} max={filterData.maxPrice} step="100" value={filterData.maxPrice} />
                    <div class="flex justify-between text-xs text-gray-600 mt-1">
                      <span>{filterData.minPrice} ₽</span>
                      <span>{filterData.maxPrice} ₽</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Size Filter -->
              <div class="mb-4 lg:mb-6">
                <h4 class="font-semibold mb-2 lg:mb-3 text-sm lg:text-base">Размеры</h4>
                <ul class="space-y-1 lg:space-y-2">
                  {filterData.sizes.map((size) => (
                    <li>
                      <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-primary rounded-none focus:ring-primary product-size-checkbox" value={size} />
                        <span class="ml-2 text-gray-700 text-sm lg:text-base">{size}</span>
                      </label>
                    </li>
                  ))}
                </ul>
              </div>

              <!-- Color Filter -->
              <div class="mb-4 lg:mb-6">
                <h4 class="font-semibold mb-2 lg:mb-3 text-sm lg:text-base">Цвета</h4>
                <ul class="space-y-1 lg:space-y-2">
                  {filterData.colors.map((color) => (
                    <li>
                      <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-primary rounded-none focus:ring-primary product-color-checkbox" value={color} />
                        <span class="ml-2 text-gray-700 text-sm lg:text-base">{color}</span>
                      </label>
                    </li>
                  ))}
                </ul>
              </div>

            </div>

            <!-- Features Section -->
            <div class="mb-6">
              <h3 class="text-lg lg:text-xl font-bold mb-4 border-b border-gray-200 pb-2">Преимущества</h3>
              <div class="space-y-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-[#baa385]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm lg:text-base font-semibold">Высокое качество</h4>
                    <p class="text-gray-600 text-sm">Строгий контроль на всех этапах производства</p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-[#baa385]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm lg:text-base font-semibold">Быстрая доставка</h4>
                    <p class="text-gray-600 text-sm">Оперативная доставка по всей России</p>
                  </div>
                </div>
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="w-6 h-6 text-[#baa385]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm lg:text-base font-semibold">Гарантия качества</h4>
                    <p class="text-gray-600 text-sm">Гарантийный срок на всю продукцию</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Contact CTA -->
            <div class="bg-[#baa385] text-white p-4 rounded-none">
              <h3 class="text-lg font-bold mb-2">Нужна консультация?</h3>
              <p class="text-sm mb-4">Наши специалисты помогут подобрать оптимальное решение</p>
              <a
                href="/contact"
                class="block w-full bg-white text-[#baa385] text-center py-2 font-semibold hover:bg-gray-100 transition-colors"
              >
                Связаться с нами
              </a>
            </div>
          </div>
        </aside>

        <!-- Products List Area -->
        <div class="lg:col-span-3">

        <!-- Inline Subcategories -->
        <div class="mb-8 overflow-x-auto">
          <div class="flex space-x-3 pb-2 min-w-max">
            <button
              class="px-4 py-2 text-sm whitespace-nowrap border rounded-none font-semibold category-btn active"
              data-subcategory="all"
            >
              Все
            </button>
            {category.subcategories.map((subcategory) => (
              <button
                class="px-4 py-2 text-sm whitespace-nowrap border border-gray-200 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary font-semibold category-btn"
                data-subcategory={subcategory.toLowerCase().replace(/\s+/g, '-')}
              >
                {subcategory}
              </button>
            ))}
          </div>
        </div>


          <!-- Sorting and View Options -->
          <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
              <span class="text-gray-700">Сортировка:</span>
              <SortDropdown
                options={[
                  { value: 'default', label: 'По умолчанию' },
                  { value: 'price-asc', label: 'По цене (возрастание)' },
                  { value: 'price-desc', label: 'По цене (убывание)' },
                  { value: 'name-asc', label: 'По названию (А-Я)' },
                  { value: 'name-desc', label: 'По названию (Я-А)' },
                ]}
                selected={typeof window !== 'undefined' && window.selectedSort ? window.selectedSort : 'default'}
                onChange={(value: string) => { if (typeof window !== 'undefined') window.handleSortChange(value); }}
              />
            </div>
            <div class="flex space-x-2">
              <button id="grid-view-btn" class="px-3 py-2 border rounded-none bg-gray-200 text-gray-800 focus:outline-none hover:bg-gray-300">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM13 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2h-2zM13 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2z" />
                </svg>
              </button>
              <button id="list-view-btn" class="px-3 py-2 border rounded-none bg-white text-gray-700 focus:outline-none hover:bg-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM9 15a1 1 0 011-1h6a1 1 0 110 2h-6a1 1 0 01-1-1z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Products Grid/List -->
          <div id="products-list" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {categoryProducts.map((product) => (
              <>
                <ProductCardGrid product={product} attributeTypesConfig={attributeTypesConfig} />
              </>
            ))}
          </div>
        </div>
      </div>
    </div>
  </section>
</PageLayout>

<script is:inline lang="ts">
  const filterToggleBtn = document.getElementById('filter-toggle');
  const sidebar = document.getElementById('product-sidebar');
  const productList = document.getElementById('products-list');
  const gridViewBtn = document.getElementById('grid-view-btn');
  const listViewBtn = document.getElementById('list-view-btn');
  const categoryButtons = document.querySelectorAll('.category-btn');

  // Toggle sidebar on mobile
  filterToggleBtn?.addEventListener('click', () => {
    sidebar?.classList.toggle('hidden');
  });

  // Toggle view between grid and list
  gridViewBtn?.addEventListener('click', () => {
    productList?.classList.remove('grid-cols-1');
    productList?.classList.add('grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-3', 'xl:grid-cols-4');
    gridViewBtn.classList.add('bg-gray-200', 'text-gray-800');
    gridViewBtn.classList.remove('bg-white', 'text-gray-700');
    listViewBtn?.classList.remove('bg-gray-200', 'text-gray-800');
    listViewBtn?.classList.add('bg-white', 'text-gray-700');
  });

  listViewBtn?.addEventListener('click', () => {
    productList?.classList.remove('grid-cols-2', 'md:grid-cols-3', 'lg:grid-cols-3', 'xl:grid-cols-4');
    productList?.classList.add('grid-cols-1');
    listViewBtn.classList.add('bg-gray-200', 'text-gray-800');
    listViewBtn.classList.remove('bg-white', 'text-gray-700');
    gridViewBtn?.classList.remove('bg-gray-200', 'text-gray-800');
    gridViewBtn?.classList.add('bg-white', 'text-gray-700');
  });

  // Handle subcategory filtering
  categoryButtons.forEach(button => {
    button.addEventListener('click', () => {
      console.log('Subcategory button clicked');
      const selectedSubcategory = button.getAttribute('data-subcategory');
      console.log('Selected subcategory:', selectedSubcategory);

      // Update active button class
      categoryButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // Filter products
      if (productList) {
        console.log('Filtering products...');
        Array.from(productList.children).forEach(card => {
          const productCard = card;
          const productSubcategory = productCard.getAttribute('data-subcategory');
          console.log('Product card subcategory:', productSubcategory);

          if (selectedSubcategory === 'all' || productSubcategory === selectedSubcategory) {
            productCard.style.display = ''; // Show the product card
          } else {
            productCard.style.display = 'none'; // Hide the product card
          }
        });
        console.log('Filtering complete.');
      }
    });
  });

  let selectedSort = 'default';
  window.sortProducts = function(sortBy) {
    if (!productList) return;
    const productCards = Array.from(productList.querySelectorAll('.product-card'));
    productCards.sort((a, b) => {
      const priceA = parseFloat(a.getAttribute('data-price') || '0');
      const priceB = parseFloat(b.getAttribute('data-price') || '0');
      const nameA = a.getAttribute('data-name') || '';
      const nameB = b.getAttribute('data-name') || '';
      switch (sortBy) {
        case 'price-asc': return priceA - priceB;
        case 'price-desc': return priceB - priceA;
        case 'name-asc': return nameA.localeCompare(nameB);
        case 'name-desc': return nameB.localeCompare(nameA);
        default: return 0;
      }
    });
    productCards.forEach(card => productList.appendChild(card));
  };
  window.handleSortChange = function(value) {
    selectedSort = value;
    window.sortProducts(value);
  };
  document.addEventListener('DOMContentLoaded', () => {
    window.sortProducts(selectedSort);
  });
</script>

<style>
  .category-btn.active {
    @apply bg-[#baa385] text-white border-[#baa385];
  }
</style>
