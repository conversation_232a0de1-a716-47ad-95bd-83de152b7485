---
import AdminLayout from '@layouts/AdminLayout.astro';

const { id } = Astro.params;
const blockId = new URL(Astro.request.url).searchParams.get('blockId');
---

<AdminLayout title={`Редактировать блок: ${blockId} (страница: ${id})`}>
  <form action="/api/admin/save-block" method="POST" style="max-width:700px;margin:2rem auto;">
    <input type="hidden" name="id" value={id} />
    <input type="hidden" name="blockId" value={blockId} />
    <div style="margin-bottom:1rem;">
      <label>Тип блока:
        <select name="type">
          <option value="text">Текстовый</option>
        </select>
      </label>
    </div>
    <div style="margin-bottom:1rem;">
      <label>Порядок:
        <input name="order" type="number" value="1" min="1" required />
      </label>
    </div>
    <div style="margin-bottom:1rem;">
      <label>
        <input type="checkbox" name="enabled" checked /> Включён
      </label>
    </div>
    <div style="margin-bottom:1rem;">
      <label>Язык:
        <select name="lang">
          <option value="ru">Русский</option>
          <option value="en">English</option>
        </select>
      </label>
    </div>
    <div style="margin-bottom:1rem;">
      <label>Контент (TinyMCE):
        <textarea name="content" rows="10" style="width:100%"></textarea>
      </label>
    </div>
    <button type="submit" class="btn btn-primary">Сохранить</button>
    <a href={`/admin/settings/pages/edit/${id}`} class="btn" style="margin-left:1rem;">Отмена</a>
  </form>
  <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
  <script src="/js/tinymce-init.js"></script>
  <style>
    .btn { background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; padding: 0.25rem 0.75rem; color: #6b7280; cursor: pointer; text-decoration: none; }
    .btn-primary { background: #2563eb; color: #fff; border-color: #2563eb; }
  </style>
</AdminLayout>