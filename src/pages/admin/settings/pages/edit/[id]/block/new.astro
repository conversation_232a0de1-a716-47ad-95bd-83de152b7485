---
import AdminLayout from '@layouts/AdminLayout.astro';

const { id } = Astro.params;
---

<AdminLayout title={`Добавить блок на страницу: ${id}`}>
  <div class="max-w-7xl w-full mx-auto bg-white rounded-lg shadow p-8 mt-8">
    <h1 class="text-2xl font-bold text-gray-900 mb-2">Добавить блок</h1>
    <p class="text-gray-600 mb-6">Заполните параметры блока и добавьте контент. После сохранения блок появится в списке на странице.</p>
    <form action="/api/admin/save-block" method="POST" class="space-y-6" onsubmit="document.getElementById('content-json').value = window.editorjsLastData || ''">
      <input type="hidden" name="id" value={id} />
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Тип блока:</label>
          <select name="type" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="text">Текстовый</option>
          </select>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">ID блока:</label>
          <input name="blockId" value={Math.random().toString(36).slice(2,10)} required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Порядок:</label>
          <input name="order" type="number" value="1" min="1" required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
        </div>
        <div class="flex items-center mt-6">
          <input type="checkbox" name="enabled" checked class="mr-2 rounded border-gray-300 focus:ring-blue-500" />
          <label class="text-sm text-gray-700">Включён</label>
        </div>
        <div>
          <label class="block mb-1 text-sm font-medium text-gray-700">Язык:</label>
          <select name="lang" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
            <option value="ru">Русский</option>
            <option value="en">English</option>
          </select>
        </div>
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Контент (Editor.js):</label>
        <div id="editorjs" class="border border-gray-200 rounded-lg bg-white"></div>
        <input type="hidden" name="content" id="content-json" />
      </div>
      <div class="flex gap-4 mt-8">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">Сохранить</button>
        <a href={`/admin/settings/pages/edit/${id}`} class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">Отмена</a>
      </div>
    </form>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/editorjs@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/header@latest"></script>
  <script src="https://cdn.jsdelivr.net/npm/@editorjs/list@latest"></script>
  <script is:inline>
    function initEditor() {
      if (document.getElementById('editorjs')) {
        const editor = new EditorJS({
          holder: 'editorjs',
          tools: {
            header: window.Header,
            list: window.List,
          },
          onChange: async () => {
            const output = await editor.save();
            window.editorjsLastData = JSON.stringify(output);
            var hidden = document.getElementById('content-json');
            if (hidden) hidden.value = window.editorjsLastData;
          },
        });
        window.editorjs = editor;
      }
    }

    function waitForEditorTools() {
      if (window.EditorJS && window.Header && window.List) {
        initEditor();
      } else {
        setTimeout(waitForEditorTools, 100);
      }
    }

    waitForEditorTools();
  </script>
</AdminLayout>