---
import AdminLayout from '../../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../../utils/auth';
import ProductStatusManager from '../../../../components/admin/ProductStatusManager.astro';
import ProductVariants from '../../../../components/admin/ProductVariants.astro';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Получение ID товара из параметров URL
const { id } = Astro.params;

// Загрузка данных о товарах
import productsData from '../../../../../data/product/products.json';

// Загрузка данных об атрибутах
import attributesFile from '../../../../../data/product/attributes.json';
import attributeTypesConfig from '../../../../../data/product/attribute-types-config.json';
const attributesData = attributesFile;
const attributeTypes = attributeTypesConfig;
const attributeTypesFullConfig = attributeTypesConfig;

// Загрузка настроек товаров
import settingsData from '../../../../../data/product/settings-product.json';

// Поиск товара по ID
const product = productsData.find(p => p.id === id);

// Если товар не найден, перенаправляем на список товаров
if (!product) {
  return Astro.redirect('/admin/products');
}
---

<AdminLayout title={`Редактирование товара ${product.name} | LuxBeton`}>
  <div class="container mx-auto py-8 px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Редактирование товара</h1>
      <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400 inline-flex items-center">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
        </svg>
        Назад к списку товаров
      </a>
    </div>

    <form id="product-form" class="bg-white rounded-lg shadow-md p-6">
      <input type="hidden" id="product-id" value={product.id}>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Основная информация -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Основная информация</h2>

          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Название товара</label>
            <input
              type="text"
              id="name"
              value={product.name}
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              required
            >
          </div>

          <div class="mb-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="sku" class="block text-sm font-medium text-gray-700 mb-1">SKU</label>
                <input
                  type="text"
                  id="sku"
                  value={product.sku}
                  class="w-full px-3 py-2 border border-gray-300 rounded-md"
                  required
                >
                <p class="text-xs text-gray-500 mt-1">SKU сгенерирован автоматически на основе ID, но вы можете изменить его вручную. SKU должен быть уникальным.</p>
              </div>
              <div>
                <label for="product-slug" class="block text-sm font-medium text-gray-700 mb-1">SLUG (URL товара)</label>
                <div class="flex items-center space-x-2">
                  <input
                    type="text"
                    id="product-slug"
                    value={product.slug || ''}
                    placeholder="url-slug-tovara"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                  >
                  <button
                    type="button"
                    id="generate-product-slug-btn"
                    class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 border border-blue-300 rounded-md transition-colors"
                    title="Генерировать SLUG из названия товара с транслитерацией"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                  </button>
                </div>
                <p class="mt-1 text-xs text-gray-500">Автоматически генерируется из названия или нажмите кнопку для ручной генерации</p>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <div class="relative">
              <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
                Категория
                <svg
                  class="w-4 h-4 text-yellow-600 cursor-help inline-block ml-2 category-info-icon"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </label>
              <input
                type="text"
                id="category"
                value={product.category}
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed category-input"
                readonly
                disabled
              >
              <!-- Tooltip -->
              <div
                id="category-warning-tooltip"
                class="category-tooltip absolute bottom-full left-0 mb-2 px-3 py-2 bg-yellow-50 border border-yellow-200 rounded shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 z-10 w-80"
              >
                <div class="text-sm font-medium text-yellow-800 mb-1">Изменение категории недоступно</div>
                <div class="text-xs text-yellow-700">Категория влияет на ID товара. Для изменения категории создайте новый товар в нужной категории.</div>
                <!-- Стрелка tooltip'а -->
                <div class="absolute top-full left-4 border-4 border-transparent border-t-yellow-200"></div>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <label for="subcategory" class="block text-sm font-medium text-gray-700 mb-1">Подкатегория</label>
            <select
              id="subcategory"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Загрузка подкатегорий...</option>
            </select>
          </div>

          <div class="mb-4">
            <label for="shortDescription" class="block text-sm font-medium text-gray-700 mb-1">Краткое описание</label>
            <textarea
              id="shortDescription"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              rows="2"
              required
            >{product.shortDescription}</textarea>
          </div>

          <div class="mb-4">
            <label for="fullDescription" class="block text-sm font-medium text-gray-700 mb-1">Полное описание</label>
            <textarea
              id="fullDescription"
              class="w-full px-3 py-2 border border-gray-300 rounded-md"
              rows="5"
            >{product.fullDescription}</textarea>
          </div>
        </div>

        <!-- Цена и атрибуты -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Цена и атрибуты</h2>

          <!-- Тип товара (только для просмотра) -->
          <div class="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <label class="block text-sm font-medium text-gray-700 mb-1">
              <svg class="w-4 h-4 inline mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              Тип товара
            </label>
            <input
              type="text"
              id="product-type-display"
              value={product.productType || 'physical'}
              class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 cursor-not-allowed"
              readonly
              disabled
            />
            <input type="hidden" id="product-type" value={product.productType || 'physical'} />
            <p class="text-xs text-gray-500 mt-1">
              <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              Тип товара нельзя изменить после создания
            </p>
          </div>



          <!-- Компонент выбора атрибутов -->
          <div id="attributes-section" class="mb-4">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Атрибуты товара</h3>
            <div id="attributes-container" class="space-y-4">
              <!-- Атрибуты будут добавлены динамически -->
            </div>
            <button
              type="button"
              id="add-attribute-btn"
              class="mt-3 inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Добавить атрибут
            </button>
          </div>

          <!-- Варианты товара -->
          <ProductVariants
            settingsData={settingsData}
            attributesData={attributesData}
            attributeTypes={attributeTypes}
          />

          <!-- Цена товара для покупателя -->
          <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              Цена товара
              <!-- Иконка с всплывающей подсказкой -->
              <div class="relative ml-2">
                <svg class="w-4 h-4 text-gray-400 hover:text-gray-600 cursor-help" fill="currentColor" viewBox="0 0 20 20" id="price-info-icon">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <!-- Всплывающая подсказка -->
                <div id="price-tooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 whitespace-nowrap z-10">
                  <div class="text-center">
                    <div class="font-semibold text-blue-300">Базовая цена отключена</div>
                    <div class="mt-1">Цены указаны в вариантах товара. Базовая цена не требуется.</div>
                  </div>
                  <!-- Стрелка -->
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                </div>
              </div>
            </h3>

            <!-- Компактная форма с тремя полями в ряд -->
            <div class="grid grid-cols-3 gap-3 mb-4">
              <!-- Цена -->
              <div>
                <label for="basePrice" class="block text-sm font-medium text-gray-700 mb-1">
                  Цена
                </label>
                <input
                  type="number"
                  id="basePrice"
                  value={product.basePrice?.value ?? 0}
                  step="0.01"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
              </div>

              <!-- Валюта (символ) -->
              <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">
                  Валюта
                </label>
                <select
                  id="currency"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                >
                  <!-- Динамически заполняется JavaScript -->
                </select>
              </div>

              <!-- Единица измерения -->
              <div>
                <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">
                  За единицу
                </label>
                <select
                  id="unit"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                >
                  <!-- Динамически заполняется JavaScript -->
                </select>
              </div>
            </div>

            <!-- Интерактивное табло цены для покупателя -->
            <div id="customer-price-display" class="p-4 bg-white border-2 border-blue-300 rounded-lg shadow-sm">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <svg class="w-4 h-4 mr-1 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  Цена для покупателя:
                </span>
                <span id="price-source-indicator" class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                  <!-- Источник цены -->
                </span>
              </div>
              <div id="customer-price-text" class="text-2xl font-bold text-blue-700 mb-2">
                0 ₽ за шт
              </div>
              <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                <div class="flex items-start">
                  <svg class="w-3 h-3 mr-1 mt-0.5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                  <div id="price-explanation">
                    <strong>Эту цену увидят покупатели на сайте.</strong><br>
                    Приоритет: Основная цена из вариантов → Базовая цена → 0
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Статус товара -->
          <div class="mb-4">
            <ProductStatusManager
              currentStatus={product.status || 'draft'}
              productId={product.id}
              showLabel={true}
              size="md"
            />
          </div>

          <!-- Наличие на складе -->
          <div class="mb-4">
            <label for="inStock" class="flex items-center">
              <input
                type="checkbox"
                id="inStock"
                class="h-4 w-4 text-blue-600 border-gray-300 rounded"
                checked={product.inStock}
              >
              <span class="ml-2 text-sm text-gray-700">В наличии на складе</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Изображения -->
      <div class="mt-6">
        <h2 class="text-xl font-semibold mb-4">Изображения</h2>

        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <div class="flex justify-between items-center">
            <div>
              <h3 class="text-sm font-medium text-blue-800 mb-2">Автоматическое определение изображений</h3>
              <p class="text-xs text-blue-700">Система автоматически найдет изображения в папке товара по новым правилам именования</p>
            </div>
            <button
              type="button"
              id="auto-detect-images"
              class="update-images-btn text-white px-3 py-1 rounded text-sm"
              style="background-color: #3b82f6;"
            >
              Обновить изображения
            </button>
          </div>
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Главное изображение</label>
          <div class="flex items-center">
            <img
              id="current-main-image"
              src={`/product/${product.images.main}`}
              alt="Главное изображение"
              class="h-20 w-20 object-cover mr-4 rounded border"
            />
            <div>
              <input type="file" id="main-image" accept="image/*" class="text-sm mb-2">
              <p class="text-xs text-gray-500">Текущий файл: {product.images.main}</p>
            </div>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Дополнительные изображения</label>
          <div id="additional-images-grid" class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-2">
            {product.images.additional.map((img, index) => (
              <div class="relative" data-image-path={img}>
                <img
                  src={`/product/${img}`}
                  alt={`Изображение ${index + 1}`}
                  class="h-20 w-full object-cover rounded border"
                />
                <button
                  type="button"
                  data-index={index}
                  data-image-path={img}
                  class="delete-image absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                >
                  ×
                </button>
                <p class="text-xs text-gray-500 mt-1 truncate">{img.split('/').pop()}</p>
              </div>
            ))}
          </div>
          <input type="file" id="additional-images" accept="image/*" multiple class="text-sm">
          <p class="text-xs text-gray-500 mt-1">Можно добавить до 6 дополнительных изображений</p>
        </div>
      </div>

      <div class="mt-8 flex justify-end">
        <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2 hover:bg-gray-400">
          Отмена
        </a>
        <button type="submit" class="save-product-btn text-white px-4 py-2 rounded" style="background-color: #3b82f6;">
          Сохранить изменения
        </button>
      </div>
    </form>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки сохранения продукта */
  .save-product-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки обновления изображений */
  .update-images-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации SLUG */
  .generate-product-slug-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для tooltip категории */
  .category-tooltip {
    white-space: normal;
    word-wrap: break-word;
  }

  /* Показываем tooltip при наведении на иконку или поле ввода */
  .category-info-icon:hover ~ .category-tooltip,
  .category-input:hover ~ .category-tooltip {
    opacity: 1;
    pointer-events: auto;
  }

  /* Альтернативный способ показа tooltip через JavaScript */
  .category-tooltip.show {
    opacity: 1;
    pointer-events: auto;
  }

  /* Стили для атрибутов */
  .attribute-row {
    transition: all 0.3s ease;
  }

  .attribute-row.collapsed {
    opacity: 0.9;
  }

  .collapsed-view {
    transition: all 0.2s ease;
  }

  .collapsed-view:hover {
    background-color: #e5e7eb !important;
  }

  .price-info-icon:hover ~ #price-warning-tooltip,
  .price-info-group:hover #price-warning-tooltip {
    opacity: 1;
    pointer-events: auto;
  }
  #price-warning-tooltip.show {
    opacity: 1;
    pointer-events: auto;
  }
</style>

<script is:inline define:vars={{ product, attributesData, attributeTypes, attributeTypesFullConfig, settingsData }}>
  // Сохраняем данные товара в глобальной переменной для использования в форме
  window.currentProduct = product;

  // Глобальные переменные для атрибутов
  window.attributeTypes = attributeTypes;
  window.attributesData = attributesData;
  window.attributeTypesFullConfig = attributeTypesFullConfig;

  // Настройки товаров
  window.settingsData = settingsData;

  let attributeCounter = 0;

  // Функция для загрузки подкатегорий
  async function loadSubcategories(categoryName, selectedSubcategory = '') {
    const subcategorySelect = document.getElementById('subcategory');

    try {
      // Загружаем данные категорий
      const response = await fetch('/data/product/categories.json');
      const categoriesData = await response.json();

      // Находим выбранную категорию
      const selectedCategory = categoriesData.categories.find(cat => cat.name === categoryName);

      if (selectedCategory && selectedCategory.subcategories) {
        // Очищаем текущие опции
        subcategorySelect.innerHTML = '<option value="">Выберите подкатегорию</option>';

        // Добавляем подкатегории
        selectedCategory.subcategories.forEach(subcategory => {
          const option = document.createElement('option');
          option.value = subcategory;
          option.textContent = subcategory;
          if (subcategory === selectedSubcategory) {
            option.selected = true;
          }
          subcategorySelect.appendChild(option);
        });

        // Включаем выбор подкатегории
        subcategorySelect.disabled = false;
      } else {
        // Если подкатегорий нет
        subcategorySelect.innerHTML = '<option value="">Подкатегории отсутствуют</option>';
        subcategorySelect.disabled = true;
      }
    } catch (error) {
      subcategorySelect.innerHTML = '<option value="">Ошибка загрузки подкатегорий</option>';
      subcategorySelect.disabled = true;
    }
  }

  // Инициализация подкатегорий при загрузке страницы
  document.addEventListener('DOMContentLoaded', async function() {
    const currentCategory = window.currentProduct.category;
    const currentSubcategory = window.currentProduct.subcategory;

    if (currentCategory) {
      loadSubcategories(currentCategory, currentSubcategory);
    }

    // Инициализация tooltip для категории
    initCategoryTooltip();

    // Инициализация настроек товаров (без инициализации цены покупателя)
    initializeProductTypeDisplay();
    initializeCurrencies();
    initializeUnits();

    // Инициализация компонента атрибутов
    initializeAttributesComponent();

    // Загрузка существующих атрибутов с задержкой для обеспечения готовности данных
    setTimeout(() => {
      loadExistingAttributes();
    }, 100);

    // Загрузка и инициализация компонента вариантов
    loadVariantsComponent();

    // Инициализируем отображение цены покупателя после загрузки компонента вариантов
    setTimeout(() => {
      initializeCustomerPriceDisplay();
      updateCustomerPrice();
    }, 300);

    // Инициализация всплывающей подсказки для иконки цены
    initializePriceTooltip();
  });

  // Функция для инициализации всплывающей подсказки
  function initializePriceTooltip() {
    const icon = document.getElementById('price-info-icon');
    const tooltip = document.getElementById('price-tooltip');

    if (!icon || !tooltip) return;

    icon.addEventListener('mouseenter', () => {
      tooltip.classList.remove('opacity-0', 'pointer-events-none');
      tooltip.classList.add('opacity-100');
    });

    icon.addEventListener('mouseleave', () => {
      tooltip.classList.remove('opacity-100');
      tooltip.classList.add('opacity-0', 'pointer-events-none');
    });
  }

  // Функция для инициализации tooltip категории
  function initCategoryTooltip() {
    const categoryIcon = document.querySelector('.category-info-icon');
    const categoryInput = document.querySelector('.category-input');
    const tooltip = document.getElementById('category-warning-tooltip');

    if (!categoryIcon || !categoryInput || !tooltip) return;

    // Показываем tooltip при наведении на иконку
    categoryIcon.addEventListener('mouseenter', function() {
      tooltip.classList.add('show');
    });

    categoryIcon.addEventListener('mouseleave', function() {
      tooltip.classList.remove('show');
    });

    // Показываем tooltip при наведении на поле ввода
    categoryInput.addEventListener('mouseenter', function() {
      tooltip.classList.add('show');
    });

    categoryInput.addEventListener('mouseleave', function() {
      tooltip.classList.remove('show');
    });

    // Показываем tooltip при фокусе на поле ввода
    categoryInput.addEventListener('focus', function() {
      tooltip.classList.add('show');
    });

    categoryInput.addEventListener('blur', function() {
      tooltip.classList.remove('show');
    });
  }

  // Функция для загрузки и инициализации компонента вариантов
  function loadVariantsComponent() {
    // Сначала загружаем утилиты для работы с атрибутами
    import('/src/utils/attributeManager.js').then(() => {
      // Затем загружаем утилиты для работы с вариантами
      import('/src/utils/variantManager.js').then(() => {
        // Функции вариантов доступны через window
        // Инициализируем компонент вариантов
        if (window.initializeVariantsComponent) {
          window.initializeVariantsComponent();
        }
        // Загружаем существующие варианты товара
        loadExistingVariants();

        // Инициализируем состояние полей базовой цены после загрузки вариантов
        setTimeout(() => {
          if (window.toggleBasePriceFields) {
            window.toggleBasePriceFields();
          }
        }, 500); // Даем время на загрузку вариантов
      }).catch(() => {
        // Ошибка при загрузке утилит вариантов
      });
    }).catch(() => {
      // Ошибка при загрузке утилит атрибутов
    });
  }

  // Функция для загрузки существующих вариантов товара
  function loadExistingVariants() {
    const product = window.currentProduct;

    if (!product || !product.variants || !Array.isArray(product.variants)) {
      return;
    }

    // Загружаем каждый вариант
    product.variants.forEach((variant, index) => {
      setTimeout(() => {
        loadExistingVariant(variant);
      }, index * 100); // Небольшая задержка между загрузкой вариантов
    });
  }

  // Функция для загрузки одного варианта
  function loadExistingVariant(variantData) {
    if (!window.addVariantRow) {
      return;
    }

    // Добавляем новую строку варианта
    window.addVariantRow();

    // Находим последний добавленный вариант
    const variantRows = document.querySelectorAll('.variant-row');
    const lastVariantRow = variantRows[variantRows.length - 1];

    if (!lastVariantRow) {
      return;
    }

    // Заполняем данные варианта
    setTimeout(() => {
      fillVariantData(lastVariantRow, variantData);
    }, 200);
  }

  // Функция для заполнения данных варианта
  function fillVariantData(variantRow, variantData) {
    try {


      // Сохраняем ID варианта для последующего использования
      if (variantData.id) {
        variantRow.setAttribute('data-variant-id', variantData.id);
      }

      // Заполняем основные поля
      const nameInput = variantRow.querySelector('.variant-name');
      const priceInput = variantRow.querySelector('.variant-price');
      const currencySelect = variantRow.querySelector('.variant-currency');
      const unitSelect = variantRow.querySelector('.variant-unit');
      const inStockCheckbox = variantRow.querySelector('.variant-in-stock');
      const skuInput = variantRow.querySelector('.variant-sku');
      const isPrimaryPriceCheckbox = variantRow.querySelector('.variant-is-primary-price');

      if (nameInput) nameInput.value = variantData.name || '';
      if (priceInput) priceInput.value = variantData.price?.value || '';
      if (currencySelect) currencySelect.value = variantData.price?.currency || '';
      if (unitSelect) unitSelect.value = variantData.price?.unit || '';
      if (inStockCheckbox) inStockCheckbox.checked = variantData.inStock !== false;
      if (skuInput) skuInput.value = variantData.sku || '';
      if (isPrimaryPriceCheckbox) isPrimaryPriceCheckbox.checked = variantData.isPrimaryPrice || false;

      // Заполняем атрибуты варианта
      if (variantData.attributes && typeof variantData.attributes === 'object') {
        fillVariantAttributes(variantRow, variantData.attributes);
      }

      // Сохраняем вариант (сворачиваем в компактный вид)
      setTimeout(() => {
        const saveBtn = variantRow.querySelector('.save-variant-btn');
        if (saveBtn && window.saveVariant) {
          saveBtn.click();
        }
      }, 300);

    } catch (error) {
      // Игнорируем ошибки заполнения данных варианта
    }
  }

  // Функция для получения атрибутов из данных товара
  function getAttributesFromProductData() {
    const product = window.currentProduct;

    if (!product || !product.attributes) {
      return {};
    }

    const attributes = {};

    // Преобразуем атрибуты товара в формат, ожидаемый вариантами
    for (const [attributeKey, attributeValue] of Object.entries(product.attributes)) {
      // Проверяем наличие типа атрибута
      if (!window.attributeTypes || !window.attributeTypes[attributeKey]) {
        continue;
      }

      // Проверяем наличие данных атрибута
      if (!window.attributesData || !window.attributesData[attributeKey]) {
        continue;
      }

      attributes[attributeKey] = {
        type: attributeKey,
        name: window.attributeTypes[attributeKey].name || attributeKey,
        value: attributeValue,
        data: window.attributesData[attributeKey]
      };
    }

    return attributes;
  }

  // Функция для заполнения атрибутов варианта
  function fillVariantAttributes(variantRow, attributes) {
    if (!attributes || typeof attributes !== 'object') {
      return;
    }

    const container = variantRow.querySelector('.variant-attributes-container');
    if (!container) {
      return;
    }

    // Получаем выбранные атрибуты из основной формы
    let selectedAttributes = window.getSelectedProductAttributes();

    // Если атрибуты не найдены в форме, получаем их из данных товара
    if (!selectedAttributes || Object.keys(selectedAttributes).length === 0) {
      selectedAttributes = getAttributesFromProductData();
    }

    if (!selectedAttributes || Object.keys(selectedAttributes).length === 0) {
      return;
    }

    // Создаем строки атрибутов только для тех атрибутов, которые сохранены в данных варианта
    Object.keys(attributes).forEach(attributeType => {
      // Проверяем, что этот тип атрибута есть в выбранных атрибутах
      if (!selectedAttributes[attributeType]) {
        return;
      }

      // Получаем значение атрибута из данных варианта
      const attributeValue = attributes[attributeType];

      // Создаем строку атрибута
      const attributeId = `variant-attr-${variantRow.id}-${attributeType}-${Date.now()}`;
      const attributeRow = document.createElement('div');
      attributeRow.className = 'variant-attribute-row bg-white border border-gray-200 rounded-lg';
      attributeRow.id = attributeId;

      // Создаем опции только для выбранных атрибутов
      const attributeOptions = Object.entries(selectedAttributes).map(([key, attr]) =>
        `<option value="${key}" ${key === attributeType ? 'selected' : ''}>${attr.name}</option>`
      ).join('');

      attributeRow.innerHTML = `
        <!-- Развернутая форма -->
        <div class="attribute-form-expanded p-4">
          <div class="flex justify-between items-center mb-4">
            <h6 class="text-sm font-semibold text-gray-900">Атрибут</h6>
            <div class="flex items-center gap-2">
              <button type="button" class="collapse-attribute-btn px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                Сохранить
              </button>
              <button type="button" class="remove-variant-attribute-btn px-3 py-1.5 text-sm font-medium text-red-600 hover:text-red-800 hover:bg-red-50 border border-red-300 rounded-md transition-colors" data-attribute-id="${attributeId}">
                Удалить
              </button>
            </div>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Тип атрибута</label>
              <select class="variant-attribute-type w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="">Выберите атрибут</option>
                ${attributeOptions}
              </select>
            </div>

            <div class="variant-attribute-value">
              <label class="block text-sm font-medium text-gray-700 mb-2">Значение</label>
              <div class="px-3 py-2 text-sm text-gray-500 bg-gray-50 border border-gray-300 rounded-md">
                Загрузка...
              </div>
            </div>
          </div>
        </div>

        <!-- Свернутый вид (скрыт по умолчанию) -->
        <div class="attribute-form-collapsed hidden p-3 bg-gray-50">
          <div class="flex justify-between items-center">
            <div class="flex-1">
              <div class="flex items-center gap-2">
                <span class="text-sm font-medium text-gray-900 attribute-type-name">Тип атрибута</span>
                <span class="text-xs text-gray-500">•</span>
                <span class="text-sm text-gray-700 attribute-value-summary">Значение</span>
              </div>
            </div>
            <div class="flex items-center gap-2">
              <button type="button" class="expand-attribute-btn text-sm text-blue-600 hover:text-blue-800 font-medium">
                Редактировать
              </button>
              <button type="button" class="remove-variant-attribute-btn text-red-600 hover:text-red-800 hover:bg-red-50 p-1 rounded transition-colors" data-attribute-id="${attributeId}" title="Удалить атрибут">
                ×
              </button>
            </div>
          </div>
        </div>
      `;

      container.appendChild(attributeRow);

      // Добавляем обработчики событий
      const typeSelect = attributeRow.querySelector('.variant-attribute-type');
      const removeBtns = attributeRow.querySelectorAll('.remove-variant-attribute-btn');
      const collapseBtn = attributeRow.querySelector('.collapse-attribute-btn');
      const expandBtn = attributeRow.querySelector('.expand-attribute-btn');

      if (typeSelect) {
        typeSelect.addEventListener('click', function(event) {
          event.stopPropagation();
        });

        typeSelect.addEventListener('change', function(event) {
          event.stopPropagation();
          if (window.handleVariantAttributeTypeChange) {
            window.handleVariantAttributeTypeChange(this, attributeId);
          }
          // Активируем кнопку сохранения после выбора типа
          if (window.updateCollapseButtonState) {
            window.updateCollapseButtonState(attributeId);
          }
        });
      }

      // Обработчики для кнопок удаления (есть в обоих видах)
      removeBtns.forEach(btn => {
        btn.addEventListener('click', function(event) {
          event.stopPropagation(); // Предотвращаем всплытие события
          if (window.removeVariantAttributeRow) {
            window.removeVariantAttributeRow(this.dataset.attributeId);
          }
        });
      });

      // Обработчик сохранения (сворачивания)
      if (collapseBtn) {
        collapseBtn.addEventListener('click', function(event) {
          event.stopPropagation();
          if (window.collapseAttributeForm) {
            window.collapseAttributeForm(attributeId);
          }
        });
      }

      // Обработчик разворачивания
      if (expandBtn) {
        expandBtn.addEventListener('click', function(event) {
          event.stopPropagation();
          if (window.expandAttributeForm) {
            window.expandAttributeForm(attributeId);
          }
        });
      }

      // Заполняем значение атрибута после создания интерфейса
      setTimeout(() => {
        fillVariantAttributeValue(attributeRow, attributeType, attributeValue);
      }, 100);
    });
  }

  // Функция для заполнения значения конкретного атрибута варианта
  function fillVariantAttributeValue(attributeRow, attributeType, attributeValue) {
    try {
      // Сначала инициализируем интерфейс для выбранного типа атрибута
      const typeSelect = attributeRow.querySelector('.variant-attribute-type');
      if (window.handleVariantAttributeTypeChange && typeSelect) {
        window.handleVariantAttributeTypeChange(typeSelect, attributeRow.id);
      }

      // Ждем, пока интерфейс создастся, затем заполняем значение
      setTimeout(() => {
        const valueContainer = attributeRow.querySelector('.variant-attribute-value');
        if (!valueContainer) {
          return;
        }

        // Ищем элементы для заполнения значения
        const valueSelect = valueContainer.querySelector('.variant-attribute-value-select');
        const valueInput = valueContainer.querySelector('.variant-attribute-value-input');
        const valueCheckboxes = valueContainer.querySelectorAll('.variant-attribute-checkbox');

        // Проверяем, есть ли чекбоксы (множественный выбор)
        if (valueCheckboxes.length > 0) {
          // Обрабатываем множественный выбор через чекбоксы
          if (Array.isArray(attributeValue)) {
            // Если значение - массив, отмечаем соответствующие чекбоксы
            valueCheckboxes.forEach(checkbox => {
              let shouldCheck = false;

              if (attributeType === 'color_pigments' || attributeType === 'material') {
                // Для цветовых пигментов и материалов сравниваем по ID
                shouldCheck = attributeValue.some(item =>
                  (typeof item === 'object' && item.id === checkbox.value) ||
                  (typeof item === 'string' && item === checkbox.value)
                );
              } else {
                // Для других типов сравниваем значения напрямую
                shouldCheck = attributeValue.includes(checkbox.value);
              }

              checkbox.checked = shouldCheck;
            });
          } else if (attributeValue) {
            // Если значение не массив, но есть значение, пытаемся найти соответствующий чекбокс
            valueCheckboxes.forEach(checkbox => {
              let shouldCheck = false;

              if (attributeType === 'color_pigments' || attributeType === 'material') {
                // Для цветовых пигментов и материалов
                if (typeof attributeValue === 'object' && attributeValue.id) {
                  shouldCheck = checkbox.value === attributeValue.id;
                } else if (typeof attributeValue === 'string') {
                  shouldCheck = checkbox.value === attributeValue;
                }
              } else {
                // Для других типов
                shouldCheck = checkbox.value === attributeValue;
              }

              checkbox.checked = shouldCheck;
            });
          }
        } else if (valueSelect) {


          // Для селектов - устанавливаем выбранное значение
          if (attributeType === 'size' && typeof attributeValue === 'object') {
            // Для размеров ищем опцию с соответствующим JSON
            const options = valueSelect.querySelectorAll('option');
            for (let option of options) {
              if (option.value) {
                try {
                  const optionValue = JSON.parse(option.value);
                  if (optionValue.length === attributeValue.length &&
                      optionValue.width === attributeValue.width &&
                      optionValue.height === attributeValue.height) {
                    option.selected = true;

                    break;
                  }
                } catch (e) {
                  // Игнорируем ошибки парсинга
                }
              }
            }
          } else if (attributeType === 'colors') {
            // Для цветов - ищем соответствующее значение
            valueSelect.value = attributeValue;

          } else if (['surfaces', 'patterns'].includes(attributeType)) {
            // Для объектных атрибутов - используем id
            const idToFind = typeof attributeValue === 'object' ? attributeValue.id : attributeValue;
            valueSelect.value = idToFind;

          } else if (attributeType === 'color_pigments') {
            // Для цветовых пигментов в вариантах - одиночный выбор через селект


            // Определяем ID пигмента для поиска
            let pigmentIdToFind = null;
            if (typeof attributeValue === 'object' && attributeValue.id) {
              pigmentIdToFind = attributeValue.id;
            } else if (typeof attributeValue === 'string') {
              pigmentIdToFind = attributeValue;
            }



            if (pigmentIdToFind && valueSelect) {
              // Ищем опцию с соответствующим ID
              const options = valueSelect.querySelectorAll('option');
              for (let option of options) {
                if (option.value === pigmentIdToFind) {
                  option.selected = true;

                  break;
                }
              }
            }
          } else if (attributeType === 'material') {
            // Для материала - используем id
            const idToFind = typeof attributeValue === 'object' ? attributeValue.id : attributeValue;
            valueSelect.value = idToFind;

          } else {
            // Для других типов - простое сравнение значений
            valueSelect.value = attributeValue;

          }
        } else if (valueInput) {


          // Для инпутов - устанавливаем значение
          if (typeof attributeValue === 'object') {
            valueInput.value = JSON.stringify(attributeValue);
          } else {
            valueInput.value = attributeValue;
          }
        }

        // Автоматически сворачиваем атрибут после заполнения значения
        setTimeout(() => {
          if (window.collapseAttributeForm) {
            window.collapseAttributeForm(attributeRow.id);
          }
        }, 100);
      }, 500); // Увеличиваем задержку для надежности

    } catch (error) {
      // Игнорируем ошибки заполнения значения атрибута
    }
  }

  // Инициализация настроек товаров
  function initializeProductSettings() {
    initializeProductTypeDisplay();
    initializeCurrencies();
    initializeUnits();
  }

  // Инициализация отображения типа товара
  function initializeProductTypeDisplay() {
    const productTypeDisplay = document.getElementById('product-type-display');
    const productType = window.currentProduct.productType || 'physical';

    if (productTypeDisplay && window.settingsData) {
      // Находим тип товара в настройках
      const typeData = window.settingsData.product_types.supported.find(type => type.key === productType);
      if (typeData) {
        productTypeDisplay.value = `${typeData.label.ru} - ${typeData.description.ru}`;
      } else {
        productTypeDisplay.value = productType;
      }
    }
  }

  // Инициализация валют
  function initializeCurrencies() {
    const currencySelect = document.getElementById('currency');
    if (!currencySelect || !window.settingsData) return;

    // Получаем текущую валюту товара или используем основную валюту по умолчанию
    const currentCurrency = window.currentProduct.basePrice?.currency || window.settingsData.currencies.primary;

    // Заполняем селект валют
    currencySelect.innerHTML = '';
    window.settingsData.currencies.supported.forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.key;
      option.textContent = `${currency.key} - ${currency.label.ru}`;
      option.selected = currency.key === currentCurrency;
      currencySelect.appendChild(option);
    });

    // Если у товара не было валюты, устанавливаем основную валюту
    if (!window.currentProduct.basePrice?.currency) {
      // Товар не имел валюты, установлена основная валюта
    }
  }

  // Инициализация единиц измерения
  function initializeUnits() {
    const productType = window.currentProduct.productType || 'physical';
    const currentUnit = window.currentProduct.basePrice?.unit || 'piece';

    updateAvailableUnits(productType, currentUnit);
  }

  // Обновление доступных единиц измерения в зависимости от типа товара
  function updateAvailableUnits(productType, selectedUnit = null) {
    const unitSelect = document.getElementById('unit');
    if (!unitSelect || !window.settingsData) return;

    // Определяем какие единицы измерения подходят для данного типа товара
    let relevantUnitTypes = [];

    switch (productType) {
      case 'physical':
        relevantUnitTypes = ['weight', 'volume', 'dimensions', 'countable'];
        break;
      case 'digital':
        relevantUnitTypes = ['countable'];
        break;
      case 'service':
        relevantUnitTypes = ['service'];
        break;
      default:
        relevantUnitTypes = ['weight', 'volume', 'dimensions', 'countable', 'service'];
    }

    // Очищаем селект
    unitSelect.innerHTML = '';

    // Добавляем единицы измерения для каждого подходящего типа
    relevantUnitTypes.forEach(unitType => {
      if (window.settingsData.units[unitType] && window.settingsData.units[unitType].supported) {
        // Добавляем заголовок группы
        const optgroup = document.createElement('optgroup');
        optgroup.label = getUnitTypeLabel(unitType);

        window.settingsData.units[unitType].supported.forEach(unit => {
          const option = document.createElement('option');
          option.value = unit.key;
          option.textContent = `за ${unit.label.ru} (${unit.key})`;
          option.dataset.unitLabel = unit.label.ru;

          // Выбираем текущую единицу товара
          if (unit.key === selectedUnit) {
            option.selected = true;
          }

          optgroup.appendChild(option);
        });

        unitSelect.appendChild(optgroup);
      }
    });

    // Обновляем предварительный просмотр цены
    updateCustomerPrice();
  }

  // Получить название типа единицы измерения
  function getUnitTypeLabel(unitType) {
    const labels = {
      'weight': 'Вес',
      'volume': 'Объем',
      'dimensions': 'Размеры',
      'countable': 'Штучные',
      'service': 'Услуги'
    };
    return labels[unitType] || unitType;
  }

  // Инициализация отображения цены для покупателя
  function initializeCustomerPriceDisplay() {
    const priceInput = document.getElementById('basePrice');
    const currencySelect = document.getElementById('currency');
    const unitSelect = document.getElementById('unit');

    if (priceInput && currencySelect && unitSelect) {
      // Добавляем обработчики событий для обновления цены покупателя
      priceInput.addEventListener('input', updateCustomerPrice);
      currencySelect.addEventListener('change', updateCustomerPrice);
      unitSelect.addEventListener('change', updateCustomerPrice);

      // Показываем цену покупателя при загрузке
      updateCustomerPrice();
    }

    // Добавляем обработчик для изменений в вариантах
    if (typeof window.addVariantChangeListener === 'function') {
      window.addVariantChangeListener(updateCustomerPrice);
    }
  }

  // Получение цены из вариантов с основной ценой
  function getPrimaryPriceFromVariants() {
    // ТОЛЬКО проверяем текущее состояние DOM (актуальные данные)
    // Не используем fallback на window.currentProduct, так как он не обновляется при удалении
    if (typeof window.getProductVariants === 'function') {
      try {
        const variants = window.getProductVariants();
        if (Array.isArray(variants)) {
          const primaryVariant = variants.find(variant => variant.isPrimaryPrice === true && variant.price);
          if (primaryVariant && primaryVariant.price) {
            return primaryVariant.price;
          }
        }
      } catch (error) {
        // Игнорируем ошибки
      }
    }

    return null;
  }

  // Получение символа валюты
  function getCurrencySymbol(currencyCode) {
    if (!window.variantSettingsData?.currencies?.supported) return currencyCode;

    const currency = window.variantSettingsData.currencies.supported.find(c => c.key === currencyCode);
    return currency?.simvol || currencyCode;
  }

  // Получение названия единицы измерения
  function getUnitLabel(unitCode) {
    const unitSelect = document.getElementById('unit');
    if (unitSelect) {
      const selectedOption = Array.from(unitSelect.options).find(option => option.value === unitCode);
      return selectedOption?.dataset.unitLabel || selectedOption?.textContent || unitCode;
    }
    return unitCode;
  }

  // Обновление цены для покупателя
  function updateCustomerPrice() {
    const customerPriceDisplay = document.getElementById('customer-price-display');
    const customerPriceText = document.getElementById('customer-price-text');
    const priceSourceIndicator = document.getElementById('price-source-indicator');
    const priceExplanation = document.getElementById('price-explanation');

    if (!customerPriceDisplay || !customerPriceText || !priceSourceIndicator) {
      return;
    }

    let finalPrice = null;
    let priceSource = '';
    let explanation = '';

    // 1. Проверяем основную цену из вариантов (приоритет 1)
    const primaryPrice = getPrimaryPriceFromVariants();

    if (primaryPrice && primaryPrice.value > 0) {
      finalPrice = primaryPrice;
      priceSource = 'Основная из вариантов';
      explanation = '<strong>Цена взята из варианта, помеченного как основная.</strong><br>Покупатели увидят эту цену на сайте.';
    } else {
      // 2. Проверяем базовую цену (приоритет 2)
      const priceInput = document.getElementById('basePrice');
      const currencySelect = document.getElementById('currency');
      const unitSelect = document.getElementById('unit');

      if (priceInput && currencySelect && unitSelect) {
        const basePrice = parseFloat(priceInput.value) || 0;
        const currency = currencySelect.value;
        const unit = unitSelect.value;

        if (basePrice > 0 && currency && unit) {
          finalPrice = {
            value: basePrice,
            currency: currency,
            unit: unit
          };
          priceSource = 'Базовая цена';
          explanation = '<strong>Цена взята из базовой цены товара.</strong><br>Покупатели увидят эту цену на сайте.';
        }
      }
    }

    // 3. Если нет ни основной, ни базовой цены (приоритет 3)
    if (!finalPrice) {
      finalPrice = { value: 0, currency: '₽', unit: 'шт' };
      priceSource = 'Не указана';
      explanation = '<strong>Цена не указана.</strong><br>Укажите базовую цену или создайте вариант с основной ценой.';
    }

    // Форматируем и отображаем цену
    const currencySymbol = getCurrencySymbol(finalPrice.currency);
    const unitLabel = getUnitLabel(finalPrice.unit);
    const formattedPrice = finalPrice.value.toFixed(2);

    const newPriceText = `${formattedPrice} ${currencySymbol} за ${unitLabel}`;
    customerPriceText.innerHTML = newPriceText;
    priceSourceIndicator.textContent = priceSource;

    if (priceExplanation) {
      priceExplanation.innerHTML = explanation;
    }

    // Меняем стиль в зависимости от источника цены
    customerPriceDisplay.className = 'p-4 border-2 rounded-lg shadow-sm';
    if (finalPrice.value === 0) {
      customerPriceDisplay.classList.add('bg-red-50', 'border-red-300');
      customerPriceText.className = 'text-2xl font-bold text-red-700 mb-2';
    } else if (priceSource === 'Основная из вариантов') {
      customerPriceDisplay.classList.add('bg-green-50', 'border-green-300');
      customerPriceText.className = 'text-2xl font-bold text-green-700 mb-2';
    } else {
      customerPriceDisplay.classList.add('bg-blue-50', 'border-blue-300');
      customerPriceText.className = 'text-2xl font-bold text-blue-700 mb-2';
    }

    // Управляем видимостью всплывающей подсказки
    updatePriceTooltipVisibility(priceSource);
  }

  // Функция для управления видимостью всплывающей подсказки
  function updatePriceTooltipVisibility(priceSource) {
    const icon = document.getElementById('price-info-icon');
    const tooltip = document.getElementById('price-tooltip');

    if (!icon || !tooltip) return;

    // Показываем подсказку только если есть основная цена из вариантов
    if (priceSource === 'Основная из вариантов') {
      icon.style.display = 'block';
    } else {
      icon.style.display = 'none';
      // Скрываем подсказку если она была открыта
      tooltip.classList.remove('opacity-100');
      tooltip.classList.add('opacity-0', 'pointer-events-none');
    }
  }

  // Инициализация компонента атрибутов
  function initializeAttributesComponent() {
    const addAttributeBtn = document.getElementById('add-attribute-btn');
    if (addAttributeBtn) {
      addAttributeBtn.addEventListener('click', addAttributeRow);
    }
  }

  // Загрузка существующих атрибутов товара
  function loadExistingAttributes() {
    const product = window.currentProduct;

    // Проверяем готовность всех необходимых данных
    if (!window.attributeTypes || !window.attributesData) {
      setTimeout(loadExistingAttributes, 100);
      return;
    }

    if (!product || !product.attributes) {
      return;
    }

    // Проходим по всем атрибутам товара и создаем для них формы
    for (const [attributeKey, attributeValue] of Object.entries(product.attributes)) {
      let currentKey = attributeKey;
      let currentValue = attributeValue;

      // Для размеров используем ключ 'size' как в данных товара
      // currentKey остается 'size'

      // Пропускаем атрибуты, которые не имеют соответствующего типа
      if (!window.attributeTypes[currentKey]) {
        continue;
      }

      const row = addAttributeRow(currentKey, currentValue);
      if (row) {
        // Сворачиваем атрибут после создания, чтобы он не занимал много места
        // Используем достаточную задержку, чтобы убедиться, что все элементы отрисованы
        setTimeout(() => {
          // Проверяем, что интерфейс полностью загружен
          if (checkIfHasSelectedValue(row, currentKey)) {
            row.setAttribute('data-current-value', JSON.stringify(currentValue));
            collapseAttributeRow(row, currentKey);
          }
        }, 200); // Увеличиваем задержку для надежности
      }
    }
  }

  // Функция добавления строки атрибута
  function addAttributeRow(preselectedType = null, preselectedValue = null) {
    const container = document.getElementById('attributes-container');
    if (!container) return;

    // Проверяем, загружены ли данные атрибутов
    if (!window.attributeTypes || !window.attributesData) {
      // Данные атрибутов не загружены, откладываем добавление атрибута
      setTimeout(() => addAttributeRow(preselectedType, preselectedValue), 100);
      return;
    }



    // Сворачиваем все существующие атрибуты перед добавлением нового
    collapseAllAttributes();

    const attributeId = `attribute-${++attributeCounter}`;
    const attributeRow = document.createElement('div');
    attributeRow.className = 'attribute-row bg-gray-50 p-4 rounded-lg border';
    attributeRow.id = attributeId;

    attributeRow.innerHTML = `
      <div class="attribute-header grid grid-cols-1 md:grid-cols-12 gap-4">
        <div class="md:col-span-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Тип атрибута</label>
          <select class="attribute-type-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" data-attribute-id="${attributeId}">
            <option value="">Выберите тип атрибута</option>
            ${Object.entries(window.attributeTypes).map(([key, config]) =>
              `<option value="${key}" ${preselectedType === key ? 'selected' : ''}>${config.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="attribute-value-container md:col-span-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">Значение</label>
          <div class="attribute-value-content">
            <p class="text-sm text-gray-500">Выберите тип атрибута</p>
          </div>
        </div>
        <div class="md:col-span-2 flex items-end justify-end">
          <button type="button" class="remove-attribute-btn px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 border border-red-300 rounded text-sm" data-attribute-id="${attributeId}">
            удалить
          </button>
        </div>
      </div>
    `;

    container.appendChild(attributeRow);

    // Добавляем обработчики событий
    const typeSelect = attributeRow.querySelector('.attribute-type-select');
    const removeBtn = attributeRow.querySelector('.remove-attribute-btn');

    if (typeSelect) {
      typeSelect.addEventListener('change', async function() {
        await handleAttributeTypeChange(this);
      });

      // Добавляем обработчик для сворачивания других атрибутов при фокусе
      typeSelect.addEventListener('focus', function() {
        collapseOtherAttributes(attributeId);
      });

      // Если есть предвыбранный тип, инициализируем его
      if (preselectedType) {
        handleAttributeTypeChange(typeSelect, preselectedValue).catch(() => {
          // Ошибка при инициализации атрибута
        });
      }
    }

    if (removeBtn) {
      removeBtn.addEventListener('click', function() {
        removeAttributeRow(this.dataset.attributeId);
      });
    }

    // Добавляем обработчик для сворачивания при клике на значения
    const valueContainer = attributeRow.querySelector('.attribute-value-content');
    if (valueContainer) {
      valueContainer.addEventListener('click', function() {
        collapseOtherAttributes(attributeId);
      });
    }

    return attributeRow;
  }

  // Функция удаления строки атрибута
  function removeAttributeRow(attributeId) {
    const row = document.getElementById(attributeId);
    if (row) {
      row.remove();
    }
  }

  // Функция сворачивания всех атрибутов
  function collapseAllAttributes() {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const valueContainer = row.querySelector('.attribute-value-content');

      // Проверяем, есть ли выбранный тип и значение
      if (typeSelect && typeSelect.value && valueContainer) {
        const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

        if (hasSelectedValue) {
          collapseAttributeRow(row, typeSelect.value);
        }
      }
    });
  }

  // Функция сворачивания других атрибутов
  function collapseOtherAttributes(currentAttributeId) {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      if (row.id !== currentAttributeId) {
        const typeSelect = row.querySelector('.attribute-type-select');
        const valueContainer = row.querySelector('.attribute-value-content');

        // Проверяем, есть ли выбранный тип и значение
        if (typeSelect && typeSelect.value && valueContainer) {
          const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

          if (hasSelectedValue) {
            collapseAttributeRow(row, typeSelect.value);
          }
        }
      }
    });
  }

  // Проверяем, есть ли выбранное значение в атрибуте
  function checkIfHasSelectedValue(row, attributeType) {
    if (attributeType === 'colors') {
      return row.querySelectorAll('input[type="checkbox"]:checked').length > 0;
    } else if (attributeType === 'size') {
      // Для размеров проверяем наличие выбранных чекбоксов или пользовательских размеров
      const checkedSizes = row.querySelectorAll('.attribute-checkbox:checked');
      const customSizes = row.querySelectorAll('.custom-size-item');
      return checkedSizes.length > 0 || customSizes.length > 0;
    } else if (attributeType === 'weight') {
      // Для весов проверяем наличие выбранных чекбоксов или пользовательских весов
      const checkedWeights = row.querySelectorAll('.attribute-checkbox:checked');
      const customWeights = row.querySelectorAll('.custom-weight-item');
      return checkedWeights.length > 0 || customWeights.length > 0;
    } else if (attributeType === 'material') {
      // Для материалов проверяем наличие выбранных чекбоксов или пользовательских материалов
      const checkedMaterials = row.querySelectorAll('.attribute-checkbox:checked');
      const customMaterials = row.querySelectorAll('.custom-material-item');
      return checkedMaterials.length > 0 || customMaterials.length > 0;
    } else {
      const select = row.querySelector('.attribute-value-select');
      return select && select.value;
    }
  }

  // Получаем текущее значение атрибута из формы
  function getCurrentAttributeValue(row, attributeType) {
    if (attributeType === 'colors') {
      const checkedColors = row.querySelectorAll('input[type="checkbox"]:checked');
      return Array.from(checkedColors).map(cb => cb.value);
    } else if (attributeType === 'size') {
      // Для размеров собираем все выбранные размеры
      const sizes = [];

      // Собираем выбранные предустановленные размеры
      const checkedSizes = row.querySelectorAll('.attribute-checkbox:checked');
      checkedSizes.forEach(checkbox => {
        try {
          const size = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          sizes.push(size);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      // Собираем пользовательские размеры
      const customSizes = row.querySelectorAll('.custom-size-item');
      customSizes.forEach(item => {
        try {
          const size = JSON.parse(item.getAttribute('data-size'));
          sizes.push(size);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return sizes.length > 0 ? sizes : null;
    } else if (attributeType === 'weight') {
      const weights = [];

      // Собираем выбранные предустановленные веса
      const checkedWeights = row.querySelectorAll('.attribute-checkbox:checked');
      checkedWeights.forEach(checkbox => {
        try {
          const weight = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          weights.push(weight);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      // Собираем пользовательские веса
      const customWeights = row.querySelectorAll('.custom-weight-item');
      customWeights.forEach(item => {
        try {
          const weight = JSON.parse(item.getAttribute('data-weight'));
          weights.push(weight);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return weights;
    } else if (attributeType === 'material') {
      const materials = [];

      // Собираем выбранные предустановленные материалы
      const checkedMaterials = row.querySelectorAll('.attribute-checkbox:checked');
      checkedMaterials.forEach(checkbox => {
        try {
          const material = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          materials.push(material);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      // Собираем пользовательские материалы
      const customMaterials = row.querySelectorAll('.custom-material-item');
      customMaterials.forEach(item => {
        try {
          const material = JSON.parse(item.getAttribute('data-material'));
          materials.push(material);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return materials;
    } else if (attributeType === 'color_pigments') {
      // Цветовые пигменты - множественный выбор
      const pigments = [];

      // Собираем выбранные пигменты
      const checkedPigments = row.querySelectorAll('.attribute-checkbox:checked');
      checkedPigments.forEach(checkbox => {
        try {
          const pigment = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          pigments.push(pigment);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return pigments;
    } else {
      const select = row.querySelector('.attribute-value-select');
      if (select) {
        if (select.multiple) {
          return Array.from(select.selectedOptions).map(option => option.value);
        } else {
          const selectedValue = select.value;
          if (selectedValue) {
            try {
              return JSON.parse(selectedValue.replace(/&quot;/g, '"'));
            } catch {
              return selectedValue;
            }
          }
        }
      }
    }
    return null;
  }

  // Добавляем обработчики изменений для атрибута
  function addAttributeChangeListeners(row, attributeType) {
    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    // Функция для обновления сохраненного значения
    const updateSavedValue = () => {
      const currentValue = getCurrentAttributeValue(row, attributeType);
      if (currentValue !== null) {
        row.setAttribute('data-current-value', JSON.stringify(currentValue));
      }
    };

    if (attributeType === 'colors') {
      // Для чекбоксов цветов
      const checkboxes = valueContainer.querySelectorAll('input[type="checkbox"]');
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSavedValue);
      });

      // Обработчик для кнопки сохранения цветов
      const saveColorsBtn = valueContainer.querySelector('.save-colors-btn');
      if (saveColorsBtn) {
        saveColorsBtn.addEventListener('click', function() {
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');

          if (selectedCheckboxes.length > 0) {
            updateSavedValue();
            collapseAttributeRow(row, 'colors');
          } else {
            alert('Выберите хотя бы один цвет');
          }
        });
      }
    } else if (attributeType === 'size') {
      // Для чекбоксов размеров
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSavedValue);
      });

      // Обработчик для добавления пользовательского размера
      const addCustomSizeBtn = valueContainer.querySelector('.add-custom-size-btn');
      if (addCustomSizeBtn) {
        addCustomSizeBtn.addEventListener('click', function() {
          const lengthInput = valueContainer.querySelector('.custom-size-length');
          const widthInput = valueContainer.querySelector('.custom-size-width');
          const heightInput = valueContainer.querySelector('.custom-size-height');
          const customSizesList = valueContainer.querySelector('.custom-sizes-list');

          const length = parseInt(lengthInput.value);
          const width = parseInt(widthInput.value);
          const height = parseInt(heightInput.value);

          if (length && width && height && customSizesList) {
            const customSize = { length, width, height, custom: true };

            const customSizeElement = document.createElement('div');
            customSizeElement.className = 'custom-size-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
            customSizeElement.innerHTML = `
              <span class="text-gray-700">${length}×${width}×${height} мм <span class="text-xs text-gray-500">(польз.)</span></span>
              <button type="button" class="remove-custom-size text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
            `;
            customSizeElement.setAttribute('data-size', JSON.stringify(customSize));

            customSizeElement.querySelector('.remove-custom-size').addEventListener('click', function() {
              customSizeElement.remove();
              updateSavedValue();
            });

            customSizesList.appendChild(customSizeElement);

            // Очищаем поля ввода
            lengthInput.value = '';
            widthInput.value = '';
            heightInput.value = '';

            updateSavedValue();
          }
        });
      }

      // Обработчик для кнопки сохранения размеров
      const saveSizesBtn = valueContainer.querySelector('.save-sizes-btn');
      if (saveSizesBtn) {
        saveSizesBtn.addEventListener('click', function() {
          updateSavedValue();
          collapseAttributeRow(row, attributeType);
        });
      }
    } else if (attributeType === 'weight') {
      // Для чекбоксов весов
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSavedValue);
      });

      // Обработчик для добавления пользовательского веса
      const addCustomWeightBtn = valueContainer.querySelector('.add-custom-weight-btn');
      if (addCustomWeightBtn) {
        addCustomWeightBtn.addEventListener('click', function() {
          const valueInput = valueContainer.querySelector('.custom-weight-value');
          const unitSelect = valueContainer.querySelector('.custom-weight-unit');
          const customWeightsList = valueContainer.querySelector('.custom-weights-list');

          const value = parseFloat(valueInput.value);
          const unit = unitSelect.value;

          if (value && unit && customWeightsList) {
            const customWeight = { value, unit, custom: true };

            const customWeightElement = document.createElement('div');
            customWeightElement.className = 'custom-weight-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
            customWeightElement.innerHTML = `
              <span class="text-gray-700">${value} ${unit} <span class="text-xs text-gray-500">(польз.)</span></span>
              <button type="button" class="remove-custom-weight text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
            `;
            customWeightElement.setAttribute('data-weight', JSON.stringify(customWeight));

            customWeightElement.querySelector('.remove-custom-weight').addEventListener('click', function() {
              customWeightElement.remove();
              updateSavedValue();
            });

            customWeightsList.appendChild(customWeightElement);

            // Очищаем поля ввода
            valueInput.value = '';
            unitSelect.selectedIndex = 0;

            updateSavedValue();
          }
        });
      }

      // Обработчик для кнопки сохранения весов
      const saveWeightsBtn = valueContainer.querySelector('.save-weights-btn');
      if (saveWeightsBtn) {
        saveWeightsBtn.addEventListener('click', function() {
          updateSavedValue();
          collapseAttributeRow(row, attributeType);
        });
      }
    } else if (attributeType === 'material') {
      // Для чекбоксов материалов
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSavedValue);
      });

      // Обработчик для добавления пользовательского материала
      const addCustomMaterialBtn = valueContainer.querySelector('.add-custom-material-btn');
      if (addCustomMaterialBtn) {
        addCustomMaterialBtn.addEventListener('click', function() {
          const nameInput = valueContainer.querySelector('.custom-material-name');
          const customMaterialsList = valueContainer.querySelector('.custom-materials-list');

          const name = nameInput.value.trim();

          if (name && customMaterialsList) {
            const customMaterial = { name, custom: true };

            const customMaterialElement = document.createElement('div');
            customMaterialElement.className = 'custom-material-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
            customMaterialElement.innerHTML = `
              <span class="text-gray-700">${name} <span class="text-xs text-gray-500">(польз.)</span></span>
              <button type="button" class="remove-custom-material text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
            `;
            customMaterialElement.setAttribute('data-material', JSON.stringify(customMaterial));

            customMaterialElement.querySelector('.remove-custom-material').addEventListener('click', function() {
              customMaterialElement.remove();
              updateSavedValue();
            });

            customMaterialsList.appendChild(customMaterialElement);

            // Очищаем поле ввода
            nameInput.value = '';

            updateSavedValue();
          }
        });
      }

      // Обработчик для кнопки сохранения материалов
      const saveMaterialsBtn = valueContainer.querySelector('.save-materials-btn');
      if (saveMaterialsBtn) {
        saveMaterialsBtn.addEventListener('click', function() {
          updateSavedValue();
          collapseAttributeRow(row, attributeType);
        });
      }
    } else if (attributeType === 'color_pigments') {
      // Для чекбоксов цветовых пигментов
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSavedValue);
      });

      // Обработчик для кнопки сохранения пигментов
      const saveColorPigmentsBtn = valueContainer.querySelector('.save-color-pigments-btn');
      if (saveColorPigmentsBtn) {
        saveColorPigmentsBtn.addEventListener('click', function() {
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');

          if (selectedCheckboxes.length > 0) {
            collapseAttributeRow(row, 'color_pigments');
          } else {
            alert('Выберите хотя бы один пигмент');
          }
        });
      }
    } else {
      // Для селектов
      const selects = valueContainer.querySelectorAll('select');
      selects.forEach(select => {
        select.addEventListener('change', function() {
          updateSavedValue();

          // Автоматическое сворачивание для обычных селектов при выборе значения
          if (select.tagName === 'SELECT' && select.classList.contains('attribute-value-select') && select.value) {
            // Проверяем, что это не множественный выбор и не атрибуты, которые уже имеют свои кнопки сохранения
            const typeConfig = window.attributeTypes?.[attributeType];
            if (!select.multiple && !['colors', 'size'].includes(attributeType) && !typeConfig?.isSimpleArray) {
              setTimeout(() => {
                collapseAttributeRow(row, attributeType);
              }, 100);
            }
          }
        });
      });
    }
  }

  // Сворачиваем конкретную строку атрибута
  function collapseAttributeRow(row, attributeType) {
    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    // Сохраняем текущие значения атрибута перед сворачиванием
    const currentValue = getCurrentAttributeValue(row, attributeType);

    // Сохраняем значение в атрибуте data для последующего восстановления
    row.setAttribute('data-current-value', JSON.stringify(currentValue));

    // Получаем выбранные значения для отображения
    const selectedValues = getSelectedValuesText(row, attributeType);

    if (selectedValues) {
      valueContainer.innerHTML = `
        <div class="collapsed-view p-3 bg-gray-100 rounded border cursor-pointer" onclick="expandAttributeRow('${row.id}')">
          <div class="text-sm font-semibold text-gray-800">${selectedValues}</div>
          <div class="text-xs text-gray-500 mt-1">Нажмите для редактирования</div>
        </div>
      `;

      // Добавляем класс для отслеживания состояния
      row.classList.add('collapsed');
    }
  }

  // Получаем текст выбранных значений
  function getSelectedValuesText(row, attributeType) {
    if (attributeType === 'colors') {
      const checked = row.querySelectorAll('input[type="checkbox"]:checked');
      return Array.from(checked).map(cb => cb.value).join(', ');
    } else if (attributeType === 'size') {
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      const customSizes = row.querySelectorAll('.custom-size-item');

      const selectedSizes = [];

      // Добавляем предустановленные размеры
      checked.forEach(checkbox => {
        try {
          const size = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          selectedSizes.push(`${size.length}×${size.width}×${size.height} мм`);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      });

      // Добавляем пользовательские размеры
      customSizes.forEach(item => {
        try {
          const size = JSON.parse(item.getAttribute('data-size'));
          selectedSizes.push(`${size.length}×${size.width}×${size.height} мм (польз.)`);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      });

      return selectedSizes.length > 0 ? selectedSizes.join(', ') : null;
    } else if (attributeType === 'weight') {
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      const customWeights = row.querySelectorAll('.custom-weight-item');

      const selectedWeights = [];

      // Добавляем предустановленные веса
      checked.forEach(checkbox => {
        const weightText = checkbox.getAttribute('data-weight-text');
        if (weightText) {
          selectedWeights.push(weightText);
        }
      });

      // Добавляем пользовательские веса
      customWeights.forEach(item => {
        try {
          const weight = JSON.parse(item.getAttribute('data-weight'));
          selectedWeights.push(`${weight.value} ${weight.unit} (польз.)`);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      });

      return selectedWeights.length > 0 ? selectedWeights.join(', ') : null;
    } else if (attributeType === 'material') {
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      const customMaterials = row.querySelectorAll('.custom-material-item');

      const selectedMaterials = [];

      // Добавляем предустановленные материалы
      checked.forEach(checkbox => {
        try {
          const material = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          selectedMaterials.push(material.name);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      });

      // Добавляем пользовательские материалы
      customMaterials.forEach(item => {
        try {
          const material = JSON.parse(item.getAttribute('data-material'));
          selectedMaterials.push(`${material.name} (польз.)`);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      });

      return selectedMaterials.length > 0 ? selectedMaterials.join(', ') : null;
    } else if (attributeType === 'color_pigments') {
      // Цветовые пигменты - множественный выбор
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      const pigmentTexts = [];

      checked.forEach(checkbox => {
        try {
          const pigment = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          pigmentTexts.push(pigment.name);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return pigmentTexts.length > 0 ? pigmentTexts.join(', ') : null;
    } else if (['surfaces', 'patterns', 'strength_classes', 'frost_resistance', 'water_absorption'].includes(attributeType)) {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.value) {
        try {
          const item = JSON.parse(select.value.replace(/&quot;/g, '"'));
          if (item.name && item.description) {
            return `${item.name} - ${item.description}`;
          } else if (item.class && item.description) {
            return `${item.class} - ${item.description}`;
          } else {
            return item.name || item.class || select.value;
          }
        } catch (e) {
          const selectedOption = select.selectedOptions[0];
          return selectedOption?.textContent || '';
        }
      }
    } else {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.value) {
        try {
          // Пытаемся распарсить как JSON
          const item = JSON.parse(select.value.replace(/&quot;/g, '"'));
          if (item.name && item.description) {
            return `${item.name} - ${item.description}`;
          } else if (item.class && item.description) {
            return `${item.class} - ${item.description}`;
          } else {
            return item.name || item.class || select.value;
          }
        } catch (e) {
          // Если не JSON, берем текст опции
          const selectedOption = select.selectedOptions[0];
          return selectedOption?.textContent || '';
        }
      }
    }
    return null;
  }

  // Разворачиваем атрибут (глобальная функция для onclick)
  window.expandAttributeRow = async function(attributeId) {
    const row = document.getElementById(attributeId);
    if (!row) return;

    const typeSelect = row.querySelector('.attribute-type-select');
    if (typeSelect && typeSelect.value) {
      const attributeType = typeSelect.value;
      let currentValue = null;

      // Сначала пытаемся получить сохраненное значение из data-атрибута
      const savedValue = row.getAttribute('data-current-value');
      if (savedValue) {
        try {
          currentValue = JSON.parse(savedValue);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      }

      // Если нет сохраненного значения, берем из исходных данных товара
      if (!currentValue) {
        const product = window.currentProduct;
        if (product && product.attributes) {
          if (attributeType === 'size') {
            // Для размеров используем ключ 'size'
            currentValue = product.attributes.size;
            // Если это не массив, преобразуем в массив для единообразия обработки
            if (currentValue && !Array.isArray(currentValue)) {
              currentValue = [currentValue];
            }
          } else {
            // Для всех остальных атрибутов используем прямое соответствие
            currentValue = product.attributes[attributeType];
          }
        }
      }

      // Помечаем, что атрибут был развернут вручную
      row.setAttribute('data-manually-expanded', 'true');

      // Восстанавливаем полный интерфейс с текущими значениями
      await handleAttributeTypeChange(typeSelect, currentValue);
      row.classList.remove('collapsed');

      // УБИРАЕМ автоматическое сворачивание - пользователь должен иметь возможность редактировать
      // Форма остается развернутой для редактирования

      // Сворачиваем другие атрибуты
      collapseOtherAttributes(attributeId);
    }
  };

  // Обработчик изменения типа атрибута
  async function handleAttributeTypeChange(selectElement, preselectedValue = null) {
    const attributeType = selectElement.value;
    const attributeId = selectElement.dataset.attributeId;
    const row = document.getElementById(attributeId);



    if (!row || !attributeType) return;

    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    // Проверяем, загружены ли данные атрибутов
    if (!window.attributeTypes || !window.attributesData) {
      // Данные атрибутов не загружены
      valueContainer.innerHTML = '<p class="text-sm text-red-500">Ошибка: данные атрибутов не загружены</p>';
      return;
    }

    const typeConfig = window.attributeTypes[attributeType];
    const attributeData = window.attributesData[attributeType];



    if (!typeConfig || !attributeData) {
      // Ошибка: нет данных для атрибута
      valueContainer.innerHTML = '<p class="text-sm text-red-500">Ошибка загрузки данных атрибута</p>';
      return;
    }

    // Генерируем интерфейс в зависимости от типа атрибута
    generateAttributeInterface(valueContainer, attributeType, typeConfig, attributeData, preselectedValue);

    // Добавляем обработчики для отслеживания изменений в атрибуте
    addAttributeChangeListeners(row, attributeType);

    // Автоматически сворачиваем атрибут, если есть предустановленные значения
    // Но только при первоначальной загрузке (не при разворачивании)
    if (preselectedValue && !row.hasAttribute('data-manually-expanded') && (
      (attributeType === 'color_pigments' && Array.isArray(preselectedValue) && preselectedValue.length > 0) ||
      (attributeType === 'color_pigments' && !Array.isArray(preselectedValue) && preselectedValue.id)
    )) {
      setTimeout(() => {
        collapseAttributeRow(row, attributeType);
      }, 100);
    }
  }

  // Функция генерации интерфейса атрибута
  function generateAttributeInterface(valueContainer, attributeType, typeConfig, attributeData, preselectedValue) {


    if (typeConfig.isSimpleArray) {
      // Простой массив строк (например, текстуры)
      const selectedValues = Array.isArray(preselectedValue) ? preselectedValue : (preselectedValue ? [preselectedValue] : []);
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" multiple>
          ${attributeData.map(item => `<option value="${item}" ${selectedValues.includes(item) ? 'selected' : ''}>${item}</option>`).join('')}
        </select>
        <p class="text-xs text-gray-500 mt-1">Удерживайте Ctrl для выбора нескольких значений</p>
      `;
    } else if (attributeType === 'colors') {
      // Специальная обработка для цветов
      const selectedColors = Array.isArray(preselectedValue) ? preselectedValue : (preselectedValue ? [preselectedValue] : []);
      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${attributeData.map(color => `
            <label class="flex items-center">
              <input type="checkbox" value="${color.name}" ${selectedColors.includes(color.name) ? 'checked' : ''} class="attribute-checkbox mr-2" data-name="${color.name}">
              <div class="w-4 h-4 rounded border mr-2" style="background-color: ${color.hex}"></div>
              <span>${color.name}</span>
            </label>
          `).join('')}
        </div>
        <div class="mt-4 pt-3 border-t border-gray-200">
          <button type="button" class="save-colors-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить цвета</button>
        </div>
      `;
    } else if (attributeType === 'size') {
      // Специальная обработка для размеров - используем тот же интерфейс что и при создании
      let selectedSizes = [];
      if (Array.isArray(preselectedValue)) {
        selectedSizes = preselectedValue;
      } else if (preselectedValue && typeof preselectedValue === 'object') {
        // Если это один размер (объект), преобразуем в массив
        selectedSizes = [preselectedValue];
      }

      valueContainer.innerHTML = `
        <div class="space-y-4">
          <!-- Предустановленные размеры -->
          ${Object.entries(attributeData).map(([category, sizes]) => `
            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-2">${category}</h4>
              <div class="grid grid-cols-2 gap-2">
                ${sizes.map(size => {
                  const isSelected = selectedSizes.some(s => s.length === size.length && s.width === size.width && s.height === size.height);
                  return `
                    <label class="flex items-center">
                      <input type="checkbox" class="attribute-checkbox mr-3" value="${JSON.stringify(size).replace(/"/g, '&quot;')}" ${isSelected ? 'checked' : ''}>
                      <span class="text-sm font-normal">${size.length}×${size.width}×${size.height} мм</span>
                    </label>
                  `;
                }).join('')}
              </div>
            </div>
          `).join('')}

          <!-- Пользовательский размер -->
          <div class="pt-3 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Добавить пользовательское значение</h4>
            <div class="custom-size-section">
              <div class="space-y-2">
                <div class="custom-size-inputs grid grid-cols-3 gap-2">
                  <input type="number" min="1" max="10000" placeholder="Длина" class="custom-size-length px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" />
                  <input type="number" min="1" max="10000" placeholder="Ширина" class="custom-size-width px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" />
                  <input type="number" min="1" max="10000" placeholder="Высота" class="custom-size-height px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <button type="button" class="add-custom-size-btn w-full px-3 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 font-medium transition-colors">
                  + Добавить размер
                </button>
              </div>
              <div class="custom-sizes-list space-y-1 mt-2"></div>
            </div>
          </div>

          <!-- Кнопка сохранения -->
          <div class="pt-3 border-t border-gray-200">
            <button type="button" class="save-sizes-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить размеры</button>
          </div>
        </div>
      `;

      // Восстанавливаем пользовательские размеры
      const customSizes = selectedSizes.filter(size => size.custom);
      const customSizesList = valueContainer.querySelector('.custom-sizes-list');
      if (customSizesList && customSizes.length > 0) {
        customSizes.forEach(size => {
          const customSizeElement = document.createElement('div');
          customSizeElement.className = 'custom-size-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
          customSizeElement.innerHTML = `
            <span class="text-gray-700">${size.length}×${size.width}×${size.height} мм <span class="text-xs text-gray-500">(польз.)</span></span>
            <button type="button" class="remove-custom-size text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
          `;
          customSizeElement.setAttribute('data-size', JSON.stringify(size));

          // Добавляем обработчик удаления
          customSizeElement.querySelector('.remove-custom-size').addEventListener('click', function() {
            customSizeElement.remove();
          });

          customSizesList.appendChild(customSizeElement);
        });
      }
    } else if (attributeType === 'weight') {
      // Специальная обработка для веса - используем тот же интерфейс что и при создании
      let selectedWeights = [];
      if (Array.isArray(preselectedValue)) {
        selectedWeights = preselectedValue;
      } else if (preselectedValue && typeof preselectedValue === 'object') {
        // Если это один вес (объект), преобразуем в массив
        selectedWeights = [preselectedValue];
      }

      valueContainer.innerHTML = `
        <div class="space-y-4">
          <!-- Предустановленные веса -->
          <div class="grid grid-cols-2 gap-2">
            ${attributeData.map(item => {
              const isSelected = selectedWeights.some(w => w.value === item.value && w.unit === item.unit && !w.custom);
              return `
                <label class="flex items-center">
                  <input type="checkbox" class="attribute-checkbox mr-3" value="${JSON.stringify(item).replace(/"/g, '&quot;')}" data-weight-text="${item.value} ${item.unit}" ${isSelected ? 'checked' : ''}>
                  <span class="text-sm font-normal">${item.value} ${item.unit}</span>
                </label>
              `;
            }).join('')}
          </div>

          <!-- Пользовательский вес -->
          <div class="pt-3 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Добавить пользовательское значение</h4>
            <div class="space-y-2">
              <div class="grid grid-cols-2 gap-2">
                <input type="number" step="0.01" min="0" class="custom-weight-value px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" placeholder="Значение">
                <select class="custom-weight-unit px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                  <option value="г">г</option>
                  <option value="кг">кг</option>
                  <option value="т">т</option>
                  <option value="мг">мг</option>
                  <option value="ц">ц</option>
                </select>
              </div>
              <button type="button" class="add-custom-weight-btn w-full px-3 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 font-medium transition-colors">
                + Добавить значение
              </button>
            </div>
            <div class="custom-weights-list space-y-1 mt-2"></div>
          </div>

          <!-- Кнопка сохранения -->
          <div class="pt-3 border-t border-gray-200">
            <button type="button" class="save-weights-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить веса</button>
          </div>
        </div>
      `;

      // Восстанавливаем пользовательские веса
      const customWeights = selectedWeights.filter(weight => weight.custom);
      const customWeightsList = valueContainer.querySelector('.custom-weights-list');
      if (customWeightsList && customWeights.length > 0) {
        customWeights.forEach(weight => {
          const customWeightElement = document.createElement('div');
          customWeightElement.className = 'custom-weight-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
          customWeightElement.innerHTML = `
            <span class="text-gray-700">${weight.value} ${weight.unit} <span class="text-xs text-gray-500">(польз.)</span></span>
            <button type="button" class="remove-custom-weight text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
          `;
          customWeightElement.setAttribute('data-weight', JSON.stringify(weight));

          // Добавляем обработчик удаления
          customWeightElement.querySelector('.remove-custom-weight').addEventListener('click', function() {
            customWeightElement.remove();
          });

          customWeightsList.appendChild(customWeightElement);
        });
      }
    } else if (attributeType === 'material') {
      // Специальная обработка для материалов - используем тот же интерфейс что и при создании
      const selectedMaterials = Array.isArray(preselectedValue) ? preselectedValue : [];

      valueContainer.innerHTML = `
        <div class="space-y-4">
          <!-- Предустановленные материалы -->
          <div class="grid grid-cols-1 gap-2">
            ${attributeData.map(item => {
              const isSelected = selectedMaterials.some(m => m.name === item.name && !m.custom);
              return `
                <label class="flex items-center">
                  <input type="checkbox" class="attribute-checkbox mr-3" value="${JSON.stringify(item).replace(/"/g, '&quot;')}" ${isSelected ? 'checked' : ''}>
                  <span class="text-sm font-normal">${item.name}</span>
                </label>
              `;
            }).join('')}
          </div>

          <!-- Пользовательский материал -->
          <div class="pt-3 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Добавить пользовательское значение</h4>
            <div class="space-y-2">
              <input type="text" class="custom-material-name w-full px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" placeholder="Название материала">
              <button type="button" class="add-custom-material-btn w-full px-3 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 font-medium transition-colors">
                + Добавить материал
              </button>
            </div>
            <div class="custom-materials-list space-y-1 mt-2"></div>
          </div>

          <!-- Кнопка сохранения -->
          <div class="pt-3 border-t border-gray-200">
            <button type="button" class="save-materials-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить материалы</button>
          </div>
        </div>
      `;

      // Восстанавливаем пользовательские материалы
      const customMaterials = selectedMaterials.filter(material => material.custom);
      const customMaterialsList = valueContainer.querySelector('.custom-materials-list');
      if (customMaterialsList && customMaterials.length > 0) {
        customMaterials.forEach(material => {
          const customMaterialElement = document.createElement('div');
          customMaterialElement.className = 'custom-material-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
          customMaterialElement.innerHTML = `
            <span class="text-gray-700">${material.name} <span class="text-xs text-gray-500">(польз.)</span></span>
            <button type="button" class="remove-custom-material text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
          `;
          customMaterialElement.setAttribute('data-material', JSON.stringify(material));

          // Добавляем обработчик удаления
          customMaterialElement.querySelector('.remove-custom-material').addEventListener('click', function() {
            customMaterialElement.remove();
          });

          customMaterialsList.appendChild(customMaterialElement);
        });
      }
    } else if (attributeType === 'color_pigments') {
      // Цветовые пигменты - множественный выбор с чекбоксами
      const selectedPigments = Array.isArray(preselectedValue) ? preselectedValue : (preselectedValue ? [preselectedValue] : []);


      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${attributeData.map(pigment => {
            const isSelected = selectedPigments.some(selected => selected.id === pigment.id);
            return `
              <label class="flex items-start">
                <input type="checkbox" class="attribute-checkbox mr-3 mt-1" value="${JSON.stringify(pigment).replace(/"/g, '&quot;')}" data-id="${pigment.id}" data-name="${pigment.name}" ${isSelected ? 'checked' : ''}>
                <div class="flex-1">
                  <span class="text-sm font-semibold">${pigment.name}</span>
                  <p class="text-xs text-gray-600 mt-1">${pigment.description}</p>
                </div>
              </label>
            `;
          }).join('')}
        </div>
        <div class="mt-4 pt-3 border-t border-gray-200">
          <button type="button" class="save-color-pigments-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить пигменты</button>
        </div>
      `;

      // Добавляем обработчик для кнопки "Сохранить пигменты"
      const saveColorPigmentsBtn = valueContainer.querySelector('.save-color-pigments-btn');
      if (saveColorPigmentsBtn) {
        saveColorPigmentsBtn.addEventListener('click', function() {
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');

          if (selectedCheckboxes.length > 0) {
            collapseAttributeRow(row, 'color_pigments');
          } else {
            alert('Выберите хотя бы один пигмент');
          }
        });
      }
    } else if (['surfaces', 'patterns'].includes(attributeType)) {
      // Атрибуты с id, name и description (одиночный выбор)
      const selectedId = preselectedValue && typeof preselectedValue === 'object' ? preselectedValue.id : preselectedValue;


      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите ${window.attributeTypes[attributeType].name.toLowerCase()}</option>
          ${attributeData.map(item => `<option value='${JSON.stringify(item)}' ${selectedId === item.id ? 'selected' : ''}>${item.name} - ${item.description}</option>`).join('')}
        </select>
      `;
    } else {
      // Обработка пользовательских типов атрибутов
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => {
            const itemValue = typeof item === 'object' ? JSON.stringify(item) : item;
            const displayValue = typeof item === 'object' ? (item.name || item.value || JSON.stringify(item)) : item;
            const isSelected = JSON.stringify(preselectedValue) === JSON.stringify(item);
            return `<option value="${itemValue.replace(/"/g, '&quot;')}" ${isSelected ? 'selected' : ''}>${displayValue}</option>`;
          }).join('')}
        </select>
      `;
    }
  }

  // Функция сбора данных атрибутов из форм
  function collectAttributesData() {
    const attributes = {};

    document.querySelectorAll('.attribute-row').forEach(row => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const attributeType = typeSelect?.value;

      if (!attributeType) return;

      let attributeValue = null;

      // Если атрибут свернут, берем значение из data-атрибута
      if (row.classList.contains('collapsed')) {
        const savedValue = row.getAttribute('data-current-value');
        if (savedValue) {
          try {
            attributeValue = JSON.parse(savedValue);
          } catch (e) {
            // Игнорируем ошибки парсинга
          }
        }
      } else {
        // Если атрибут развернут, собираем данные из формы
        const valueContainer = row.querySelector('.attribute-value-content');
        if (!valueContainer) return;

        // Используем функцию getCurrentAttributeValue для правильного сбора данных
        attributeValue = getCurrentAttributeValue(row, attributeType);

        if (attributeValue === null) {
          // Если getCurrentAttributeValue не смогла собрать данные, пробуем альтернативные методы
          const select = valueContainer.querySelector('.attribute-value-select');
          if (select) {
            if (select.multiple) {
              // Множественный выбор
              attributeValue = Array.from(select.selectedOptions).map(option => option.value);
            } else {
              // Одиночный выбор
              const selectedValue = select.value;
              if (selectedValue) {
                try {
                  // Пытаемся распарсить JSON для сложных объектов
                  attributeValue = JSON.parse(selectedValue.replace(/&quot;/g, '"'));
                } catch {
                  // Если не JSON, используем как строку
                  attributeValue = selectedValue;
                }
              }
            }
          }
        }
      }

      if (attributeValue !== null && attributeValue !== undefined) {
        // Для размеров используем ключ 'size' (как в исходных данных)
        if (attributeType === 'size') {
          // Сохраняем под ключом 'size'
          attributes.size = attributeValue;
        } else {
          // Для всех остальных атрибутов используем их тип как ключ
          attributes[attributeType] = attributeValue;
        }
      }
    });

    return attributes;
  }

  // Обработчик отправки формы
  document.getElementById('product-form').addEventListener('submit', async (e) => {
    e.preventDefault();

    // Сбор данных формы
    const productId = document.getElementById('product-id').value;
    const name = document.getElementById('name').value;
    const slug = document.getElementById('product-slug').value;
    const category = document.getElementById('category').value;
    const subcategory = document.getElementById('subcategory').value;
    const shortDescription = document.getElementById('shortDescription').value;
    const fullDescription = document.getElementById('fullDescription').value;
    const productType = document.getElementById('product-type').value;
    const currency = document.getElementById('currency').value;
    const price = parseFloat(document.getElementById('basePrice').value);
    const unit = document.getElementById('unit').value;
    const sku = document.getElementById('sku').value;
    // Сбор данных атрибутов из динамических форм
    const attributes = collectAttributesData();

    // Собираем данные вариантов товара
    let productVariants = [];
    try {
      if (window.collectVariantsData) {
        productVariants = window.collectVariantsData();

      }
    } catch (error) {
      // Ошибка при сборе данных вариантов
      if (window.adminModal) {
        await window.adminModal.showError('Ошибка при обработке вариантов товара');
      } else {
        alert('Ошибка при обработке вариантов товара');
      }
      return;
    }

    const inStock = document.getElementById('inStock').checked;
    const status = document.getElementById(`status-input-${productId}`).value || 'draft';

    // Проверяем, есть ли варианты товара с ценами
    const hasVariantsWithPricing = window.hasVariantsWithPricing ? window.hasVariantsWithPricing() : false;

    // Проверяем, есть ли вариант с основной ценой
    const primaryPriceVariants = productVariants.filter(variant => variant.isPrimaryPrice);
    const hasPrimaryPriceVariant = primaryPriceVariants.length > 0;

    // Валидация: должен быть только один вариант с основной ценой
    if (primaryPriceVariants.length > 1) {
      await window.adminModal?.showError('Ошибка: найдено несколько вариантов с пометкой "Основная цена". Должен быть только один.');
      return;
    }

    if (!hasVariantsWithPricing && !hasPrimaryPriceVariant) {
      // Если нет вариантов с ценами, базовая цена обязательна
      if (!currency) {
        await window.adminModal?.showError('Пожалуйста, выберите валюту для базовой цены');
        return;
      }
      if (!unit) {
        await window.adminModal?.showError('Пожалуйста, выберите единицу измерения для базовой цены');
        return;
      }
      if (isNaN(price) || price <= 0) {
        if (window.adminModal) {
          await window.adminModal.showError('Пожалуйста, укажите корректную базовую цену товара');
        } else {
          alert('Пожалуйста, укажите корректную базовую цену товара');
        }
        return;
      }
    }

    // Получаем текущие данные товара из глобальной переменной
    const currentProduct = window.currentProduct;

    // Получение правильного categorySlug из данных категорий
    // Загружаем данные категорий для получения правильного slug
    let categorySlug = category.toLowerCase().replace(/\s+/g, '-');
    try {
      const categoriesResponse = await fetch('/data/product/categories.json');
      const categoriesData = await categoriesResponse.json();
      const foundCategory = categoriesData.categories.find(cat => cat.name === category);
      if (foundCategory) {
        categorySlug = foundCategory.slug;
      }
    } catch (error) {
      // Не удалось загрузить данные категорий, используется fallback slug
    }

    // Обновляем данные изображений перед сохранением
    updateProductImages();

    // Определяем базовую цену
    let basePrice = null;

    // Ищем вариант, помеченный как основная цена
    const primaryPriceVariant = productVariants.find(variant => variant.isPrimaryPrice);

    if (primaryPriceVariant) {
      // Если есть вариант с основной ценой, используем его
      basePrice = {
        value: primaryPriceVariant.price.value,
        currency: primaryPriceVariant.price.currency,
        unit: primaryPriceVariant.price.unit
      };
    } else if (!hasVariantsWithPricing || (hasVariantsWithPricing && !primaryPriceVariant)) {
      // Если нет вариантов с ценами ИЛИ есть варианты, но ни один не помечен как основной, используем базовую цену
      basePrice = {
        value: price,
        currency,
        unit
      };
    }

    // Формирование объекта товара
    const productData = {
      id: productId,
      sku,
      name,
      slug,
      category,
      categorySlug,
      subcategory,
      shortDescription,
      fullDescription,
      productType,
      basePrice: basePrice,
      attributes: attributes,
      variants: productVariants, // Добавляем варианты товара
      images: window.currentProduct.images, // Используем актуальные данные изображений
      inStock,
      popularity: currentProduct.popularity || 4.0,
      status
    };

    try {


      // Отправка данных на сервер
      const response = await fetch('/api/admin/products', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      });



      if (response.ok) {
        await response.json();

        await window.adminModal?.showSuccess('Товар успешно обновлен!');
        window.location.href = '/admin/products';
      } else {
        const errorData = await response.json();
        // Ошибка сервера
        await window.adminModal?.showError('Ошибка при обновлении товара: ' + (errorData.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      // Ошибка при обновлении товара
      alert('Ошибка при обновлении товара: ' + error.message);
    }
  });

  // Обработчик автоматического определения изображений
  document.getElementById('auto-detect-images').addEventListener('click', async () => {
    const productId = document.getElementById('product-id').value;

    try {
      const response = await fetch(`/api/admin/detect-images?productId=${productId}`);
      const result = await response.json();

      if (result.success && result.images) {
        // Обновляем главное изображение
        if (result.images.main) {
          const mainImg = document.getElementById('current-main-image');
          mainImg.src = `/product/${result.images.main}`;

          // Обновляем текст с именем файла
          const mainImageContainer = mainImg.parentElement.parentElement;
          const fileNameText = mainImageContainer.querySelector('p.text-xs');
          if (fileNameText) {
            fileNameText.textContent = `Текущий файл: ${result.images.main}`;
          }
        }

        // Обновляем дополнительные изображения
        const additionalGrid = document.getElementById('additional-images-grid');
        additionalGrid.innerHTML = '';

        result.images.additional.forEach((img, index) => {
          const imgContainer = document.createElement('div');
          imgContainer.className = 'relative';
          imgContainer.setAttribute('data-image-path', img);

          imgContainer.innerHTML = `
            <img
              src="/product/${img}"
              alt="Изображение ${index + 1}"
              class="h-20 w-full object-cover rounded border"
            />
            <button
              type="button"
              data-index="${index}"
              data-image-path="${img}"
              class="delete-image absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
            >
              ×
            </button>
            <p class="text-xs text-gray-500 mt-1 truncate">${img.split('/').pop()}</p>
          `;

          additionalGrid.appendChild(imgContainer);
        });

        // Переназначаем обработчики удаления
        attachDeleteHandlers();

        await window.adminModal?.showSuccess('Изображения успешно обновлены!');
      } else {
        await window.adminModal?.showError('Не удалось определить изображения: ' + (result.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      // Ошибка при определении изображений
      await window.adminModal?.showError('Произошла ошибка при определении изображений. Попробуйте еще раз.');
    }
  });

  // Функция для назначения обработчиков удаления изображений
  function attachDeleteHandlers() {
    document.querySelectorAll('.delete-image').forEach(button => {
      button.addEventListener('click', async () => {
        const confirmed = await window.confirmModal?.show({
          title: 'Подтверждение удаления',
          message: 'Вы уверены, что хотите удалить это изображение?',
          confirmText: 'Удалить',
          cancelText: 'Отмена',
          confirmButtonClass: 'bg-red-100 hover:bg-red-200 text-red-700 border border-red-300'
        });

        if (confirmed) {
          const imagePath = button.getAttribute('data-image-path');
          const container = button.closest('.relative');

          try {
            // Удаляем файл с сервера
            const response = await fetch(`/api/admin/delete-image?imagePath=${encodeURIComponent(imagePath)}`, {
              method: 'DELETE'
            });

            const result = await response.json();

            if (result.success) {
              // Удаляем элемент из DOM
              container.remove();

              // Обновляем данные товара
              updateProductImages();

              // Изображение успешно удалено
            } else {
              alert('Ошибка при удалении изображения: ' + (result.error || 'Неизвестная ошибка'));
            }
          } catch (error) {
            // Ошибка при удалении изображения
            alert('Ошибка при удалении изображения');
          }
        }
      });
    });
  }

  // Обработчик загрузки главного изображения
  document.getElementById('main-image').addEventListener('change', async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const productId = document.getElementById('product-id').value;
    const productSlug = document.getElementById('product-slug').value; // Получаем SLUG товара
    const formData = new FormData();
    formData.append('file', file);
    formData.append('productId', productId);
    formData.append('productSlug', productSlug); // Передаем SLUG для генерации имени файла
    formData.append('imageType', 'main');

    try {
      const response = await fetch('/api/admin/upload-image', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        // Обновляем главное изображение
        const mainImg = document.getElementById('current-main-image');
        mainImg.src = `/product/${result.imagePath}?t=${Date.now()}`;

        // Обновляем текст с именем файла
        const fileNameText = mainImg.parentElement.parentElement.querySelector('p.text-xs');
        if (fileNameText) {
          fileNameText.textContent = `Текущий файл: ${result.imagePath}`;
        }

        // Обновляем данные товара
        window.currentProduct.images.main = result.imagePath;

        alert('Главное изображение успешно загружено!');
      } else {
        alert('Ошибка при загрузке изображения: ' + (result.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      // Ошибка при загрузке изображения
      alert('Ошибка при загрузке изображения');
    }

    // Очищаем input
    event.target.value = '';
  });

  // Обработчик загрузки дополнительных изображений
  document.getElementById('additional-images').addEventListener('change', async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    const productId = document.getElementById('product-id').value;
    const productSlug = document.getElementById('product-slug').value; // Получаем SLUG товара

    for (const file of files) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('productId', productId);
      formData.append('productSlug', productSlug); // Передаем SLUG для генерации имени файла
      formData.append('imageType', 'additional');

      try {
        const response = await fetch('/api/admin/upload-image', {
          method: 'POST',
          body: formData
        });

        const result = await response.json();

        if (result.success) {
          // Добавляем новое изображение в сетку
          addImageToGrid(result.imagePath);

          // Обновляем данные товара
          if (!window.currentProduct.images.additional) {
            window.currentProduct.images.additional = [];
          }
          window.currentProduct.images.additional.push(result.imagePath);

          // Дополнительное изображение загружено
        } else {
          alert('Ошибка при загрузке изображения ' + file.name + ': ' + (result.error || 'Неизвестная ошибка'));
        }
      } catch (error) {
        // Ошибка при загрузке изображения
        alert('Ошибка при загрузке изображения ' + file.name);
      }
    }

    // Очищаем input
    event.target.value = '';
    alert('Загрузка дополнительных изображений завершена!');
  });

  // Функция для добавления изображения в сетку
  function addImageToGrid(imagePath) {
    const grid = document.getElementById('additional-images-grid');
    const index = grid.children.length;

    const imgContainer = document.createElement('div');
    imgContainer.className = 'relative';
    imgContainer.setAttribute('data-image-path', imagePath);

    imgContainer.innerHTML = `
      <img
        src="/product/${imagePath}?t=${Date.now()}"
        alt="Изображение ${index + 1}"
        class="h-20 w-full object-cover rounded border"
      />
      <button
        type="button"
        data-index="${index}"
        data-image-path="${imagePath}"
        class="delete-image absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
      >
        ×
      </button>
      <p class="text-xs text-gray-500 mt-1 truncate">${imagePath.split('/').pop()}</p>
    `;

    grid.appendChild(imgContainer);

    // Переназначаем обработчики удаления
    attachDeleteHandlers();
  }

  // Функция для обновления данных изображений товара
  function updateProductImages() {
    const mainImg = document.getElementById('current-main-image');
    const mainImagePath = mainImg.src.split('/product/')[1]?.split('?')[0];

    if (mainImagePath) {
      window.currentProduct.images.main = mainImagePath;
    }

    // Собираем дополнительные изображения из DOM
    const additionalImages = [];
    document.querySelectorAll('#additional-images-grid .relative').forEach(container => {
      const imagePath = container.getAttribute('data-image-path');
      if (imagePath) {
        additionalImages.push(imagePath);
      }
    });

    window.currentProduct.images.additional = additionalImages;
  }

  // Инициализируем обработчики удаления при загрузке страницы
  attachDeleteHandlers();

  // Функция генерации SLUG с транслитерацией
  function generateSlugFromName(name) {
    if (!name) return '';

    // Маппинг кириллических символов в латинские для SLUG
    const cyrillicToLatin = {
      'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
      'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
      'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
      'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
      'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
      'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
      'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts',
      'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    };

    // Транслитерация кириллицы в латиницу
    let transliterated = '';
    for (let i = 0; i < name.length; i++) {
      const char = name[i];
      transliterated += cyrillicToLatin[char] || char;
    }

    // Создание SLUG: приведение к нижнему регистру, замена пробелов на дефисы, удаление недопустимых символов
    return transliterated
      .toLowerCase()
      .replace(/\s+/g, '-')           // Заменяем пробелы на дефисы
      .replace(/[^a-z0-9-]/g, '')     // Удаляем все символы кроме букв, цифр и дефисов
      .replace(/--+/g, '-')           // Заменяем множественные дефисы на одинарные
      .replace(/^-+/g, '')            // Удаляем дефисы в начале
      .replace(/-+$/g, '');           // Удаляем дефисы в конце
  }

  // Обработчик кнопки генерации SLUG
  document.getElementById('generate-product-slug-btn').addEventListener('click', function() {
    const nameInput = document.getElementById('name');
    const productSlugInput = document.getElementById('product-slug');

    if (nameInput.value) {
      const slug = generateSlugFromName(nameInput.value);
      productSlugInput.value = slug;
    }
  });

  document.addEventListener('DOMContentLoaded', function() {
    const priceIcon = document.querySelector('.price-info-icon');
    const tooltip = document.getElementById('price-warning-tooltip');
    if (!priceIcon || !tooltip) return;
    priceIcon.addEventListener('mouseenter', function() {
      tooltip.classList.add('show');
    });
    priceIcon.addEventListener('mouseleave', function() {
      tooltip.classList.remove('show');
    });
    priceIcon.addEventListener('focus', function() {
      tooltip.classList.add('show');
    });
    priceIcon.addEventListener('blur', function() {
      tooltip.classList.remove('show');
    });
  });
</script>
