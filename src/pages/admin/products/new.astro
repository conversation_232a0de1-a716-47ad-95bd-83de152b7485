---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';
import ProductVariants from '../../../components/admin/ProductVariants.astro';
import ProductStatusManager from '../../../components/admin/ProductStatusManager.astro';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных о категориях для выпадающего списка
import categoriesFile from '../../../../data/product/categories.json';
// Фильтруем только активные категории (activeForProducts = true)
const categoriesData = categoriesFile.categories.filter(cat => cat.activeForProducts !== false);

// Загрузка данных об атрибутах
import attributesFile from '../../../../data/product/attributes.json';
import attributeTypesConfig from '../../../../data/product/attribute-types-config.json';
const attributesData = attributesFile;
const attributeTypes = attributeTypesConfig;
const attributeTypesFullConfig = attributeTypesConfig;

// Загрузка настроек товаров
import settingsData from '../../../../data/product/settings-product.json';
---

<AdminLayout title="Добавить товар | LuxBeton">
  <div class="container mx-auto py-8 px-4">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Добавить новый товар</h1>
      <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded hover:bg-gray-400">
        Назад к списку
      </a>
    </div>

    <form id="product-form" class="bg-white rounded-lg shadow-md p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Основная информация -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Основная информация</h2>

          <div class="mb-4">
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Название товара</label>
            <input
              type="text"
              id="name"
              name="name"
              placeholder="Введите название товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              required
              autocomplete="off"
            />
          </div>

          <div class="mb-4">
            <label for="product-slug" class="block text-sm font-medium text-gray-700 mb-1">SLUG (URL товара)</label>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="product-slug"
                name="product-slug"
                placeholder="url-slug-tovara"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
                autocomplete="off"
              />
              <button
                type="button"
                id="generate-product-slug-btn"
                class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 border border-blue-300 rounded-md transition-colors"
                title="Генерировать SLUG из названия товара с транслитерацией"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">Автоматически генерируется из названия или нажмите кнопку для ручной генерации</p>
          </div>

          <div class="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Категория</label>
              <select
                id="category"
                name="category"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
                autocomplete="off"
              >
                <option value="">Выберите категорию</option>
                {categoriesData.map(cat => (
                  <option value={cat.name} data-category-id={cat.id}>{cat.name}</option>
                ))}
              </select>
              <div id="category-info" class="text-xs text-gray-500 mt-1 hidden">
                <span id="category-id-prefix" class="font-medium"></span>
                <span> - префикс для ID товаров этой категории</span>
              </div>
            </div>
            <div>
              <label for="subcategory" class="block text-sm font-medium text-gray-700 mb-1">Подкатегория</label>
              <select
                id="subcategory"
                name="subcategory"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                disabled
                autocomplete="off"
              >
                <option value="">Сначала выберите категорию</option>
              </select>
            </div>
          </div>

          <div class="mb-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="id" class="block text-sm font-medium text-gray-700 mb-1">ID товара</label>
              <div class="flex items-center space-x-2">
                <input
                  type="text"
                  id="id"
                  name="id"
                  placeholder="Будет сгенерирован автоматически"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-gray-50"
                  readonly
                  required
                  autocomplete="off"
                />
                <button
                  type="button"
                  id="generate-id-btn"
                  class="hidden"
                  tabindex="-1"
                  aria-hidden="true"
                >
                  Генерировать
                </button>
              </div>
              <p class="text-xs text-gray-500 mt-1">ID будет автоматически сгенерирован при выборе категории</p>
            </div>
            <div>
              <label for="sku" class="block text-sm font-medium text-gray-700 mb-1">SKU (артикул)</label>
              <div class="flex items-center space-x-2">
                <input
                  type="text"
                  id="sku"
                  name="sku"
                  placeholder="Будет сгенерирован автоматически из ID, можно изменить вручную"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                  autocomplete="off"
                />
                <button
                  type="button"
                  id="generate-sku-btn"
                  class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 border border-blue-300 rounded-md transition-colors"
                  title="Генерировать SKU на основе ID"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                </button>
              </div>
              <p class="text-xs text-gray-500 mt-1">SKU генерируется автоматически на основе ID, но вы можете изменить его вручную. SKU должен быть уникальным.</p>
            </div>
          </div>

          <div class="mb-4">
            <label for="shortDescription" class="block text-sm font-medium text-gray-700 mb-1">Краткое описание</label>
            <textarea
              id="shortDescription"
              name="shortDescription"
              placeholder="Краткое описание товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows="2"
              required
              autocomplete="off"
            ></textarea>
          </div>

          <div class="mb-4">
            <label for="fullDescription" class="block text-sm font-medium text-gray-700 mb-1">Полное описание</label>
            <textarea
              id="fullDescription"
              name="fullDescription"
              placeholder="Подробное описание товара"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              rows="5"
              autocomplete="off"
            ></textarea>
          </div>
        </div>

        <!-- Цена и атрибуты -->
        <div>
          <h2 class="text-xl font-semibold mb-4">Цена и атрибуты</h2>

          <!-- Тип товара -->
          <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <label for="product-type" class="block text-sm font-medium text-gray-700 mb-1">
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              Тип товара
            </label>
            <select
              id="product-type"
              name="product-type"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
              required
              autocomplete="off"
            >
              <!-- Динамически заполняется JavaScript -->
            </select>
            <p class="text-xs text-blue-600 mt-1">
              <svg class="w-3 h-3 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
              Тип товара определяет доступные единицы измерения
            </p>
          </div>





          <!-- Компонент выбора атрибутов -->
          <div id="attributes-section" class="mb-4">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Атрибуты товара</h3>
            <div id="attributes-container" class="space-y-4">
              <!-- Атрибуты будут добавлены динамически -->
            </div>
            <button
              type="button"
              id="add-attribute-btn"
              class="mt-3 inline-flex items-center px-3 py-2 border border-blue-300 text-sm font-medium rounded text-blue-700 bg-blue-50 hover:bg-blue-100 transition-colors"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Добавить атрибут
            </button>
          </div>

          <!-- Варианты товара -->
          <ProductVariants
            settingsData={settingsData}
            attributesData={attributesData}
            attributeTypes={attributeTypes}
          />

          <!-- Цена товара для покупателя -->
          <div class="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
              <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              Цена товара
              <!-- Иконка с всплывающей подсказкой -->
              <div class="relative ml-2">
                <svg class="w-4 h-4 text-gray-400 hover:text-gray-600 cursor-help" fill="currentColor" viewBox="0 0 20 20" id="price-info-icon">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
                <!-- Всплывающая подсказка -->
                <div id="price-tooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg shadow-lg opacity-0 pointer-events-none transition-opacity duration-200 whitespace-nowrap z-10">
                  <div class="text-center">
                    <div class="font-semibold text-blue-300">Базовая цена отключена</div>
                    <div class="mt-1">Цены указаны в вариантах товара. Базовая цена не требуется.</div>
                  </div>
                  <!-- Стрелка -->
                  <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"></div>
                </div>
              </div>
            </h3>

            <!-- Компактная форма с тремя полями в ряд -->
            <div class="grid grid-cols-3 gap-3 mb-4">
              <!-- Цена -->
              <div>
                <label for="basePrice" class="block text-sm font-medium text-gray-700 mb-1">
                  Цена
                </label>
                <input
                  type="number"
                  id="basePrice"
                  name="basePrice"
                  placeholder="0.00"
                  step="0.01"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  autocomplete="off"
                />
              </div>

              <!-- Валюта (символ) -->
              <div>
                <label for="currency" class="block text-sm font-medium text-gray-700 mb-1">
                  Валюта
                </label>
                <select
                  id="currency"
                  name="currency"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                  autocomplete="off"
                >
                  <!-- Динамически заполняется JavaScript -->
                </select>
              </div>

              <!-- Единица измерения -->
              <div>
                <label for="unit" class="block text-sm font-medium text-gray-700 mb-1">
                  За единицу
                </label>
                <select
                  id="unit"
                  name="unit"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white"
                  autocomplete="off"
                >
                  <option value="">Сначала выберите тип товара</option>
                </select>
              </div>
            </div>

            <!-- Интерактивное табло цены для покупателя -->
            <div id="customer-price-display" class="p-4 bg-white border-2 border-blue-300 rounded-lg shadow-sm">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 flex items-center">
                  <svg class="w-4 h-4 mr-1 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  Цена для покупателя:
                </span>
                <span id="price-source-indicator" class="text-xs px-2 py-1 rounded-full bg-gray-100 text-gray-600">
                  <!-- Источник цены -->
                </span>
              </div>
              <div id="customer-price-text" class="text-2xl font-bold text-blue-700 mb-2">
                0 ₽ за шт
              </div>
              <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                <div class="flex items-start">
                  <svg class="w-3 h-3 mr-1 mt-0.5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                  </svg>
                  <div id="price-explanation">
                    <strong>Эту цену увидят покупатели на сайте.</strong><br>
                    Приоритет: Основная цена из вариантов → Базовая цена → 0
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Статус товара -->
          <div class="mb-4">
            <ProductStatusManager
              currentStatus="draft"
              productId="new-product"
              showLabel={true}
              size="md"
            />
            <p class="text-xs text-gray-500 mt-1">
              Новые товары создаются со статусом "Черновик" и не отображаются на сайте до публикации.
            </p>
          </div>

          <!-- Наличие на складе -->
          <div class="mb-4">
            <label for="inStock" class="flex items-center">
              <input
                type="checkbox"
                id="inStock"
                class="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                checked
              />
              <span class="ml-2 text-sm text-gray-700">В наличии на складе</span>
            </label>
          </div>
        </div>
      </div>

      <!-- Изображения -->
      <div class="mt-6">
        <h2 class="text-xl font-semibold mb-4">Изображения</h2>

        <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
          <h3 class="text-sm font-medium text-blue-800 mb-2">Правила именования изображений:</h3>
          <ul class="text-xs text-blue-700 space-y-1">
            <li>• Главное изображение: будет названо как <code>название-товара_main.jpg</code></li>
            <li>• Дополнительные: будут названы как <code>название-товара_1.jpg</code>, <code>название-товара_2.jpg</code> и т.д.</li>
            <li>• Поддерживаются форматы: JPG, PNG</li>
          </ul>
        </div>

        <div class="mb-4">
          <label for="main-image" class="block text-sm font-medium text-gray-700 mb-1">Главное изображение</label>
          <input
            type="file"
            id="main-image"
            name="main-image"
            accept="image/*"
            class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <div id="main-image-preview" class="mt-2 hidden">
            <img id="main-image-preview-img" src="" alt="Предпросмотр главного изображения" class="h-20 w-20 object-cover rounded border">
          </div>
        </div>

        <div class="mb-4">
          <label for="additional-images" class="block text-sm font-medium text-gray-700 mb-1">Дополнительные изображения (до 6 штук)</label>
          <input
            type="file"
            id="additional-images"
            name="additional-images"
            accept="image/*"
            multiple
            class="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          <div id="additional-images-preview" class="mt-2 hidden">
            <div class="grid grid-cols-6 gap-2" id="additional-images-grid"></div>
          </div>
          <!-- Предпросмотр дополнительных изображений будет добавлен динамически -->
        </div>
      </div>

      <div class="mt-8 flex justify-end">
        <a href="/admin/products" class="bg-gray-300 text-gray-800 px-4 py-2 rounded mr-2 hover:bg-gray-400">
          Отмена
        </a>
        <button type="submit" class="create-product-btn text-white px-4 py-2 rounded" style="background-color: #3b82f6;">
          Создать товар
        </button>
      </div>
    </form>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки создания продукта */
  .create-product-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации ID */
  .generate-id-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации SLUG */
  .generate-product-slug-btn:hover {
    background-color: #2563eb !important;
  }

  /* ... существующие стили ... */
  .price-info-icon:hover ~ #price-warning-tooltip,
  .price-info-group:hover #price-warning-tooltip {
    opacity: 1;
    pointer-events: auto;
  }
  #price-warning-tooltip.show {
    opacity: 1;
    pointer-events: auto;
  }
</style>

<script define:vars={{ attributesData, attributeTypes, attributeTypesFullConfig, settingsData }} is:inline>
  // Данные атрибутов
  window.attributesData = attributesData;
  window.attributeTypes = attributeTypes;
  window.attributeTypesFullConfig = attributeTypesFullConfig;

  // Настройки товаров
  window.settingsData = settingsData;

  // Импорт утилит для работы с атрибутами и вариантами
  import('/src/utils/attributeManager.js').then(() => {
    // Функции атрибутов доступны через window
  });

  import('/src/utils/variantManager.js').then(() => {
    // Функции вариантов доступны через window
    // Инициализируем компонент вариантов после загрузки DOM
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        if (window.initializeVariantsComponent) {
          window.initializeVariantsComponent();
        }
        // Инициализируем состояние полей базовой цены
        if (window.toggleBasePriceFields) {
          window.toggleBasePriceFields();
        }
      });
    } else {
      // DOM уже загружен
      if (window.initializeVariantsComponent) {
        window.initializeVariantsComponent();
      }
      // Инициализируем состояние полей базовой цены
      if (window.toggleBasePriceFields) {
        window.toggleBasePriceFields();
      }
    }
  });

  // Обработчик отправки формы
  function initializeFormHandler() {
    const form = document.getElementById('product-form');
    if (!form) {
      return;
    }

    form.addEventListener('submit', async (e) => {
    e.preventDefault();

    // Сбор данных формы
    const id = document.getElementById('id').value;
    const name = document.getElementById('name').value;
    const slug = document.getElementById('product-slug').value;
    const category = document.getElementById('category').value;
    const subcategory = document.getElementById('subcategory').value;
    const shortDescription = document.getElementById('shortDescription').value;
    const fullDescription = document.getElementById('fullDescription').value;
    const productType = document.getElementById('product-type').value;
    const currency = document.getElementById('currency').value;
    const sku = document.getElementById('sku').value;

    // Проверка обязательных полей
    if (!id || !name || !category) {
      if (window.adminModal) {
        await window.adminModal.showError('Пожалуйста, заполните все обязательные поля (ID, название, категория)');
      } else {
        alert('Пожалуйста, заполните все обязательные поля (ID, название, категория)');
      }
      return;
    }

    // Собираем данные вариантов товара ДО проверки hasVariants
    let productVariants = [];
    try {
      productVariants = window.collectVariantsData();
    } catch (error) {
      if (window.adminModal) {
        await window.adminModal.showError('Ошибка при обработке вариантов товара');
      } else {
        alert('Ошибка при обработке вариантов товара');
      }
      return;
    }

    // Проверяем, есть ли варианты товара с ценами
    const hasVariantsWithPricing = window.hasVariantsWithPricing ? window.hasVariantsWithPricing() : false;

    // Проверяем, есть ли вариант с основной ценой
    const primaryPriceVariants = productVariants.filter(variant => variant.isPrimaryPrice);
    const hasPrimaryPriceVariant = primaryPriceVariants.length > 0;

    // Валидация: должен быть только один вариант с основной ценой
    if (primaryPriceVariants.length > 1) {
      if (window.adminModal) {
        await window.adminModal.showError('Ошибка: найдено несколько вариантов с пометкой "Основная цена". Должен быть только один.');
      } else {
        alert('Ошибка: найдено несколько вариантов с пометкой "Основная цена". Должен быть только один.');
      }
      return;
    }

    // Если нет вариантов с ценами И нет варианта с основной ценой, базовая цена обязательна
    const price = parseFloat(document.getElementById('basePrice').value);
    const unit = document.getElementById('unit').value;

    if (!hasVariantsWithPricing && !hasPrimaryPriceVariant) {
      // Проверяем обязательные поля базовой цены
      if (isNaN(price) || price <= 0) {
        if (window.adminModal) {
          await window.adminModal.showError('Пожалуйста, укажите корректную базовую цену товара');
        } else {
          alert('Пожалуйста, укажите корректную базовую цену товара');
        }
        return;
      }

      if (!currency) {
        if (window.adminModal) {
          await window.adminModal.showError('Пожалуйста, выберите валюту для базовой цены');
        } else {
          alert('Пожалуйста, выберите валюту для базовой цены');
        }
        return;
      }

      if (!unit) {
        if (window.adminModal) {
          await window.adminModal.showError('Пожалуйста, выберите единицу измерения для базовой цены');
        } else {
          alert('Пожалуйста, выберите единицу измерения для базовой цены');
        }
        return;
      }
    }
    const inStock = document.getElementById('inStock').checked;
    const status = document.getElementById('status-input-new-product').value || 'draft';

    // Валидация новых полей
    if (!productType) {
      if (window.adminModal) {
        await window.adminModal.showError('Пожалуйста, выберите тип товара');
      } else {
        alert('Пожалуйста, выберите тип товара');
      }
      return;
    }


    // Собираем данные атрибутов из нового компонента
    let selectedAttributes = {};
    try {
      selectedAttributes = collectAttributesData();
    } catch (error) {
      if (window.adminModal) {
        await window.adminModal.showError('Ошибка при обработке атрибутов товара');
      } else {
        alert('Ошибка при обработке атрибутов товара');
      }
      return;
    }

    // Получение правильного categorySlug из данных категорий
    // Загружаем данные категорий для получения правильного slug
    let categorySlug = category.toLowerCase().replace(/\s+/g, '-');
    try {
      const categoriesResponse = await fetch('/data/product/categories.json');
      const categoriesData = await categoriesResponse.json();
      const foundCategory = categoriesData.categories.find(cat => cat.name === category);
      if (foundCategory) {
        categorySlug = foundCategory.slug;
      }
    } catch (error) {
      // Используется fallback slug
    }

    // Используем только выбранные пользователем атрибуты
    const finalAttributes = selectedAttributes;

    // Определяем базовую цену
    let basePrice = null;

    // Ищем вариант, помеченный как основная цена
    const primaryPriceVariant = productVariants.find(variant => variant.isPrimaryPrice);

    if (primaryPriceVariant) {
      // Если есть вариант с основной ценой, используем его
      basePrice = {
        value: primaryPriceVariant.price.value,
        currency: primaryPriceVariant.price.currency,
        unit: primaryPriceVariant.price.unit
      };
    } else if (!hasVariantsWithPricing || (hasVariantsWithPricing && !primaryPriceVariant)) {
      // Если нет вариантов с ценами ИЛИ есть варианты, но ни один не помечен как основной, используем базовую цену
      basePrice = {
        value: price,
        currency,
        unit
      };
    }

    const productData = {
      id,
      sku,
      name,
      slug,
      category,
      categorySlug,
      subcategory,
      shortDescription,
      fullDescription,
      productType,
      basePrice: basePrice,
      attributes: finalAttributes,
      variants: productVariants,
      images: {
        main: `${id}/${slug}_main.jpg`,
        additional: []
      },
      inStock,
      popularity: 4.0,
      status
    };

    try {
      // Сначала создаем товар
      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        // Загружаем изображения, если они выбраны
        const uploadedImages = await uploadProductImages(id, slug);

        // Обновляем данные товара с реальными путями к изображениям
        if (uploadedImages.main || uploadedImages.additional.length > 0) {
          const updatedProductData = {
            ...productData,
            images: {
              main: uploadedImages.main || productData.images.main,
              additional: uploadedImages.additional
            }
          };

          // Обновляем товар в JSON файле
          const updateResponse = await fetch('/api/admin/products', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(updatedProductData)
          });

          if (!updateResponse.ok) {
            await updateResponse.text();
            // Ошибка при обновлении путей к изображениям
          }
        }

        await window.adminModal.showSuccess('Товар успешно создан!');
        window.location.href = '/admin/products';
      } else {
        const error = await response.json();
        await window.adminModal.showError('Ошибка при создании товара: ' + (error.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      await window.adminModal.showError('Произошла ошибка при создании товара. Проверьте подключение к интернету и попробуйте еще раз.');
    }
    });
  }

  // Функция для загрузки изображений товара
  async function uploadProductImages(productId, productSlug) {
    const mainImageInput = document.getElementById('main-image');
    const additionalImagesInput = document.getElementById('additional-images');

    const uploadedImages = {
      main: null,
      additional: []
    };

    // Загружаем главное изображение
    if (mainImageInput.files && mainImageInput.files[0]) {
      const result = await uploadSingleImage(mainImageInput.files[0], productId, productSlug, true);
      if (result && result.success) {
        uploadedImages.main = result.path;
      }
    }

    // Загружаем дополнительные изображения
    if (additionalImagesInput.files) {
      for (let i = 0; i < Math.min(additionalImagesInput.files.length, 6); i++) {
        const result = await uploadSingleImage(additionalImagesInput.files[i], productId, productSlug, false);
        if (result && result.success) {
          uploadedImages.additional.push(result.path);
        }
      }
    }

    return uploadedImages;
  }

  // Функция для загрузки одного изображения
  async function uploadSingleImage(file, productId, productSlug, isMain) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('productId', productId);
    formData.append('productName', productSlug); // Передаем SLUG товара для генерации имени файла
    formData.append('isMain', isMain.toString());

    try {
      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (!result.success) {
        return null;
      } else {
        return result;
      }
    } catch (error) {
      return null;
    }
  }

  // Инициализация предпросмотра изображений
  function initializeImagePreviews() {
    // Предпросмотр главного изображения
    const mainImageInput = document.getElementById('main-image');
    if (mainImageInput) {
      mainImageInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function(e) {
        const preview = document.getElementById('main-image-preview');
        const img = document.getElementById('main-image-preview-img');
        img.src = e.target.result;
        preview.classList.remove('hidden');
      };
        reader.readAsDataURL(file);
      }
      });
    }

    // Предпросмотр дополнительных изображений
    const additionalImagesInput = document.getElementById('additional-images');
    if (additionalImagesInput) {
      additionalImagesInput.addEventListener('change', function(e) {
    const files = Array.from(e.target.files).slice(0, 6); // Максимум 6 изображений
    const preview = document.getElementById('additional-images-preview');
    const grid = document.getElementById('additional-images-grid');

    // Очищаем предыдущий предпросмотр
    grid.innerHTML = '';

    if (files.length > 0) {
      preview.classList.remove('hidden');

      files.forEach((file, index) => {
        const reader = new FileReader();
        reader.onload = function(e) {
          const imgContainer = document.createElement('div');
          imgContainer.className = 'relative';

          const img = document.createElement('img');
          img.src = e.target.result;
          img.alt = `Дополнительное изображение ${index + 1}`;
          img.className = 'h-16 w-16 object-cover rounded border';

          imgContainer.appendChild(img);
          grid.appendChild(imgContainer);
        };
        reader.readAsDataURL(file);
      });
      } else {
        preview.classList.add('hidden');
      }
      });
    }
  }

  // Инициализация обработчиков событий
  function initializeEventHandlers() {
    // Автоматическая генерация ID товара при выборе категории
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
      categorySelect.addEventListener('change', async function(e) {
    const selectedCategory = e.target.value;
    const selectedOption = e.target.selectedOptions[0];
    const categoryId = selectedOption ? selectedOption.getAttribute('data-category-id') : null;

    const idInput = document.getElementById('id');
    const generateBtn = document.getElementById('generate-id-btn');
    const categoryInfo = document.getElementById('category-info');
    const categoryIdPrefix = document.getElementById('category-id-prefix');
    const subcategorySelect = document.getElementById('subcategory');

    if (selectedCategory && categoryId) {
      // Показываем информацию о категории
      categoryIdPrefix.textContent = categoryId;
      categoryInfo.classList.remove('hidden');

      // Включаем кнопку генерации
      generateBtn.disabled = false;

      // Автоматически генерируем ID
      await generateProductId(selectedCategory);

      // Загружаем подкатегории для выбранной категории
      await loadSubcategories(selectedCategory);
    } else {
      // Скрываем информацию и очищаем поля
      categoryInfo.classList.add('hidden');
      generateBtn.disabled = true;
      idInput.value = '';

      // Очищаем и отключаем выбор подкатегории
      subcategorySelect.innerHTML = '<option value="">Сначала выберите категорию</option>';
      subcategorySelect.disabled = true;
      }
      });
    }

    // Обработчик кнопки генерации ID
    const generateIdBtn = document.getElementById('generate-id-btn');
    if (generateIdBtn) {
      generateIdBtn.addEventListener('click', async function() {
        const category = document.getElementById('category').value;
        if (category) {
          await generateProductId(category);
        }
      });
    }

    // Автоматическая генерация SLUG при вводе названия товара
    const nameInput = document.getElementById('name');
    if (nameInput) {
      nameInput.addEventListener('input', function() {
        const productSlugInput = document.getElementById('product-slug');
        if (!productSlugInput.value) { // Генерируем только если поле пустое
          const slug = generateSlugFromName(this.value);
          productSlugInput.value = slug;
        }
      });
    }

    // Обработчик кнопки генерации SLUG
    const generateSlugBtn = document.getElementById('generate-product-slug-btn');
    if (generateSlugBtn) {
      generateSlugBtn.addEventListener('click', function() {
        const nameInput = document.getElementById('name');
        const productSlugInput = document.getElementById('product-slug');

        if (nameInput.value) {
          const slug = generateSlugFromName(nameInput.value);
          productSlugInput.value = slug;
        }
      });
    }

    // В initializeEventHandlers добавить обработчик для generate-sku-btn
    const generateSkuBtn = document.getElementById('generate-sku-btn');
    if (generateSkuBtn) {
      generateSkuBtn.addEventListener('click', function() {
        const idInput = document.getElementById('id');
        const skuInput = document.getElementById('sku');
        if (idInput && skuInput && idInput.value) {
          skuInput.value = idInput.value;
        }
      });
    }
  }

  // Функция генерации ID товара
  async function generateProductId(categoryName) {
    const idInput = document.getElementById('id');
    const generateBtn = document.getElementById('generate-id-btn');

    try {
      // Показываем состояние загрузки
      generateBtn.textContent = 'Генерация...';
      generateBtn.disabled = true;
      idInput.value = 'Генерируется...';

      const response = await fetch(`/api/admin/generate-product-id?category=${encodeURIComponent(categoryName)}`);
      const result = await response.json();

      if (result.success) {
        idInput.value = result.productId;
      } else {
        alert('Ошибка при генерации ID: ' + (result.error || 'Неизвестная ошибка'));
        idInput.value = '';
      }
    } catch (error) {
      alert('Ошибка при генерации ID товара');
      idInput.value = '';
    } finally {
      // Восстанавливаем состояние кнопки
      generateBtn.textContent = 'Генерировать';
      generateBtn.disabled = false;
    }
  }

  // Функция генерации SLUG с транслитерацией
  function generateSlugFromName(name) {
    if (!name) return '';

    // Маппинг кириллических символов в латинские для SLUG
    const cyrillicToLatin = {
      'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
      'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
      'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
      'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
      'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
      'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
      'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts',
      'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    };

    // Транслитерация кириллицы в латиницу
    let transliterated = '';
    for (let i = 0; i < name.length; i++) {
      const char = name[i];
      transliterated += cyrillicToLatin[char] || char;
    }

    // Создание SLUG: приведение к нижнему регистру, замена пробелов на дефисы, удаление недопустимых символов
    return transliterated
      .toLowerCase()
      .replace(/\s+/g, '-')           // Заменяем пробелы на дефисы
      .replace(/[^a-z0-9-]/g, '')     // Удаляем все символы кроме букв, цифр и дефисов
      .replace(/--+/g, '-')           // Заменяем множественные дефисы на одинарные
      .replace(/^-+/g, '')            // Удаляем дефисы в начале
      .replace(/-+$/g, '');           // Удаляем дефисы в конце
  }



  // Функция для загрузки подкатегорий
  async function loadSubcategories(categoryName) {
    const subcategorySelect = document.getElementById('subcategory');

    try {
      // Загружаем данные категорий
      const response = await fetch('/data/product/categories.json');
      const categoriesData = await response.json();

      // Находим выбранную категорию
      const selectedCategory = categoriesData.categories.find(cat => cat.name === categoryName);

      if (selectedCategory && selectedCategory.subcategories) {
        // Очищаем текущие опции
        subcategorySelect.innerHTML = '<option value="">Выберите подкатегорию</option>';

        // Добавляем подкатегории
        selectedCategory.subcategories.forEach(subcategory => {
          const option = document.createElement('option');
          option.value = subcategory;
          option.textContent = subcategory;
          subcategorySelect.appendChild(option);
        });

        // Включаем выбор подкатегории
        subcategorySelect.disabled = false;
      } else {
        // Если подкатегорий нет
        subcategorySelect.innerHTML = '<option value="">Подкатегории отсутствуют</option>';
        subcategorySelect.disabled = true;
      }
    } catch (error) {
      subcategorySelect.innerHTML = '<option value="">Ошибка загрузки подкатегорий</option>';
      subcategorySelect.disabled = true;
    }
  }

  // Управление атрибутами
  let attributeCounter = 0;



  // Инициализация компонента атрибутов
  document.addEventListener('DOMContentLoaded', function() {
    initializeFormHandler();
    initializeImagePreviews();
    initializeEventHandlers();
    initializeAttributesComponent();
    initializeProductSettings();
    initializePriceTooltip();
  });

  // Функция для инициализации всплывающей подсказки
  function initializePriceTooltip() {
    const icon = document.getElementById('price-info-icon');
    const tooltip = document.getElementById('price-tooltip');

    if (!icon || !tooltip) return;

    icon.addEventListener('mouseenter', () => {
      tooltip.classList.remove('opacity-0', 'pointer-events-none');
      tooltip.classList.add('opacity-100');
    });

    icon.addEventListener('mouseleave', () => {
      tooltip.classList.remove('opacity-100');
      tooltip.classList.add('opacity-0', 'pointer-events-none');
    });
  }

  function initializeAttributesComponent() {
    const addAttributeBtn = document.getElementById('add-attribute-btn');
    if (addAttributeBtn) {
      addAttributeBtn.addEventListener('click', addAttributeRow);
    }
  }



  // Инициализация настроек товаров
  function initializeProductSettings() {
    initializeProductTypes();
    initializeCurrencies();
    initializeUnits();
    initializeCustomerPriceDisplay();
  }

  // Инициализация типов товаров
  function initializeProductTypes() {
    const productTypeSelect = document.getElementById('product-type');
    if (!productTypeSelect || !window.settingsData) return;

    // Заполняем селект типов товаров
    productTypeSelect.innerHTML = '';

    // Добавляем опцию по умолчанию
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Выберите тип товара';
    defaultOption.selected = true;
    productTypeSelect.appendChild(defaultOption);

    window.settingsData.product_types.supported.forEach(type => {
      const option = document.createElement('option');
      option.value = type.key;
      option.textContent = `${type.label.ru} - ${type.description.ru}`;
      productTypeSelect.appendChild(option);
    });

    // Обработчик изменения типа товара
    productTypeSelect.addEventListener('change', function() {
      updateAvailableUnits(this.value);
    });

    // Не инициализируем единицы, пока не выбран тип товара
  }

  // Инициализация валют
  function initializeCurrencies() {
    const currencySelect = document.getElementById('currency');
    if (!currencySelect || !window.settingsData) return;

    // Заполняем селект валют
    currencySelect.innerHTML = '';

    // Добавляем опцию по умолчанию
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Выберите валюту';
    defaultOption.selected = true;
    currencySelect.appendChild(defaultOption);

    window.settingsData.currencies.supported.forEach(currency => {
      const option = document.createElement('option');
      option.value = currency.key;
      option.textContent = `${currency.key} - ${currency.label.ru}`;
      currencySelect.appendChild(option);
    });
  }

  // Инициализация единиц измерения
  function initializeUnits() {
    const unitSelect = document.getElementById('unit');
    if (!unitSelect) return;

    // Изначально единицы недоступны до выбора типа товара
    unitSelect.innerHTML = '<option value="">Сначала выберите тип товара</option>';
    unitSelect.disabled = true;
  }

  // Обновление доступных единиц измерения в зависимости от типа товара
  function updateAvailableUnits(productType) {
    const unitSelect = document.getElementById('unit');
    if (!unitSelect || !window.settingsData) return;

    // Если тип товара не выбран, блокируем селект единиц измерения
    if (!productType || productType === '') {
      unitSelect.innerHTML = '<option value="">Сначала выберите тип товара</option>';
      unitSelect.disabled = true;
      return;
    }

    // Определяем какие единицы измерения подходят для данного типа товара
    let relevantUnitTypes = [];

    switch (productType) {
      case 'physical':
        relevantUnitTypes = ['weight', 'volume', 'dimensions', 'countable'];
        break;
      case 'digital':
        relevantUnitTypes = ['countable'];
        break;
      case 'service':
        relevantUnitTypes = ['service'];
        break;
      default:
        relevantUnitTypes = ['weight', 'volume', 'dimensions', 'countable', 'service'];
    }

    // Очищаем селект
    unitSelect.innerHTML = '';
    unitSelect.disabled = false;

    // Добавляем опцию по умолчанию
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = 'Выберите единицу измерения (за что указывается цена)';
    unitSelect.appendChild(defaultOption);

    // Добавляем единицы измерения для каждого подходящего типа
    relevantUnitTypes.forEach(unitType => {
      if (window.settingsData.units[unitType] && window.settingsData.units[unitType].supported) {
        // Добавляем заголовок группы
        const optgroup = document.createElement('optgroup');
        optgroup.label = getUnitTypeLabel(unitType);

        window.settingsData.units[unitType].supported.forEach(unit => {
          const option = document.createElement('option');
          option.value = unit.key;
          option.textContent = `за ${unit.label.ru} (${unit.key})`;
          option.dataset.unitLabel = unit.label.ru;

          // Выбираем основную единицу по умолчанию для первого типа
          if (unitType === relevantUnitTypes[0] && unit.key === window.settingsData.units[unitType].primary) {
            option.selected = true;
          }

          optgroup.appendChild(option);
        });

        unitSelect.appendChild(optgroup);
      }
    });

    // Обновляем предварительный просмотр цены если есть выбранная единица
    if (unitSelect.value) {
      updateCustomerPrice();
    }
  }

  // Получить название типа единицы измерения
  function getUnitTypeLabel(unitType) {
    const labels = {
      'weight': 'Вес',
      'volume': 'Объем',
      'dimensions': 'Размеры',
      'countable': 'Штучные',
      'service': 'Услуги'
    };
    return labels[unitType] || unitType;
  }

  // Инициализация отображения цены для покупателя
  function initializeCustomerPriceDisplay() {
    const priceInput = document.getElementById('basePrice');
    const currencySelect = document.getElementById('currency');
    const unitSelect = document.getElementById('unit');

    if (priceInput && currencySelect && unitSelect) {
      // Добавляем обработчики событий для обновления цены покупателя
      priceInput.addEventListener('input', updateCustomerPrice);
      currencySelect.addEventListener('change', updateCustomerPrice);
      unitSelect.addEventListener('change', updateCustomerPrice);

      // Показываем цену покупателя при загрузке
      updateCustomerPrice();
    }

    // Добавляем обработчик для изменений в вариантах
    if (typeof window.addVariantChangeListener === 'function') {
      window.addVariantChangeListener(updateCustomerPrice);
    }
  }

  // Получение цены из вариантов с основной ценой
  function getPrimaryPriceFromVariants() {
    // Пробуем получить варианты из компонента вариантов
    if (typeof window.getProductVariants === 'function') {
      try {
        const variants = window.getProductVariants();
        if (Array.isArray(variants)) {
          const primaryVariant = variants.find(variant => variant.isPrimaryPrice === true && variant.price);
          if (primaryVariant && primaryVariant.price) {
            return primaryVariant.price;
          }
        }
      } catch (error) {
        // Игнорируем ошибки компонента вариантов
      }
    }

    return null;
  }

  // Получение символа валюты
  function getCurrencySymbol(currencyCode) {
    if (!window.variantSettingsData?.currencies?.supported) return currencyCode;

    const currency = window.variantSettingsData.currencies.supported.find(c => c.key === currencyCode);
    return currency?.simvol || currencyCode;
  }

  // Получение названия единицы измерения
  function getUnitLabel(unitCode) {
    const unitSelect = document.getElementById('unit');
    if (unitSelect) {
      const selectedOption = Array.from(unitSelect.options).find(option => option.value === unitCode);
      return selectedOption?.dataset.unitLabel || selectedOption?.textContent || unitCode;
    }
    return unitCode;
  }

  // Обновление цены для покупателя
  function updateCustomerPrice() {
    const customerPriceDisplay = document.getElementById('customer-price-display');
    const customerPriceText = document.getElementById('customer-price-text');
    const priceSourceIndicator = document.getElementById('price-source-indicator');
    const priceExplanation = document.getElementById('price-explanation');

    if (!customerPriceDisplay || !customerPriceText || !priceSourceIndicator) return;

    let finalPrice = null;
    let priceSource = '';
    let explanation = '';

    // 1. Проверяем основную цену из вариантов (приоритет 1)
    const primaryPrice = getPrimaryPriceFromVariants();
    if (primaryPrice && primaryPrice.value > 0) {
      finalPrice = primaryPrice;
      priceSource = 'Основная из вариантов';
      explanation = '<strong>Цена взята из варианта, помеченного как основная.</strong><br>Покупатели увидят эту цену на сайте.';
    } else {
      // 2. Проверяем базовую цену (приоритет 2)
      const priceInput = document.getElementById('basePrice');
      const currencySelect = document.getElementById('currency');
      const unitSelect = document.getElementById('unit');

      if (priceInput && currencySelect && unitSelect) {
        const basePrice = parseFloat(priceInput.value) || 0;
        const currency = currencySelect.value;
        const unit = unitSelect.value;

        if (basePrice > 0 && currency && unit) {
          finalPrice = {
            value: basePrice,
            currency: currency,
            unit: unit
          };
          priceSource = 'Базовая цена';
          explanation = '<strong>Цена взята из базовой цены товара.</strong><br>Покупатели увидят эту цену на сайте.';
        }
      }
    }

    // 3. Если нет ни основной, ни базовой цены (приоритет 3)
    if (!finalPrice) {
      finalPrice = { value: 0, currency: '₽', unit: 'шт' };
      priceSource = 'Не указана';
      explanation = '<strong>Цена не указана.</strong><br>Укажите базовую цену или создайте вариант с основной ценой.';
    }

    // Форматируем и отображаем цену
    const currencySymbol = getCurrencySymbol(finalPrice.currency);
    const unitLabel = getUnitLabel(finalPrice.unit);
    const formattedPrice = finalPrice.value.toFixed(2);

    customerPriceText.innerHTML = `${formattedPrice} ${currencySymbol} за ${unitLabel}`;
    priceSourceIndicator.textContent = priceSource;

    if (priceExplanation) {
      priceExplanation.innerHTML = explanation;
    }

    // Меняем стиль в зависимости от источника цены
    customerPriceDisplay.className = 'p-4 border-2 rounded-lg shadow-sm';
    if (finalPrice.value === 0) {
      customerPriceDisplay.classList.add('bg-red-50', 'border-red-300');
      customerPriceText.className = 'text-2xl font-bold text-red-700 mb-2';
    } else if (priceSource === 'Основная из вариантов') {
      customerPriceDisplay.classList.add('bg-green-50', 'border-green-300');
      customerPriceText.className = 'text-2xl font-bold text-green-700 mb-2';
    } else {
      customerPriceDisplay.classList.add('bg-blue-50', 'border-blue-300');
      customerPriceText.className = 'text-2xl font-bold text-blue-700 mb-2';
    }

    // Управляем видимостью всплывающей подсказки
    updatePriceTooltipVisibility(priceSource);
  }

  // Функция для управления видимостью всплывающей подсказки
  function updatePriceTooltipVisibility(priceSource) {
    const icon = document.getElementById('price-info-icon');
    const tooltip = document.getElementById('price-tooltip');

    if (!icon || !tooltip) return;

    // Показываем подсказку только если есть основная цена из вариантов
    if (priceSource === 'Основная из вариантов') {
      icon.style.display = 'block';
    } else {
      icon.style.display = 'none';
      // Скрываем подсказку если она была открыта
      tooltip.classList.remove('opacity-100');
      tooltip.classList.add('opacity-0', 'pointer-events-none');
    }
  }

  function addAttributeRow() {
    const container = document.getElementById('attributes-container');
    if (!container) return;

    // Сворачиваем все существующие атрибуты перед добавлением нового
    collapseAllAttributes();

    const attributeId = `attribute-${++attributeCounter}`;
    const attributeRow = document.createElement('div');
    attributeRow.className = 'attribute-row bg-gray-50 p-4 rounded-lg border';
    attributeRow.id = attributeId;

    attributeRow.innerHTML = `
      <div class="attribute-header grid grid-cols-1 md:grid-cols-12 gap-4">
        <div class="md:col-span-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">Тип атрибута</label>
          <select class="attribute-type-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" data-attribute-id="${attributeId}">
            <option value="">Выберите тип атрибута</option>
            ${Object.entries(window.attributeTypes).map(([key, config]) =>
              `<option value="${key}">${config.name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="attribute-value-container md:col-span-6">
          <label class="block text-sm font-medium text-gray-700 mb-1">Значение</label>
          <div class="attribute-value-content">
            <p class="text-sm text-gray-500">Сначала выберите тип атрибута</p>
          </div>
        </div>
        <div class="md:col-span-2 flex items-end justify-end">
          <button type="button" class="remove-attribute-btn px-3 py-2 bg-red-100 hover:bg-red-200 text-red-700 border border-red-300 rounded text-sm" data-attribute-id="${attributeId}">
            удалить
          </button>
        </div>
      </div>
    `;

    container.appendChild(attributeRow);

    // Добавляем обработчики событий
    const typeSelect = attributeRow.querySelector('.attribute-type-select');
    const removeBtn = attributeRow.querySelector('.remove-attribute-btn');

    if (typeSelect) {
      typeSelect.addEventListener('change', function() {
        handleAttributeTypeChange(this);
      });

      // Добавляем обработчик для сворачивания других атрибутов при фокусе
      typeSelect.addEventListener('focus', function() {
        collapseOtherAttributes(attributeId);
      });
    }

    if (removeBtn) {
      removeBtn.addEventListener('click', function() {
        removeAttributeRow(this.dataset.attributeId);
      });
    }

    // Добавляем обработчик для сворачивания при клике на значения
    const valueContainer = attributeRow.querySelector('.attribute-value-container');
    if (valueContainer) {
      valueContainer.addEventListener('click', function() {
        collapseOtherAttributes(attributeId);
      });
    }
  }

  function handleAttributeTypeChange(selectElement) {
    const attributeType = selectElement.value;
    const attributeId = selectElement.dataset.attributeId;
    const row = document.getElementById(attributeId);

    if (!row || !attributeType) return;

    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    const typeConfig = window.attributeTypes[attributeType];
    const attributeData = window.attributesData[attributeType];

    if (!typeConfig || !attributeData) {
      valueContainer.innerHTML = '<p class="text-sm text-red-500">Ошибка загрузки данных атрибута</p>';
      return;
    }

    // Генерируем интерфейс в зависимости от типа атрибута
    if (typeConfig.isSimpleArray) {
      // Простой массив строк (например, текстуры)
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" multiple>
          ${attributeData.map(item => `<option value="${item}">${item}</option>`).join('')}
        </select>
        <p class="text-xs text-gray-500 mt-1">Удерживайте Ctrl для выбора нескольких значений</p>
        <div class="mt-4 pt-3 border-t border-gray-200">
          <button type="button" class="save-multiple-values-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить значения</button>
        </div>
      `;

      // Добавляем обработчик для кнопки "Сохранить значения"
      const saveMultipleBtn = valueContainer.querySelector('.save-multiple-values-btn');
      if (saveMultipleBtn) {
        saveMultipleBtn.addEventListener('click', function() {
          const select = valueContainer.querySelector('.attribute-value-select');
          const selectedOptions = select ? Array.from(select.selectedOptions) : [];

          if (selectedOptions.length > 0) {
            collapseAttributeRow(row, attributeType);
          } else {
            alert('Выберите хотя бы одно значение');
          }
        });
      }

      // Восстанавливаем сохраненные значения для множественного выбора после установки обработчиков
      setTimeout(() => {
        restoreMultipleSelectState(row, valueContainer);
      }, 100);
    } else if (attributeType === 'colors') {
      // Специальная обработка для цветов
      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${attributeData.map(color => `
            <label class="flex items-center">
              <input type="checkbox" class="attribute-checkbox mr-3" value="${color.id}" data-name="${color.name}">
              <div class="w-5 h-5 rounded border mr-3" style="background-color: ${color.hex}"></div>
              <span class="text-sm font-semibold">${color.name}</span>
            </label>
          `).join('')}
        </div>
        <div class="mt-4 pt-3 border-t border-gray-200">
          <button type="button" class="save-colors-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить цвета</button>
        </div>
      `;

      // Добавляем обработчик для кнопки "Сохранить цвета"
      const saveColorsBtn = valueContainer.querySelector('.save-colors-btn');
      if (saveColorsBtn) {
        saveColorsBtn.addEventListener('click', function() {
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');

          if (selectedCheckboxes.length > 0) {
            collapseAttributeRow(row, 'colors');
          } else {
            alert('Выберите хотя бы один цвет');
          }
        });
      }

      // Восстанавливаем сохраненные значения цветов после установки обработчиков
      setTimeout(() => {
        restoreColorsState(row, valueContainer);
      }, 100);
    } else if (attributeType === 'size') {
      // Генерируем интерфейс с предустановленными размерами и опцией ручного ввода (множественный выбор)
      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${Object.entries(attributeData).map(([category, sizes]) => `
            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-1">${category}</h4>
              ${sizes.map((size) => `
                <label class="flex items-center mb-1">
                  <input type="checkbox" class="attribute-checkbox mr-3" value="${JSON.stringify(size).replace(/"/g, '&quot;')}" data-category="${category}" data-size-text="${size.length}×${size.width}×${size.height} мм">
                  <span class="text-sm font-normal">${size.length}×${size.width}×${size.height} мм</span>
                </label>
              `).join('')}
            </div>
          `).join('')}
          <!-- Пользовательский размер -->
          <div class="pt-3 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Добавить пользовательское значение</h4>
            <div class="custom-size-section">
              <div class="space-y-2">
                <div class="custom-size-inputs grid grid-cols-3 gap-2">
                  <input type="number" min="1" max="10000" placeholder="Длина" class="custom-size-length px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" />
                  <input type="number" min="1" max="10000" placeholder="Ширина" class="custom-size-width px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" />
                  <input type="number" min="1" max="10000" placeholder="Высота" class="custom-size-height px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <button type="button" class="add-custom-size-btn w-full px-3 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 font-medium transition-colors">
                  + Добавить размер
                </button>
              </div>
              <div class="custom-sizes-list space-y-1 mt-2"></div>
            </div>
          </div>
          <div class="mt-4 pt-3 border-t border-gray-200">
            <button type="button" class="save-sizes-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить размеры</button>
          </div>
        </div>
      `;
      // JS: обработка для множественного выбора размеров
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      const customInputs = valueContainer.querySelector('.custom-size-inputs');
      const addCustomSizeBtn = valueContainer.querySelector('.add-custom-size-btn');
      const customSizesList = valueContainer.querySelector('.custom-sizes-list');
      const saveSizesBtn = valueContainer.querySelector('.save-sizes-btn');

      // Обработчик для предустановленных размеров (без автосворачивания)
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          // Убираем автосворачивание, чтобы можно было выбрать несколько размеров
        });
      });

      // Обработчик для добавления пользовательского размера
      if (addCustomSizeBtn && customInputs && customSizesList) {
        addCustomSizeBtn.addEventListener('click', function() {
          const lengthInput = customInputs.querySelector('.custom-size-length');
          const widthInput = customInputs.querySelector('.custom-size-width');
          const heightInput = customInputs.querySelector('.custom-size-height');

          const length = parseInt(lengthInput.value);
          const width = parseInt(widthInput.value);
          const height = parseInt(heightInput.value);

          if (length > 0 && width > 0 && height > 0) {
            // Создаем элемент для отображения добавленного размера
            const customSizeElement = document.createElement('div');
            customSizeElement.className = 'custom-size-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
            customSizeElement.innerHTML = `
              <span class="text-gray-700">${length}×${width}×${height} мм <span class="text-xs text-gray-500">(польз.)</span></span>
              <button type="button" class="remove-custom-size text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
            `;

            // Сохраняем данные в data-атрибуте
            customSizeElement.setAttribute('data-size', JSON.stringify({ length, width, height, custom: true }));

            customSizesList.appendChild(customSizeElement);

            // Очищаем поля ввода
            lengthInput.value = '';
            widthInput.value = '';
            heightInput.value = '';

            // Обработчик удаления
            customSizeElement.querySelector('.remove-custom-size').addEventListener('click', function() {
              customSizeElement.remove();
            });

            // Убираем автосворачивание, чтобы можно было добавить еще размеры
          } else {
            alert('Пожалуйста, заполните все поля размера корректными значениями (больше 0)');
          }
        });
      }

      // Обработчик для кнопки "Сохранить размеры"
      if (saveSizesBtn) {
        saveSizesBtn.addEventListener('click', function() {
          // Проверяем, что выбран хотя бы один размер
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');
          const customSizes = valueContainer.querySelectorAll('.custom-size-item');

          if (selectedCheckboxes.length > 0 || customSizes.length > 0) {
            collapseAttributeRow(row, 'size');
          } else {
            alert('Выберите хотя бы один размер или добавьте пользовательский размер');
          }
        });
      }

      // Восстанавливаем сохраненные значения после установки всех обработчиков
      setTimeout(() => {
        restoreSizesState(row, valueContainer);
      }, 100);
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(attributeType)) {
      // Атрибуты с классом и описанием
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => `<option value="${item.class}" data-description="${item.description}">${item.class} - ${item.description}</option>`).join('')}
        </select>
      `;

      // Восстанавливаем сохраненные значения
      restoreSelectState(row, valueContainer, attributeType);
    } else if (attributeType === 'weight') {
      // Специальная обработка для веса с возможностью добавления пользовательских значений
      valueContainer.innerHTML = `
        <div class="space-y-4">
          <!-- Предустановленные веса -->
          <div>
            <h4 class="text-sm font-medium text-gray-700 mb-2">Предустановленные значения</h4>
            <div class="space-y-1">
              ${attributeData.map(item => `
                <label class="flex items-center">
                  <input type="checkbox" class="attribute-checkbox mr-3" value="${JSON.stringify(item).replace(/"/g, '&quot;')}" data-weight-text="${item.value} ${item.unit}">
                  <span class="text-sm font-normal">${item.value} ${item.unit}</span>
                </label>
              `).join('')}
            </div>
          </div>

          <!-- Пользовательский вес -->
          <div class="pt-3 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Добавить пользовательское значение</h4>
            <div class="space-y-2">
              <div class="grid grid-cols-2 gap-2">
                <input type="number" step="0.01" min="0" class="custom-weight-value px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" placeholder="Значение">
                <select class="custom-weight-unit px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500">
                  <option value="г">г</option>
                  <option value="кг">кг</option>
                  <option value="т">т</option>
                  <option value="мг">мг</option>
                  <option value="ц">ц</option>
                </select>
              </div>
              <button type="button" class="add-custom-weight-btn w-full px-3 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 font-medium transition-colors">
                + Добавить значение
              </button>
            </div>
            <div class="custom-weights-list space-y-1 mt-2"></div>
          </div>

          <!-- Кнопка сохранения -->
          <div class="pt-3 border-t border-gray-200">
            <button type="button" class="save-weights-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить веса</button>
          </div>
        </div>
      `;

      // Добавляем обработчик для кнопки "Добавить пользовательский вес"
      const addCustomWeightBtn = valueContainer.querySelector('.add-custom-weight-btn');
      if (addCustomWeightBtn) {
        addCustomWeightBtn.addEventListener('click', function() {
          const valueInput = valueContainer.querySelector('.custom-weight-value');
          const unitSelect = valueContainer.querySelector('.custom-weight-unit');
          const customWeightsList = valueContainer.querySelector('.custom-weights-list');

          const value = parseFloat(valueInput.value);
          const unit = unitSelect.value;

          if (value && value > 0 && unit) {
            const customWeight = { value, unit, custom: true };

            const customWeightElement = document.createElement('div');
            customWeightElement.className = 'custom-weight-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
            customWeightElement.innerHTML = `
              <span class="text-gray-700">${value} ${unit} <span class="text-xs text-gray-500">(польз.)</span></span>
              <button type="button" class="remove-custom-weight text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
            `;
            customWeightElement.setAttribute('data-weight', JSON.stringify(customWeight));

            // Добавляем обработчик удаления
            customWeightElement.querySelector('.remove-custom-weight').addEventListener('click', function() {
              customWeightElement.remove();
            });

            customWeightsList.appendChild(customWeightElement);

            // Очищаем поля ввода
            valueInput.value = '';
            unitSelect.selectedIndex = 0;
          } else {
            alert('Введите корректное значение веса');
          }
        });
      }

      // Добавляем обработчик для автосворачивания при выборе предустановленных весов
      valueContainer.addEventListener('change', function(e) {
        if (e.target.classList.contains('attribute-checkbox')) {
          // Автоматическое сворачивание при выборе предустановленного веса
          const checkedBoxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');
          if (checkedBoxes.length > 0) {
            setTimeout(() => {
              collapseAttributeRow(row, valueContainer);
            }, 100);
          }
        }
      });

      // Добавляем обработчик для кнопки "Сохранить веса"
      const saveWeightsBtn = valueContainer.querySelector('.save-weights-btn');
      if (saveWeightsBtn) {
        saveWeightsBtn.addEventListener('click', function() {
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');
          const customWeights = valueContainer.querySelectorAll('.custom-weight-item');

          if (selectedCheckboxes.length > 0 || customWeights.length > 0) {
            collapseAttributeRow(row, 'weight');
          } else {
            alert('Выберите хотя бы одно значение веса или добавьте пользовательское');
          }
        });
      }

      // Восстанавливаем сохраненные значения после установки всех обработчиков
      setTimeout(() => {
        restoreWeightsState(row, valueContainer);
      }, 100);
    } else if (attributeType === 'material') {
      // Материалы - множественный выбор с чекбоксами и возможностью добавления пользовательских
      valueContainer.innerHTML = `
        <div class="space-y-4">
          <!-- Предустановленные материалы -->
          <div class="grid grid-cols-1 gap-2">
            ${attributeData.map(item => `
              <label class="flex items-center">
                <input type="checkbox" class="attribute-checkbox mr-3" value="${JSON.stringify(item).replace(/"/g, '&quot;')}">
                <span class="text-sm font-normal">${item.name}</span>
              </label>
            `).join('')}
          </div>

          <!-- Пользовательский материал -->
          <div class="pt-3 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Добавить пользовательское значение</h4>
            <div class="space-y-2">
              <input type="text" class="custom-material-name w-full px-2 py-1.5 text-sm border border-gray-300 rounded focus:ring-blue-500 focus:border-blue-500" placeholder="Название материала">
              <button type="button" class="add-custom-material-btn w-full px-3 py-1.5 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 font-medium transition-colors">
                + Добавить материал
              </button>
            </div>
            <div class="custom-materials-list space-y-1 mt-2"></div>
          </div>

          <!-- Кнопка сохранения -->
          <div class="pt-3 border-t border-gray-200">
            <button type="button" class="save-materials-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить материалы</button>
          </div>
        </div>
      `;

      // Добавляем обработчик для кнопки "Добавить пользовательский материал"
      const addCustomMaterialBtn = valueContainer.querySelector('.add-custom-material-btn');
      if (addCustomMaterialBtn) {
        addCustomMaterialBtn.addEventListener('click', function() {
          const nameInput = valueContainer.querySelector('.custom-material-name');
          const customMaterialsList = valueContainer.querySelector('.custom-materials-list');

          const name = nameInput.value.trim();

          if (name) {
            const customMaterial = { id: `custom_${Date.now()}`, name, description: name, custom: true };

            const customMaterialElement = document.createElement('div');
            customMaterialElement.className = 'custom-material-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
            customMaterialElement.innerHTML = `
              <span class="text-gray-700">${name} <span class="text-xs text-gray-500">(польз.)</span></span>
              <button type="button" class="remove-custom-material text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
            `;
            customMaterialElement.setAttribute('data-material', JSON.stringify(customMaterial));

            // Добавляем обработчик удаления
            customMaterialElement.querySelector('.remove-custom-material').addEventListener('click', function() {
              customMaterialElement.remove();
            });

            customMaterialsList.appendChild(customMaterialElement);

            // Очищаем поле ввода
            nameInput.value = '';
          } else {
            alert('Введите название материала');
          }
        });
      }

      // Добавляем обработчик для кнопки "Сохранить материалы"
      const saveMaterialsBtn = valueContainer.querySelector('.save-materials-btn');
      if (saveMaterialsBtn) {
        saveMaterialsBtn.addEventListener('click', function() {
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');
          const customMaterials = valueContainer.querySelectorAll('.custom-material-item');

          if (selectedCheckboxes.length > 0 || customMaterials.length > 0) {
            collapseAttributeRow(row, 'material');
          } else {
            alert('Выберите хотя бы один материал или добавьте пользовательский');
          }
        });
      }

      // Восстанавливаем сохраненные значения после установки обработчиков
      setTimeout(() => {
        restoreMaterialsState(row, valueContainer);
      }, 100);
    } else if (attributeType === 'color_pigments') {
      // Цветовые пигменты - множественный выбор с чекбоксами
      valueContainer.innerHTML = `
        <div class="space-y-2">
          ${attributeData.map(pigment => `
            <label class="flex items-start">
              <input type="checkbox" class="attribute-checkbox mr-3 mt-1" value="${JSON.stringify(pigment).replace(/"/g, '&quot;')}" data-id="${pigment.id}" data-name="${pigment.name}">
              <div class="flex-1">
                <span class="text-sm font-semibold">${pigment.name}</span>
                <p class="text-xs text-gray-600 mt-1">${pigment.description}</p>
              </div>
            </label>
          `).join('')}
        </div>
        <div class="mt-4 pt-3 border-t border-gray-200">
          <button type="button" class="save-color-pigments-btn px-4 py-2 bg-green-500 text-white text-sm rounded hover:bg-green-600 font-medium">Сохранить пигменты</button>
        </div>
      `;

      // Добавляем обработчик для кнопки "Сохранить пигменты"
      const saveColorPigmentsBtn = valueContainer.querySelector('.save-color-pigments-btn');
      if (saveColorPigmentsBtn) {
        saveColorPigmentsBtn.addEventListener('click', function() {
          const selectedCheckboxes = valueContainer.querySelectorAll('.attribute-checkbox:checked');

          if (selectedCheckboxes.length > 0) {
            collapseAttributeRow(row, 'color_pigments');
          } else {
            alert('Выберите хотя бы один пигмент');
          }
        });
      }

      // Восстанавливаем сохраненные значения после установки обработчиков
      setTimeout(() => {
        restoreColorPigmentsState(row, valueContainer);
      }, 100);
    } else if (['surfaces', 'patterns'].includes(attributeType)) {
      // Атрибуты с id, name и description (одиночный выбор)
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => `<option value="${item.id}" data-name="${item.name}" data-description="${item.description}">${item.name}</option>`).join('')}
        </select>
      `;

      // Восстанавливаем сохраненные значения
      restoreSelectState(row, valueContainer, attributeType);
    } else {
      // Обычный список
      valueContainer.innerHTML = `
        <select class="attribute-value-select w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
          <option value="">Выберите значение</option>
          ${attributeData.map(item => `<option value="${typeof item === 'string' ? item : item.id || item.name}">${typeof item === 'string' ? item : item.name || item.id}</option>`).join('')}
        </select>
      `;

      // Восстанавливаем сохраненные значения
      restoreSelectState(row, valueContainer, attributeType);
    }

    // Добавляем обработчики событий для обновления вариантов при изменении значений
    const attributeInputs = valueContainer.querySelectorAll('input, select');
    attributeInputs.forEach(input => {
      input.addEventListener('change', function() {
        // Автоматическое сворачивание для обычных селектов при выборе значения
        if (input.tagName === 'SELECT' && input.classList.contains('attribute-value-select') && input.value) {
          // Проверяем, что это не множественный выбор и не атрибуты, которые уже имеют свои кнопки сохранения
          if (!input.multiple && !['colors', 'size'].includes(attributeType) && !typeConfig.isSimpleArray) {
            setTimeout(() => {
              collapseAttributeRow(row, attributeType);
            }, 100);
          }
        }

        // Небольшая задержка для обновления DOM
        setTimeout(() => {
          updateVariantAttributeOptions();
        }, 100);
      });
    });

    // Обновляем доступные атрибуты в вариантах
    updateVariantAttributeOptions();
  }

  function removeAttributeRow(attributeId) {
    const row = document.getElementById(attributeId);
    if (row) {
      row.remove();
      // Обновляем доступные атрибуты в вариантах
      updateVariantAttributeOptions();
    }
  }

  // Функция для сворачивания всех атрибутов
  function collapseAllAttributes() {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const valueContainer = row.querySelector('.attribute-value-content');

      // Проверяем, есть ли выбранный тип и значение
      if (typeSelect && typeSelect.value && valueContainer) {
        const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

        if (hasSelectedValue) {
          collapseAttributeRow(row, typeSelect.value);
        }
      }
    });
  }

  // Функция для сворачивания других атрибутов
  function collapseOtherAttributes(currentAttributeId) {
    const allAttributeRows = document.querySelectorAll('.attribute-row');

    allAttributeRows.forEach(row => {
      if (row.id !== currentAttributeId) {
        const typeSelect = row.querySelector('.attribute-type-select');
        const valueContainer = row.querySelector('.attribute-value-content');

        // Проверяем, есть ли выбранный тип и значение
        if (typeSelect && typeSelect.value && valueContainer) {
          const hasSelectedValue = checkIfHasSelectedValue(row, typeSelect.value);

          if (hasSelectedValue) {
            collapseAttributeRow(row, typeSelect.value);
          }
        }
      }
    });
  }

  // Восстанавливаем состояние множественного выбора из сохраненных данных
  function restoreMultipleSelectState(row, valueContainer) {
    const savedValue = row.getAttribute('data-current-value');
    if (!savedValue) return;

    try {
      const selectedValues = JSON.parse(savedValue);
      if (!Array.isArray(selectedValues)) return;

      const select = valueContainer.querySelector('.attribute-value-select');
      if (!select || !select.multiple) return;

      // Восстанавливаем выбранные опции
      Array.from(select.options).forEach(option => {
        if (selectedValues.includes(option.value)) {
          option.selected = true;
        }
      });
    } catch (error) {
      // Игнорируем ошибки восстановления состояния
    }
  }

  // Восстанавливаем состояние цветов из сохраненных данных
  function restoreColorsState(row, valueContainer) {
    const savedValue = row.getAttribute('data-current-value');
    if (!savedValue) return;

    try {
      const colorsData = JSON.parse(savedValue);
      if (!Array.isArray(colorsData)) return;

      // Восстанавливаем выбранные цвета
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      checkboxes.forEach(checkbox => {
        if (colorsData.includes(checkbox.dataset.name)) {
          checkbox.checked = true;
        }
      });
    } catch (error) {
      // Игнорируем ошибки восстановления состояния
    }
  }

  // Восстанавливаем состояние весов из сохраненных данных
  function restoreWeightsState(row, valueContainer) {
    const savedValue = row.getAttribute('data-current-value');
    if (!savedValue) return;

    try {
      const weightsData = JSON.parse(savedValue);
      if (!Array.isArray(weightsData)) return;

      // Восстанавливаем предустановленные веса
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      checkboxes.forEach(checkbox => {
        const checkboxWeight = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
        const isSelected = weightsData.some(savedWeight =>
          !savedWeight.custom &&
          savedWeight.value === checkboxWeight.value &&
          savedWeight.unit === checkboxWeight.unit
        );
        if (isSelected) {
          checkbox.checked = true;
        }
      });

      // Восстанавливаем пользовательские веса
      const customWeightsList = valueContainer.querySelector('.custom-weights-list');
      const customWeights = weightsData.filter(weight => weight.custom);

      customWeights.forEach(customWeight => {
        const customWeightElement = document.createElement('div');
        customWeightElement.className = 'custom-weight-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
        customWeightElement.innerHTML = `
          <span class="text-gray-700">${customWeight.value} ${customWeight.unit} <span class="text-xs text-gray-500">(польз.)</span></span>
          <button type="button" class="remove-custom-weight text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
        `;
        customWeightElement.setAttribute('data-weight', JSON.stringify(customWeight));

        // Добавляем обработчик удаления
        customWeightElement.querySelector('.remove-custom-weight').addEventListener('click', function() {
          customWeightElement.remove();
        });

        customWeightsList.appendChild(customWeightElement);
      });
    } catch (error) {
      // Игнорируем ошибки восстановления состояния
    }
  }

  // Функция для восстановления состояния материалов
  function restoreMaterialsState(row, valueContainer) {
    const savedValue = row.getAttribute('data-current-value');
    if (!savedValue) return;

    try {
      const materialsData = JSON.parse(savedValue);
      if (!Array.isArray(materialsData)) return;

      // Восстанавливаем выбранные предустановленные материалы
      materialsData.forEach(material => {
        if (!material.custom) {
          const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
          checkboxes.forEach(checkbox => {
            try {
              const checkboxMaterial = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
              if (checkboxMaterial.name === material.name) {
                checkbox.checked = true;
              }
            } catch (e) {
              // Игнорируем ошибки парсинга
            }
          });
        }
      });

      // Восстанавливаем пользовательские материалы
      const customMaterials = materialsData.filter(material => material.custom);
      const customMaterialsList = valueContainer.querySelector('.custom-materials-list');
      if (customMaterialsList && customMaterials.length > 0) {
        customMaterials.forEach(material => {
          const customMaterialElement = document.createElement('div');
          customMaterialElement.className = 'custom-material-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
          customMaterialElement.innerHTML = `
            <span class="text-gray-700">${material.name} <span class="text-xs text-gray-500">(польз.)</span></span>
            <button type="button" class="remove-custom-material text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
          `;
          customMaterialElement.setAttribute('data-material', JSON.stringify(material));

          // Добавляем обработчик удаления
          customMaterialElement.querySelector('.remove-custom-material').addEventListener('click', function() {
            customMaterialElement.remove();
          });

          customMaterialsList.appendChild(customMaterialElement);
        });
      }
    } catch (error) {
      // Игнорируем ошибки восстановления состояния
    }
  }

  // Функция для восстановления состояния цветовых пигментов
  function restoreColorPigmentsState(row, valueContainer) {
    const savedValue = row.getAttribute('data-current-value');
    if (!savedValue) return;

    try {
      const pigmentsData = JSON.parse(savedValue);
      if (!Array.isArray(pigmentsData)) return;

      // Восстанавливаем выбранные пигменты
      const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
      checkboxes.forEach(checkbox => {
        const checkboxPigment = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
        const isSelected = pigmentsData.some(savedPigment =>
          savedPigment.id === checkboxPigment.id
        );
        if (isSelected) {
          checkbox.checked = true;
        }
      });
    } catch (error) {
      // Игнорируем ошибки восстановления состояния
    }
  }

  // Восстанавливаем состояние размеров из сохраненных данных
  function restoreSizesState(row, valueContainer) {
    const savedValue = row.getAttribute('data-current-value');
    if (!savedValue) return;

    try {
      const sizesData = JSON.parse(savedValue);
      if (!Array.isArray(sizesData)) return;

      const customSizesList = valueContainer.querySelector('.custom-sizes-list');

      sizesData.forEach(size => {
        if (size.custom) {
          // Восстанавливаем пользовательский размер
          if (customSizesList) {
            const customSizeElement = document.createElement('div');
            customSizeElement.className = 'custom-size-item flex items-center justify-between bg-gray-50 px-2 py-1.5 rounded border text-sm';
            customSizeElement.innerHTML = `
              <span class="text-gray-700">${size.length}×${size.width}×${size.height} мм <span class="text-xs text-gray-500">(польз.)</span></span>
              <button type="button" class="remove-custom-size text-red-500 hover:text-red-700 text-xs px-1 py-0.5 hover:bg-red-50 rounded transition-colors">×</button>
            `;
            customSizeElement.setAttribute('data-size', JSON.stringify(size));

            // Добавляем обработчик удаления
            customSizeElement.querySelector('.remove-custom-size').addEventListener('click', function() {
              customSizeElement.remove();
            });

            customSizesList.appendChild(customSizeElement);
          }
        } else {
          // Восстанавливаем выбор предустановленного размера
          const checkboxes = valueContainer.querySelectorAll('.attribute-checkbox');
          checkboxes.forEach(checkbox => {
            try {
              const checkboxSize = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
              if (checkboxSize.length === size.length &&
                  checkboxSize.width === size.width &&
                  checkboxSize.height === size.height) {
                checkbox.checked = true;
              }
            } catch (e) {
              // Игнорируем ошибки парсинга
            }
          });
        }
      });
    } catch (error) {
      // Игнорируем ошибки восстановления состояния
    }
  }

  // Восстанавливаем состояние обычных селектов из сохраненных данных
  function restoreSelectState(row, valueContainer, attributeType) {
    const savedValue = row.getAttribute('data-current-value');
    if (!savedValue) return;

    try {
      const select = valueContainer.querySelector('.attribute-value-select');
      if (!select) return;

      let savedData;
      try {
        savedData = JSON.parse(savedValue);
      } catch (e) {
        // Если не удается распарсить как JSON, используем как строку
        savedData = savedValue;
      }

      // Для разных типов атрибутов используем разные стратегии восстановления
      if (attributeType === 'weight') {
        // Для веса сравниваем JSON-строки
        Array.from(select.options).forEach(option => {
          if (option.value && option.value === JSON.stringify(savedData).replace(/"/g, '&quot;')) {
            option.selected = true;
          }
        });
      } else if (['surfaces', 'patterns'].includes(attributeType)) {
        // Для атрибутов с объектами сравниваем по id
        const savedId = typeof savedData === 'object' ? savedData.id : savedData;
        Array.from(select.options).forEach(option => {
          if (option.value === savedId) {
            option.selected = true;
          }
        });
      } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(attributeType)) {
        // Для атрибутов с классами сравниваем по значению класса
        const savedClass = typeof savedData === 'object' ? savedData.class : savedData;
        Array.from(select.options).forEach(option => {
          if (option.value === savedClass) {
            option.selected = true;
          }
        });
      } else {
        // Для остальных атрибутов простое сравнение значений
        Array.from(select.options).forEach(option => {
          if (option.value === savedData) {
            option.selected = true;
          }
        });
      }
    } catch (error) {
      // Игнорируем ошибки восстановления состояния
    }
  }

  // Проверяем, есть ли выбранное значение в атрибуте
  function checkIfHasSelectedValue(row, attributeType) {
    if (attributeType === 'colors') {
      return row.querySelectorAll('.attribute-checkbox:checked').length > 0;
    } else if (attributeType === 'size') {
      // Проверяем выбранные предустановленные размеры
      const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
      // Проверяем добавленные пользовательские размеры
      const customSizes = row.querySelectorAll('.custom-size-item');

      return selectedCheckboxes.length > 0 || customSizes.length > 0;
    } else if (attributeType === 'material') {
      // Проверяем выбранные предустановленные материалы
      const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
      // Проверяем добавленные пользовательские материалы
      const customMaterials = row.querySelectorAll('.custom-material-item');

      return selectedCheckboxes.length > 0 || customMaterials.length > 0;
    } else if (attributeType === 'weight') {
      // Проверяем выбранные предустановленные веса
      const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
      // Проверяем добавленные пользовательские веса
      const customWeights = row.querySelectorAll('.custom-weight-item');

      return selectedCheckboxes.length > 0 || customWeights.length > 0;
    } else if (attributeType === 'color_pigments') {
      return row.querySelectorAll('.attribute-checkbox:checked').length > 0;
    } else {
      const select = row.querySelector('.attribute-value-select');
      return select && select.value;
    }
  }

  // Сворачиваем конкретную строку атрибута
  function collapseAttributeRow(row, attributeType) {
    const valueContainer = row.querySelector('.attribute-value-content');
    if (!valueContainer) return;

    // Сохраняем текущее значение атрибута в data-атрибут
    const currentValue = getCurrentAttributeValue(row, attributeType);
    if (currentValue !== null) {
      row.setAttribute('data-current-value', JSON.stringify(currentValue));
    }

    // Получаем выбранные значения для отображения
    const selectedValues = getSelectedValuesText(row, attributeType);

    if (selectedValues) {
      valueContainer.innerHTML = `
        <div class="collapsed-view p-3 bg-gray-100 rounded border cursor-pointer" onclick="expandAttributeRow('${row.id}')">
          <div class="text-sm font-semibold text-gray-800">${selectedValues}</div>
          <div class="text-xs text-gray-500 mt-1">Нажмите для редактирования</div>
        </div>
      `;

      // Добавляем класс для отслеживания состояния
      row.classList.add('collapsed');
    }
  }

  // Получаем текущее значение атрибута из формы
  function getCurrentAttributeValue(row, attributeType) {
    if (attributeType === 'colors') {
      const checkboxes = row.querySelectorAll('.attribute-checkbox:checked');
      return Array.from(checkboxes).map(cb => cb.dataset.name);
    } else if (attributeType === 'size') {
      const sizes = [];

      // Собираем выбранные предустановленные размеры
      const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
      selectedCheckboxes.forEach(checkbox => {
        try {
          const size = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          sizes.push(size);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      // Собираем пользовательские размеры
      const customSizes = row.querySelectorAll('.custom-size-item');
      customSizes.forEach(item => {
        try {
          const size = JSON.parse(item.getAttribute('data-size'));
          sizes.push(size);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return sizes.length > 0 ? sizes : null;
    } else if (attributeType === 'material') {
      // Материалы - множественный выбор с чекбоксами и пользовательскими значениями
      const materials = [];

      // Собираем выбранные предустановленные материалы
      const checkedMaterials = row.querySelectorAll('.attribute-checkbox:checked');
      checkedMaterials.forEach(checkbox => {
        try {
          const material = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          materials.push(material);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      // Собираем пользовательские материалы
      const customMaterials = row.querySelectorAll('.custom-material-item');
      customMaterials.forEach(item => {
        try {
          const material = JSON.parse(item.getAttribute('data-material'));
          materials.push(material);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return materials.length > 0 ? materials : null;
    } else if (attributeType === 'color_pigments') {
      // Цветовые пигменты - множественный выбор
      const pigments = [];

      // Собираем выбранные пигменты
      const checkedPigments = row.querySelectorAll('.attribute-checkbox:checked');
      checkedPigments.forEach(checkbox => {
        try {
          const pigment = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          pigments.push(pigment);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return pigments.length > 0 ? pigments : null;
    } else if (['surfaces', 'patterns'].includes(attributeType)) {
      const select = row.querySelector('.attribute-value-select');
      const selectedOption = select?.selectedOptions[0];
      if (selectedOption && selectedOption.value) {
        return {
          id: selectedOption.value,
          name: selectedOption.dataset.name,
          description: selectedOption.dataset.description
        };
      }
    } else if (['strength_classes', 'frost_resistance', 'water_absorption'].includes(attributeType)) {
      const select = row.querySelector('.attribute-value-select');
      const selectedOption = select?.selectedOptions[0];
      if (selectedOption && selectedOption.value) {
        return {
          class: selectedOption.value,
          description: selectedOption.dataset.description
        };
      }
    } else if (attributeType === 'weight') {
      // Собираем все выбранные веса (предустановленные + пользовательские)
      const weights = [];

      // Предустановленные веса
      const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
      selectedCheckboxes.forEach(checkbox => {
        try {
          const weight = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          weights.push(weight);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      // Пользовательские веса
      const customWeights = row.querySelectorAll('.custom-weight-item');
      customWeights.forEach(item => {
        try {
          const weight = JSON.parse(item.getAttribute('data-weight'));
          weights.push(weight);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return weights.length > 0 ? weights : null;
    } else if (attributeType === 'textures') {
      // Специальная обработка для текстур (множественный выбор)
      const select = row.querySelector('.attribute-value-select');
      if (select && select.multiple) {
        const selectedOptions = Array.from(select.selectedOptions);
        return selectedOptions.map(option => option.value);
      }
    } else {
      // Обычные атрибуты
      const select = row.querySelector('.attribute-value-select');
      if (select) {
        if (select.multiple) {
          const selectedOptions = Array.from(select.selectedOptions);
          return selectedOptions.map(option => {
            try {
              return JSON.parse(option.value.replace(/&quot;/g, '"'));
            } catch (e) {
              return option.value;
            }
          });
        } else if (select.value) {
          // Пытаемся распарсить значение как JSON (для объектных атрибутов)
          try {
            return JSON.parse(select.value.replace(/&quot;/g, '"'));
          } catch (e) {
            // Если не JSON, возвращаем как строку
            return select.value;
          }
        }
      }
    }
    return null;
  }

  // Получаем текст выбранных значений
  function getSelectedValuesText(row, attributeType) {
    if (attributeType === 'colors') {
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      return Array.from(checked).map(cb => cb.dataset.name).join(', ');
    } else if (attributeType === 'size') {
      const sizeTexts = [];

      // Собираем текст выбранных предустановленных размеров
      const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
      selectedCheckboxes.forEach(checkbox => {
        const sizeText = checkbox.getAttribute('data-size-text');
        if (sizeText) {
          sizeTexts.push(sizeText);
        }
      });

      // Собираем текст пользовательских размеров
      const customSizes = row.querySelectorAll('.custom-size-item');
      customSizes.forEach(item => {
        const span = item.querySelector('span');
        if (span) {
          sizeTexts.push(span.textContent);
        }
      });

      return sizeTexts.length > 0 ? sizeTexts.join(', ') : null;
    } else if (attributeType === 'weight') {
      const weightTexts = [];

      // Предустановленные веса
      const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
      selectedCheckboxes.forEach(checkbox => {
        try {
          const weight = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          weightTexts.push(`${weight.value} ${weight.unit}`);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      // Пользовательские веса
      const customWeights = row.querySelectorAll('.custom-weight-item');
      customWeights.forEach(item => {
        try {
          const weight = JSON.parse(item.getAttribute('data-weight'));
          weightTexts.push(`${weight.value} ${weight.unit} (польз.)`);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return weightTexts.length > 0 ? weightTexts.join(', ') : null;
    } else if (attributeType === 'material') {
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      const customMaterials = row.querySelectorAll('.custom-material-item');

      const selectedMaterials = [];

      // Добавляем предустановленные материалы
      checked.forEach(checkbox => {
        try {
          const material = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          selectedMaterials.push(material.name);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      });

      // Добавляем пользовательские материалы
      customMaterials.forEach(item => {
        try {
          const material = JSON.parse(item.getAttribute('data-material'));
          selectedMaterials.push(`${material.name} (польз.)`);
        } catch (e) {
          // Игнорируем ошибки парсинга
        }
      });

      return selectedMaterials.length > 0 ? selectedMaterials.join(', ') : null;
    } else if (attributeType === 'color_pigments') {
      // Цветовые пигменты - множественный выбор
      const checked = row.querySelectorAll('.attribute-checkbox:checked');
      const pigmentTexts = [];

      checked.forEach(checkbox => {
        try {
          const pigment = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
          pigmentTexts.push(pigment.name);
        } catch (error) {
          // Игнорируем ошибки парсинга
        }
      });

      return pigmentTexts.length > 0 ? pigmentTexts.join(', ') : null;
    } else if (attributeType === 'textures') {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.multiple) {
        const selectedOptions = Array.from(select.selectedOptions);
        return selectedOptions.map(option => option.textContent).join(', ');
      }
    } else {
      const select = row.querySelector('.attribute-value-select');
      if (select && select.value) {
        const selectedOption = select.selectedOptions[0];
        return selectedOption.textContent;
      }
    }
    return null;
  }

  // Разворачиваем атрибут (глобальная функция для onclick)
  window.expandAttributeRow = function(attributeId) {
    const row = document.getElementById(attributeId);
    if (!row) return;

    const typeSelect = row.querySelector('.attribute-type-select');
    if (typeSelect && typeSelect.value) {
      // Восстанавливаем полный интерфейс
      handleAttributeTypeChange(typeSelect);
      row.classList.remove('collapsed');

      // Сворачиваем другие атрибуты
      collapseOtherAttributes(attributeId);
    }
  };

  // Функция для сбора данных атрибутов
  function collectAttributesData() {
    const attributes = {};
    const attributeRows = document.querySelectorAll('.attribute-row');

    attributeRows.forEach((row) => {
      const typeSelect = row.querySelector('.attribute-type-select');
      const attributeType = typeSelect?.value;

      if (!attributeType) {
        return;
      }

      let attributeValue = null;

      // Если атрибут свернут, берем значение из data-атрибута
      if (row.classList.contains('collapsed')) {
        const savedValue = row.getAttribute('data-current-value');
        if (savedValue) {
          try {
            attributeValue = JSON.parse(savedValue);
          } catch (e) {
            // Игнорируем ошибки парсинга
          }
        }
      } else {
        // Если атрибут развернут, собираем данные из формы

          if (attributeType === 'colors') {
            const checkboxes = row.querySelectorAll('.attribute-checkbox:checked');
            attributeValue = Array.from(checkboxes).map(cb => cb.dataset.name);
          } else if (attributeType === 'size') {
            const sizes = [];

            // Собираем выбранные предустановленные размеры
            const selectedCheckboxes = row.querySelectorAll('.attribute-checkbox:checked');
            selectedCheckboxes.forEach(checkbox => {
              try {
                const size = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
                sizes.push(size);
              } catch (error) {
                // Игнорируем ошибки парсинга
              }
            });

            // Собираем пользовательские размеры
            const customSizes = row.querySelectorAll('.custom-size-item');
            customSizes.forEach(item => {
              try {
                const size = JSON.parse(item.getAttribute('data-size'));
                sizes.push(size);
              } catch (error) {
                // Игнорируем ошибки парсинга
              }
            });

            if (sizes.length === 0) {
              throw new Error('Выберите хотя бы один размер');
            }

            attributeValue = sizes;
          } else if (attributeType === 'color_pigments') {
            // Цветовые пигменты - множественный выбор
            const pigments = [];

            // Собираем выбранные пигменты
            const checkedPigments = row.querySelectorAll('.attribute-checkbox:checked');
            checkedPigments.forEach(checkbox => {
              try {
                const pigment = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
                pigments.push(pigment);
              } catch (error) {
                // Игнорируем ошибки парсинга
              }
            });

            if (pigments.length === 0) {
              throw new Error('Выберите хотя бы один пигмент');
            }

            attributeValue = pigments;
          } else if (attributeType === 'material') {
            // Материалы - множественный выбор с чекбоксами и пользовательскими значениями
            const materials = [];

            // Собираем выбранные предустановленные материалы
            const checkedMaterials = row.querySelectorAll('.attribute-checkbox:checked');
            checkedMaterials.forEach(checkbox => {
              try {
                const material = JSON.parse(checkbox.value.replace(/&quot;/g, '"'));
                materials.push(material);
              } catch (error) {
                // Игнорируем ошибки парсинга
              }
            });

            // Собираем пользовательские материалы
            const customMaterials = row.querySelectorAll('.custom-material-item');
            customMaterials.forEach(item => {
              try {
                const material = JSON.parse(item.getAttribute('data-material'));
                materials.push(material);
              } catch (error) {
                // Игнорируем ошибки парсинга
              }
            });

            if (materials.length === 0) {
              throw new Error('Выберите хотя бы один материал');
            }

            attributeValue = materials;
          } else if (attributeType === 'surfaces') {
            const select = row.querySelector('.attribute-value-select');
            if (select?.value) {
              attributeValue = select.value;
            }
          } else if (attributeType === 'patterns') {
            const select = row.querySelector('.attribute-value-select');
            if (select?.value) {
              attributeValue = select.value;
            }
          } else if (attributeType === 'strength_classes') {
            const select = row.querySelector('.attribute-value-select');
            if (select?.value) {
              attributeValue = select.value;
            }
          } else if (attributeType === 'frost_resistance') {
            const select = row.querySelector('.attribute-value-select');
            if (select?.value) {
              attributeValue = select.value;
            }
          } else if (attributeType === 'water_absorption') {
            const select = row.querySelector('.attribute-value-select');
            if (select?.value) {
              attributeValue = select.value;
            }
          } else if (attributeType === 'textures') {
            const select = row.querySelector('.attribute-value-select');
            if (select && select.multiple) {
              const selectedOptions = Array.from(select.selectedOptions);
              if (selectedOptions.length > 0) {
                attributeValue = selectedOptions[0].value; // берем первое значение для совместимости
              }
            } else if (select?.value) {
              attributeValue = select.value;
            }
          } else if (attributeType === 'weight') {
            const select = row.querySelector('.attribute-value-select');
            if (select?.value) {
              try {
                const weightData = JSON.parse(select.value.replace(/&quot;/g, '"'));
                attributeValue = {
                  weight: weightData.value,
                  weight_unit: weightData.unit
                };
              } catch (error) {
                // Если не удается распарсить, используем значение как есть
                attributeValue = select.value;
              }
            }
          } else {
            // Обычные атрибуты
            const select = row.querySelector('.attribute-value-select');

            if (select) {
              if (select.multiple) {
                const selectedOptions = Array.from(select.selectedOptions);
                attributeValue = selectedOptions.map(option => {
                  try {
                    return JSON.parse(option.value.replace(/&quot;/g, '"'));
                  } catch (e) {
                    return option.value;
                  }
                });
              } else if (select.value) {
                // Пытаемся распарсить значение как JSON (для объектных атрибутов)
                try {
                  attributeValue = JSON.parse(select.value.replace(/&quot;/g, '"'));
                } catch (e) {
                  // Если не JSON, возвращаем как строку
                  attributeValue = select.value;
                }
              }
            }
          }
        }

        // Сохраняем атрибут в итоговый объект
        if (attributeValue !== null && attributeValue !== undefined) {
          // Специальная обработка для некоторых типов атрибутов
          if (attributeType === 'size') {
            attributes.size = attributeValue;
          } else if (attributeType === 'weight' && typeof attributeValue === 'object' && attributeValue.weight !== undefined) {
            attributes.weight = attributeValue.weight;
            attributes.weight_unit = attributeValue.weight_unit;
          } else {
            attributes[attributeType] = attributeValue;
          }
        }
    });


    return attributes;
  }

















  document.addEventListener('DOMContentLoaded', function() {
    const priceIcon = document.querySelector('.price-info-icon');
    const tooltip = document.getElementById('price-warning-tooltip');
    if (!priceIcon || !tooltip) return;
    priceIcon.addEventListener('mouseenter', function() {
      tooltip.classList.add('show');
    });
    priceIcon.addEventListener('mouseleave', function() {
      tooltip.classList.remove('show');
    });
    priceIcon.addEventListener('focus', function() {
      tooltip.classList.add('show');
    });
    priceIcon.addEventListener('blur', function() {
      tooltip.classList.remove('show');
    });
  });

  // Глобальный обработчик для автосворачивания форм с множественным выбором при клике вне формы
  document.addEventListener('click', function(event) {
    // Игнорируем клики внутри вариантов товара
    if (event.target.closest('.variant-row') || event.target.closest('#variants-section')) {
      return;
    }

    // Находим все развернутые формы атрибутов
    const expandedForms = document.querySelectorAll('.attribute-row:not(.collapsed) .attribute-value-container');

    expandedForms.forEach(container => {
      const row = container.closest('.attribute-row');
      const typeSelect = row.querySelector('.attribute-type-select');
      const attributeType = typeSelect?.value;

      if (!attributeType) return;

      // Проверяем, что клик был вне этой формы
      if (!container.contains(event.target)) {
        let hasSelectedValues = false;

        // Проверяем разные типы атрибутов с множественным выбором
        if (attributeType === 'size') {
          const selectedCheckboxes = container.querySelectorAll('.attribute-checkbox:checked');
          const customSizes = container.querySelectorAll('.custom-size-item');
          hasSelectedValues = selectedCheckboxes.length > 0 || customSizes.length > 0;
        } else if (attributeType === 'colors') {
          const selectedCheckboxes = container.querySelectorAll('.attribute-checkbox:checked');
          hasSelectedValues = selectedCheckboxes.length > 0;
        } else {
          // Для атрибутов с multiple select
          const select = container.querySelector('.attribute-value-select');
          if (select && select.multiple) {
            hasSelectedValues = Array.from(select.selectedOptions).length > 0;
          }
        }

        if (hasSelectedValues) {
          collapseAttributeRow(row, attributeType);
        }
      }
    });
  });

  // Автосворачивание форм с множественным выбором при добавлении нового атрибута
  const addAttributeBtn = document.getElementById('add-attribute-btn');
  if (addAttributeBtn) {
    addAttributeBtn.addEventListener('click', function() {
      // Сворачиваем все развернутые формы атрибутов с выбранными значениями
      const expandedForms = document.querySelectorAll('.attribute-row:not(.collapsed) .attribute-value-container');

      expandedForms.forEach(container => {
        const row = container.closest('.attribute-row');
        const typeSelect = row.querySelector('.attribute-type-select');
        const attributeType = typeSelect?.value;

        if (!attributeType) return;

        let hasSelectedValues = false;

        // Проверяем разные типы атрибутов с множественным выбором
        if (attributeType === 'size') {
          const selectedCheckboxes = container.querySelectorAll('.attribute-checkbox:checked');
          const customSizes = container.querySelectorAll('.custom-size-item');
          hasSelectedValues = selectedCheckboxes.length > 0 || customSizes.length > 0;
        } else if (attributeType === 'colors') {
          const selectedCheckboxes = container.querySelectorAll('.attribute-checkbox:checked');
          hasSelectedValues = selectedCheckboxes.length > 0;
        } else {
          // Для атрибутов с multiple select
          const select = container.querySelector('.attribute-value-select');
          if (select && select.multiple) {
            hasSelectedValues = Array.from(select.selectedOptions).length > 0;
          }
        }

        if (hasSelectedValues) {
          collapseAttributeRow(row, attributeType);
        }
      });
    });
  }


</script>
