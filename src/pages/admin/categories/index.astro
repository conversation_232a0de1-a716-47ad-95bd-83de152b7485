---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { isAuthenticated } from '../../../utils/auth';
import Card from '../../../components/ui/Card.astro';
import CardHeader from '../../../components/ui/CardHeader.astro';
import CardTitle from '../../../components/ui/CardTitle.astro';
import CardDescription from '../../../components/ui/CardDescription.astro';
import CardContent from '../../../components/ui/CardContent.astro';
import Button from '../../../components/ui/Button.astro';
import Badge from '../../../components/ui/Badge.astro';

// Защита страницы
if (!isAuthenticated(Astro.request)) {
  return Astro.redirect('/admin/login');
}

// Загрузка данных о категориях
import categoriesFile from '../../../../data/product/categories.json';
const categoriesData = categoriesFile.categories;

// Статистика
const totalCategories = categoriesData.length;
const totalSubcategories = categoriesData.reduce((sum, cat) => sum + cat.subcategories.length, 0);

/**
 * @param {any} cat
 * @param {string} key
 * @returns {boolean}
 */
const getCategoryStatus = (cat: any, key: string): boolean => (cat && typeof cat === 'object' && key in cat ? cat[key] !== false : true);
---

<AdminLayout title="Управление категориями | LuxBeton">
  <div class="space-y-8">
    <!-- Заголовок и действия -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between animate-fade-in">
      <div>
        <h1 class="text-3xl font-bold tracking-tight text-gray-900">Управление категориями</h1>
        <p class="mt-2 text-sm text-gray-600">Создавайте и управляйте категориями товаров для лучшей организации каталога</p>
      </div>
      <div class="mt-4 sm:mt-0">
        <button
          id="add-category-btn"
          class="add-category-btn inline-flex items-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600"
          style="background-color: #3b82f6;"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
          </svg>
          Добавить категорию
        </button>
      </div>
    </div>

    <!-- Таблица категорий -->
    <div class="animate-slide-in" style="animation-delay: 0.2s;">
      <Card>
        <CardHeader>
          <CardTitle>Список категорий</CardTitle>
        </CardHeader>
        <CardContent class="p-0">
          <!-- Desktop Table -->
          <div class="hidden md:block overflow-x-auto custom-scrollbar">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Название</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Подкатегории</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Видимость</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Доступность</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {categoriesData.map(category => (
                  <tr class="hover:bg-gray-50 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">{category.id}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{category.name}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <Badge variant="outline" class="font-mono text-xs">{category.slug}</Badge>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                      <div class="max-w-xs">
                        <div class="flex flex-wrap gap-1">
                          {category.subcategories.slice(0, 3).map(sub => (
                            <Badge variant="secondary" class="text-xs">{sub}</Badge>
                          ))}
                          {category.subcategories.length > 3 && (
                            <Badge variant="secondary" class="text-xs">+{category.subcategories.length - 3}</Badge>
                          )}
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <div class="flex gap-1">
                        <Badge variant={getCategoryStatus(category, 'visibleInCatalog') ? 'success' : 'outline'} class="text-xs">
                          {getCategoryStatus(category, 'visibleInCatalog') ? 'Видима' : 'Скрыта'}
                        </Badge>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                      <div class="flex gap-1">
                        <Badge variant={getCategoryStatus(category, 'activeForProducts') ? 'success' : 'outline'} class="text-xs">
                          {getCategoryStatus(category, 'activeForProducts') ? 'Активна' : 'Неактивна'}
                        </Badge>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div class="flex space-x-2">
                        <button
                          data-id={category.id}
                          class="edit-category inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors"
                        >
                          Редактировать
                        </button>
                        <button
                          data-id={category.id}
                          class="delete-category inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                        >
                          Удалить
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <!-- Mobile Cards -->
          <div class="md:hidden space-y-4 p-6">
            {categoriesData.map(category => (
              <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between mb-3">
                  <div>
                    <h3 class="text-sm font-medium text-gray-900">{category.name}</h3>
                    <p class="text-xs text-gray-500 font-mono">{category.id}</p>
                  </div>
                  <Badge variant="outline" class="font-mono text-xs">{category.slug}</Badge>
                </div>

                <div class="mb-3">
                  <p class="text-xs text-gray-500 mb-2">Подкатегории:</p>
                  <div class="flex flex-wrap gap-1">
                    {category.subcategories.map(sub => (
                      <Badge variant="secondary" class="text-xs">{sub}</Badge>
                    ))}
                  </div>
                </div>

                <div class="flex gap-1 mt-2">
                  <Badge variant={getCategoryStatus(category, 'visibleInCatalog') ? 'success' : 'outline'} class="text-xs">
                    {getCategoryStatus(category, 'visibleInCatalog') ? 'Видима' : 'Скрыта'}
                  </Badge>
                  <Badge variant={getCategoryStatus(category, 'activeForProducts') ? 'success' : 'outline'} class="text-xs">
                    {getCategoryStatus(category, 'activeForProducts') ? 'Активна' : 'Неактивна'}
                  </Badge>
                </div>

                <div class="flex space-x-2">
                  <button
                    data-id={category.id}
                    class="edit-category flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 transition-colors"
                  >
                    Редактировать
                  </button>
                  <button
                    data-id={category.id}
                    class="delete-category flex-1 inline-flex items-center justify-center px-3 py-1 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 transition-colors"
                  >
                    Удалить
                  </button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  </div>

  <!-- Модальное окно для добавления/редактирования категории -->
  <div id="category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md transform transition-all">
        <div class="flex items-center justify-between mb-6">
          <h2 id="modal-title" class="text-xl font-semibold text-gray-900">Добавить категорию</h2>
          <button type="button" id="close-modal" class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form id="category-form" class="space-y-4">
          <input type="hidden" id="category-id" />

          <div>
            <label for="category-name" class="block text-sm font-medium text-gray-700 mb-2">Название категории</label>
            <input
              type="text"
              id="category-name"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="Введите название категории"
            />
          </div>

          <div>
            <label for="category-slug" class="block text-sm font-medium text-gray-700 mb-2">Slug (URL)</label>
            <div class="flex items-center space-x-2">
              <input
                type="text"
                id="category-slug"
                required
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors font-mono text-sm"
                placeholder="url-slug"
              />
              <button
                type="button"
                id="generate-slug-btn"
                class="generate-slug-btn px-3 py-2 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm whitespace-nowrap"
                style="background-color: #3b82f6;"
                title="Генерировать SLUG из названия с транслитерацией"
              >
                Генерировать
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500">Автоматически генерируется из названия или нажмите кнопку для ручной генерации</p>
          </div>

          <div>
            <label for="category-subcategories" class="block text-sm font-medium text-gray-700 mb-2">Подкатегории</label>
            <textarea
              id="category-subcategories"
              rows="3"
              placeholder="Классическая, Квадратная, Прямоугольная"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
            ></textarea>
            <p class="mt-1 text-xs text-gray-500">Разделяйте подкатегории запятыми</p>
          </div>

          <div>
            <label class="inline-flex items-center mt-2">
              <input type="checkbox" id="category-visible" class="form-checkbox h-4 w-4 text-blue-600" checked />
              <span class="ml-2 text-sm text-gray-700">Показывать в каталоге</span>
            </label>
          </div>
          <div>
            <label class="inline-flex items-center mt-2">
              <input type="checkbox" id="category-active" class="form-checkbox h-4 w-4 text-blue-600" checked />
              <span class="ml-2 text-sm text-gray-700">Разрешить добавление товаров</span>
            </label>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              id="cancel-btn"
              class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Отмена
            </button>
            <button
              type="submit"
              class="save-btn inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              style="background-color: #3b82f6;"
            >
              Сохранить
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</AdminLayout>

<style>
  /* Стили для кнопки добавления категории */
  .add-category-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки сохранения */
  .save-btn:hover {
    background-color: #2563eb !important;
  }

  /* Стили для кнопки генерации SLUG */
  .generate-slug-btn:hover {
    background-color: #2563eb !important;
  }
</style>

<script>
  let isEditMode = false;
  let currentCategoryId = null;

  // Функция для получения статуса категории (клиентская версия)
  function getCategoryStatus(category, key) {
    return category && typeof category === 'object' && key in category ? category[key] !== false : true;
  }

  // Элементы DOM
  const modal = document.getElementById('category-modal');
  const modalTitle = document.getElementById('modal-title');
  const categoryForm = document.getElementById('category-form');
  const categoryIdInput = document.getElementById('category-id');
  const categoryNameInput = document.getElementById('category-name');
  const categorySlugInput = document.getElementById('category-slug');
  const categorySubcategoriesInput = document.getElementById('category-subcategories');
  const categoryVisibleInput = document.getElementById('category-visible');
  const categoryActiveInput = document.getElementById('category-active');

  // Проверяем, что все элементы найдены
  console.log('DOM элементы:', {
    modal: !!modal,
    modalTitle: !!modalTitle,
    categoryForm: !!categoryForm,
    categoryIdInput: !!categoryIdInput,
    categoryNameInput: !!categoryNameInput,
    categorySlugInput: !!categorySlugInput,
    categorySubcategoriesInput: !!categorySubcategoriesInput,
    categoryVisibleInput: !!categoryVisibleInput,
    categoryActiveInput: !!categoryActiveInput
  });

  // Кнопки
  const addCategoryBtn = document.getElementById('add-category-btn');
  const cancelBtn = document.getElementById('cancel-btn');
  const closeModalBtn = document.getElementById('close-modal');

  // Функция закрытия модального окна
  function closeModal() {
    modal?.classList.add('hidden');
  }

  // Открытие модального окна для добавления категории
  addCategoryBtn?.addEventListener('click', () => {
    isEditMode = false;
    currentCategoryId = null;
    if (modalTitle) modalTitle.textContent = 'Добавить категорию';
    categoryForm?.reset();
    if (categoryIdInput) categoryIdInput.value = '';
    if (categoryVisibleInput instanceof HTMLInputElement) categoryVisibleInput.checked = true;
    if (categoryActiveInput instanceof HTMLInputElement) categoryActiveInput.checked = true;
    modal?.classList.remove('hidden');
  });

  // Закрытие модального окна
  cancelBtn?.addEventListener('click', closeModal);
  closeModalBtn?.addEventListener('click', closeModal);

  // Закрытие модального окна при клике вне его
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.add('hidden');
    }
  });

    // Автоматическое создание slug из названия
    categoryNameInput.addEventListener('input', () => {
      if (!isEditMode) {
        const slug = generateSlugFromName(categoryNameInput.value);
        categorySlugInput.value = slug;
      }
    });

    // Обработчик кнопки генерации SLUG
    document.getElementById('generate-slug-btn').addEventListener('click', function() {
      const name = categoryNameInput.value;
      if (name) {
        const slug = generateSlugFromName(name);
        categorySlugInput.value = slug;
      }
    });

    // Обработчики для кнопок редактирования
    document.querySelectorAll('.edit-category').forEach(button => {
    button.addEventListener('click', async () => {
      const categoryId = button.getAttribute('data-id');
      console.log('Редактирование категории с ID:', categoryId);

      try {
        const response = await fetch('/api/admin/categories');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Полученные данные:', data);

        if (!data.categories || !Array.isArray(data.categories)) {
          throw new Error('Неверный формат данных категорий');
        }

        const category = data.categories.find(c => c.id === categoryId);
        console.log('Найденная категория:', category);

        if (category) {
          isEditMode = true;
          currentCategoryId = categoryId;
          modalTitle.textContent = 'Редактировать категорию';

          categoryIdInput.value = category.id;
          categoryNameInput.value = category.name;
          categorySlugInput.value = category.slug;
          categorySubcategoriesInput.value = Array.isArray(category.subcategories) ? category.subcategories.join(', ') : '';

          // Устанавливаем значения чекбоксов
          if (categoryVisibleInput instanceof HTMLInputElement) {
            categoryVisibleInput.checked = getCategoryStatus(category, 'visibleInCatalog');
          }
          if (categoryActiveInput instanceof HTMLInputElement) {
            categoryActiveInput.checked = getCategoryStatus(category, 'activeForProducts');
          }

          modal.classList.remove('hidden');
        } else {
          throw new Error(`Категория с ID ${categoryId} не найдена`);
        }
      } catch (error) {
        console.error('Ошибка загрузки категории:', error);

        // Используем модальное окно для ошибок, если доступно
        if (window.adminModal && window.adminModal.showError) {
          await window.adminModal.showError('Ошибка загрузки данных категории: ' + error.message);
        } else {
          alert('Ошибка загрузки данных категории: ' + error.message);
        }
      }
    });
  });

  // Обработчики для кнопок удаления
  document.querySelectorAll('.delete-category').forEach(button => {
    button.addEventListener('click', async () => {
      const categoryId = button.getAttribute('data-id');

      // Найдем название категории для более информативного сообщения
      const categoryName = button.closest('tr')?.querySelector('td:first-child')?.textContent?.trim() ||
                          button.closest('.bg-white')?.querySelector('h3')?.textContent?.trim() ||
                          'эту категорию';

      const confirmed = await window.adminModal?.confirmDelete(`категорию "${categoryName}"`, {
        message: `Вы уверены, что хотите удалить категорию "${categoryName}"? Все связанные подкатегории также будут удалены. Это действие нельзя отменить.`
      });

      if (confirmed) {
        try {
          const response = await fetch(`/api/admin/categories?id=${categoryId}`, {
            method: 'DELETE'
          });

          if (response.ok) {
            window.location.reload();
          } else {
            // Показываем ошибку через модальное окно
            await window.adminModal?.showError('Не удалось удалить категорию. Попробуйте еще раз.');
          }
        } catch (error) {
          console.error('Ошибка:', error);
          // Показываем ошибку через модальное окно
          await window.adminModal?.showError('Произошла ошибка при удалении категории. Проверьте подключение к интернету и попробуйте еще раз.');
        }
      }
    });
  });

  // Обработка отправки формы
  categoryForm.addEventListener('submit', async (e) => {
    e.preventDefault();

    const categoryData = {
      id: isEditMode ? currentCategoryId : categoryIdInput.value || generateCategoryId(),
      name: categoryNameInput.value,
      slug: categorySlugInput.value,
      subcategories: categorySubcategoriesInput.value
        .split(',')
        .map(s => s.trim())
        .filter(s => s),
      visibleInCatalog: categoryVisibleInput instanceof HTMLInputElement ? categoryVisibleInput.checked : true,
      activeForProducts: categoryActiveInput instanceof HTMLInputElement ? categoryActiveInput.checked : true,
    };

    try {
      const response = await fetch('/api/admin/categories', {
        method: isEditMode ? 'PUT' : 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(categoryData)
      });

      if (response.ok) {
        modal.classList.add('hidden');
        window.location.reload();
      } else {
        const error = await response.json();
        alert('Ошибка при сохранении: ' + (error.error || 'Неизвестная ошибка'));
      }
    } catch (error) {
      console.error('Ошибка:', error);
      alert('Ошибка при сохранении категории');
    }
  });

  // Функция генерации SLUG с транслитерацией
  function generateSlugFromName(name) {
    if (!name) return '';

    // Маппинг кириллических символов в латинские для SLUG
    const cyrillicToLatin = {
      'А': 'a', 'Б': 'b', 'В': 'v', 'Г': 'g', 'Д': 'd', 'Е': 'e', 'Ё': 'yo', 'Ж': 'zh',
      'З': 'z', 'И': 'i', 'Й': 'y', 'К': 'k', 'Л': 'l', 'М': 'm', 'Н': 'n', 'О': 'o',
      'П': 'p', 'Р': 'r', 'С': 's', 'Т': 't', 'У': 'u', 'Ф': 'f', 'Х': 'h', 'Ц': 'ts',
      'Ч': 'ch', 'Ш': 'sh', 'Щ': 'sch', 'Ъ': '', 'Ы': 'y', 'Ь': '', 'Э': 'e', 'Ю': 'yu', 'Я': 'ya',
      'а': 'a', 'б': 'b', 'в': 'v', 'г': 'g', 'д': 'd', 'е': 'e', 'ё': 'yo', 'ж': 'zh',
      'з': 'z', 'и': 'i', 'й': 'y', 'к': 'k', 'л': 'l', 'м': 'm', 'н': 'n', 'о': 'o',
      'п': 'p', 'р': 'r', 'с': 's', 'т': 't', 'у': 'u', 'ф': 'f', 'х': 'h', 'ц': 'ts',
      'ч': 'ch', 'ш': 'sh', 'щ': 'sch', 'ъ': '', 'ы': 'y', 'ь': '', 'э': 'e', 'ю': 'yu', 'я': 'ya'
    };

    // Транслитерация кириллицы в латиницу
    let transliterated = '';
    for (let i = 0; i < name.length; i++) {
      const char = name[i];
      transliterated += cyrillicToLatin[char] || char;
    }

    // Создание SLUG: приведение к нижнему регистру, замена пробелов на дефисы, удаление недопустимых символов
    return transliterated
      .toLowerCase()
      .replace(/\s+/g, '-')           // Заменяем пробелы на дефисы
      .replace(/[^a-z0-9-]/g, '')     // Удаляем все символы кроме букв, цифр и дефисов
      .replace(/--+/g, '-')           // Заменяем множественные дефисы на одинарные
      .replace(/^-+/g, '')            // Удаляем дефисы в начале
      .replace(/-+$/g, '');           // Удаляем дефисы в конце
  }

  // Генерация ID для новой категории
  function generateCategoryId() {
    const name = categoryNameInput && categoryNameInput.value ? categoryNameInput.value : '';

    // Маппинг кириллических символов в латинские
    const cyrillicToLatin = {
      'А': 'A', 'Б': 'B', 'В': 'V', 'Г': 'G', 'Д': 'D', 'Е': 'E', 'Ё': 'YO', 'Ж': 'ZH',
      'З': 'Z', 'И': 'I', 'Й': 'Y', 'К': 'K', 'Л': 'L', 'М': 'M', 'Н': 'N', 'О': 'O',
      'П': 'P', 'Р': 'R', 'С': 'S', 'Т': 'T', 'У': 'U', 'Ф': 'F', 'Х': 'H', 'Ц': 'TS',
      'Ч': 'CH', 'Ш': 'SH', 'Щ': 'SCH', 'Ъ': '', 'Ы': 'Y', 'Ь': '', 'Э': 'E', 'Ю': 'YU', 'Я': 'YA'
    };

    if (name.length >= 2) {
      // Преобразуем первые 2 символа в латиницу
      let id = '';
      for (let i = 0; i < Math.min(2, name.length); i++) {
        const char = name[i].toUpperCase();
        id += cyrillicToLatin[char] || char;
      }

      // Если получился слишком длинный ID (например, CH), берем только первые 2 символа
      return id.substring(0, 2);
    }
    return 'CT';
  }
</script>
