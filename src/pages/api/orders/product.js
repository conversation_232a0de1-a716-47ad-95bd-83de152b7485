import fs from 'fs/promises';
import path from 'path';
import { validateProductOrder } from '../../../utils/validation.js';

const ordersPath = path.join(process.cwd(), 'data/orders/orders-product.json');

export async function POST({ request }) {
  try {
    const body = await request.json();

    // Валидация данных заказа
    const validationErrors = validateProductOrder(body);
    if (validationErrors.length > 0) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Ошибки валидации данных',
        details: validationErrors
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создание объекта заказа
    const order = {
      id: generateOrderId(),
      type: 'product',
      clientType: body.clientType || 'individual',
      clientName: body.clientName.trim(),
      companyName: body.companyName ? body.companyName.trim() : null,
      phone: body.phone,
      paymentMethod: body.paymentMethod || null,
      deliveryMethod: body.deliveryMethod || null,
      comment: body.comment || '',
      items: body.items,
      totalAmount: body.totalAmount || 0,
      status: 'new',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Чтение существующих заказов
    let ordersData;
    try {
      const data = await fs.readFile(ordersPath, 'utf-8');
      ordersData = JSON.parse(data);
    } catch (error) {
      // Если файл не существует, создаем новую структуру
      ordersData = { orders: [] };
    }

    // Добавление нового заказа
    ordersData.orders.unshift(order); // Добавляем в начало для сортировки по дате

    // Сохранение в файл
    await fs.writeFile(ordersPath, JSON.stringify(ordersData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ 
      success: true, 
      orderId: order.id,
      message: 'Заказ успешно создан' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при создании заказа:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Ошибка сервера при создании заказа' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Функция генерации ID заказа
function generateOrderId() {
  // 7 случайных цифр
  const part1 = Array.from({length: 7}, () => Math.floor(Math.random() * 10)).join('');
  // 3 случайных цифры
  const part2 = Array.from({length: 3}, () => Math.floor(Math.random() * 10)).join('');
  return `ORD-${part1}-${part2}`;
}
