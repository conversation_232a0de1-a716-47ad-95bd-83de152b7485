import { authenticateUser } from '../../../utils/auth.js';

export async function POST({ request, cookies }) {
  try {
    const formData = await request.formData();
    const username = formData.get('username');
    const password = formData.get('password');

    if (!username || !password) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Необходимо указать имя пользователя и пароль'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const sessionToken = authenticateUser(username, password);

    if (sessionToken) {
      // Устанавливаем куки
      cookies.set('admin_session', sessionToken, {
        path: '/',
        httpOnly: true,
        secure: import.meta.env.PROD,
        maxAge: 60 * 60 * 24 // 24 часа
      });

      return new Response(JSON.stringify({
        success: true,
        redirect: '/admin'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } else {
      return new Response(JSON.stringify({
        success: false,
        error: 'Неверное имя пользователя или пароль'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  } catch (error) {
    console.error('Ошибка аутентификации:', error);
    return new Response(JSON.stringify({
      success: false,
      error: 'Ошибка при обработке запроса'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
