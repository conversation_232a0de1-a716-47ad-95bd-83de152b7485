import { loadPageSettings } from '../../../../settings/utils/settingsLoader.js';
import { savePageSettings } from '../../../../settings/utils/settingsSaver.js';

export async function post(context) {
  const formData = await context.request.formData();
  const data = Object.fromEntries(formData.entries());

  // Восстановление структуры из flat-полей
  const page = {
    id: data.id,
    template: data.template,
    visible: data.visible === 'on',
    url: { ru: data['url.ru'] || '', en: data['url.en'] || '' },
    seo: {
      title: { ru: data['seo.title.ru'] || '', en: data['seo.title.en'] || '' },
      description: { ru: data['seo.description.ru'] || '', en: data['seo.description.en'] || '' },
      keywords: { ru: data['seo.keywords.ru'] || '', en: data['seo.keywords.en'] || '' }
    },
    blocks: [], // пока не реализовано
    media: []   // пока не реализовано
  };

  const settings = await loadPageSettings();
  let pages = settings.pages || [];
  const idx = pages.findIndex(p => p.id === page.id);
  if (idx >= 0) {
    pages[idx] = page;
  } else {
    pages.push(page);
  }
  settings.pages = pages;
  await savePageSettings(settings);

  return new Response(null, {
    status: 303,
    headers: { Location: '/admin/settings/pages' }
  });
} 