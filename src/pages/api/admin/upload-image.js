import { promises as fs } from 'fs';
import path from 'path';
import { isAuthenticated } from '../../../utils/auth';
import { generateImageFileName, getProductImageFiles } from '../../../utils/imageUtils.js';

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const formData = await request.formData();
    const file = formData.get('file');
    const productId = formData.get('productId');
    const productSlug = formData.get('productSlug'); // SLUG товара для генерации имени файла
    const imageType = formData.get('imageType'); // 'main' или 'additional'

    if (!file || !productId) {
      return new Response(JSON.stringify({ error: 'Файл и ID товара обязательны' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем тип файла
    if (!file.type.startsWith('image/')) {
      return new Response(JSON.stringify({ error: 'Файл должен быть изображением' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Создаем папку для товара если её нет
    const productDir = path.join(process.cwd(), 'public', 'product', productId);
    try {
      await fs.access(productDir);
    } catch {
      await fs.mkdir(productDir, { recursive: true });
    }

    // Генерируем имя файла на основе SLUG товара
    const originalName = file.name || 'image.jpg';
    const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';

    // Используем SLUG товара если он передан, иначе fallback на productId
    const nameForFile = productSlug || productId;

    let fileName;
    if (imageType === 'main') {
      fileName = generateImageFileName(productId, nameForFile, true, 1, extension);
    } else {
      // Для дополнительных изображений определяем следующий доступный индекс
      const existingFiles = await getProductImageFiles(productId);
      console.log('Существующие файлы для товара', productId, ':', existingFiles);

      let nextIndex = 1;
      let attempts = 0;
      const maxAttempts = 20; // Максимум 20 попыток найти свободный индекс

      while (attempts < maxAttempts) {
        const testFileName = generateImageFileName(productId, nameForFile, false, nextIndex, extension);
        const testFilePath = path.join(productDir, testFileName);

        // Проверяем, существует ли файл с таким именем
        try {
          await fs.access(testFilePath);
          nextIndex++;
          attempts++;
        } catch {
          // Файл не существует, можем использовать этот индекс
          break;
        }
      }

      if (attempts >= maxAttempts) {
        throw new Error(`Не удалось найти свободный индекс для изображения после ${maxAttempts} попыток`);
      }

      fileName = generateImageFileName(productId, nameForFile, false, nextIndex, extension);
      console.log('Сгенерированное имя файла:', fileName, 'с индексом:', nextIndex);
    }

    // Сохраняем файл
    const filePath = path.join(productDir, fileName);
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    await fs.writeFile(filePath, buffer);

    // Возвращаем путь к файлу относительно папки product
    const relativePath = `${productId}/${fileName}`;

    return new Response(JSON.stringify({ 
      success: true, 
      imagePath: relativePath,
      fileName: fileName
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка загрузки изображения:', error);
    return new Response(JSON.stringify({ error: 'Ошибка загрузки файла: ' + error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
