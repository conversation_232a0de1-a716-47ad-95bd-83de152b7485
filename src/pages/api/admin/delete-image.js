import { promises as fs } from 'fs';
import path from 'path';
import { isAuthenticated } from '../../../utils/auth';

export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const imagePath = url.searchParams.get('imagePath');

    if (!imagePath) {
      return new Response(JSON.stringify({ error: 'Путь к изображению обязателен' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Проверяем, что путь безопасен (содержит только допустимые символы)
    if (!/^[a-zA-Z0-9\-_\/\.]+$/.test(imagePath)) {
      return new Response(JSON.stringify({ error: 'Недопустимый путь к файлу' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Полный путь к файлу
    const fullPath = path.join(process.cwd(), 'public', 'product', imagePath);

    // Проверяем, что файл существует
    try {
      await fs.access(fullPath);
    } catch {
      return new Response(JSON.stringify({ error: 'Файл не найден' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Удаляем файл
    await fs.unlink(fullPath);

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Изображение успешно удалено' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка удаления изображения:', error);
    return new Response(JSON.stringify({ error: 'Ошибка удаления файла: ' + error.message }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
