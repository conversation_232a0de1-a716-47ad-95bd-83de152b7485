import type { APIRoute } from 'astro';
import fs from 'fs';
import path from 'path';

export const DELETE: APIRoute = async ({ request }) => {
  try {
    const { attributeType } = await request.json();

    if (!attributeType) {
      return new Response('Тип атрибута не указан', { status: 400 });
    }

    const attributesPath = path.join(process.cwd(), 'data', 'product', 'attributes.json');

    if (!fs.existsSync(attributesPath)) {
      return new Response('Файл атрибутов не найден', { status: 404 });
    }

    const attributesData = JSON.parse(fs.readFileSync(attributesPath, 'utf-8'));

    // Проверяем, что тип атрибута существует
    if (!attributesData[attributeType]) {
      return new Response('Тип атрибута не найден', { status: 404 });
    }



    // Удаляем тип атрибута
    delete attributesData[attributeType];

    // Сохраняем обновленные данные
    fs.writeFileSync(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');

    return new Response('Тип атрибута успешно удален', { status: 200 });

  } catch (error) {
    console.error('Ошибка удаления типа атрибута:', error);
    return new Response('Внутренняя ошибка сервера', { status: 500 });
  }
};
