import type { APIRoute } from 'astro';
import fs from 'fs';
import path from 'path';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { oldKey, newKey, newName } = await request.json();

    if (!oldKey || !newKey || !newName) {
      return new Response('Отсутствуют обязательные параметры', { status: 400 });
    }

    // Проверяем, что новый ключ не совпадает с существующими
    const attributesPath = path.join(process.cwd(), 'data', 'product', 'attributes.json');
    
    if (!fs.existsSync(attributesPath)) {
      return new Response('Файл атрибутов не найден', { status: 404 });
    }

    const attributesData = JSON.parse(fs.readFileSync(attributesPath, 'utf-8'));

    // Проверяем, что старый ключ существует
    if (!attributesData[oldKey]) {
      return new Response('Тип атрибута не найден', { status: 404 });
    }

    // Проверяем, что новый ключ не занят (если он отличается от старого)
    if (newKey !== oldKey && attributesData[newKey]) {
      return new Response('Тип атрибута с таким названием уже существует', { status: 409 });
    }

    // Переименовываем тип атрибута
    if (newKey !== oldKey) {
      // Копируем данные под новым ключом
      attributesData[newKey] = attributesData[oldKey];
      // Удаляем старый ключ
      delete attributesData[oldKey];
    }

    // Сохраняем обновленные данные
    fs.writeFileSync(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');

    return new Response('Тип атрибута успешно переименован', { status: 200 });

  } catch (error) {
    console.error('Ошибка переименования типа атрибута:', error);
    return new Response('Внутренняя ошибка сервера', { status: 500 });
  }
};
