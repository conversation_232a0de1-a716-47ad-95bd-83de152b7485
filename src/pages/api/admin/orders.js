import fs from 'fs/promises';
import path from 'path';
import { isAuthenticated } from '../../../utils/auth.js';

const productOrdersPath = path.join(process.cwd(), 'data/orders/orders-product.json');
const callOrdersPath = path.join(process.cwd(), 'data/orders/orders-call.json');

// GET - получение всех заявок
export async function GET({ request, url }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const searchParams = url.searchParams;
    const type = searchParams.get('type'); // 'product', 'call' или null для всех
    const status = searchParams.get('status'); // фильтр по статусу

    let allOrders = [];

    // Загружаем заказы товаров
    if (!type || type === 'product') {
      try {
        const productData = await fs.readFile(productOrdersPath, 'utf-8');
        const productOrders = JSON.parse(productData).orders || [];
        allOrders = allOrders.concat(productOrders);
      } catch (error) {
        // Файл может не существовать
      }
    }

    // Загружаем запросы на звонки
    if (!type || type === 'call') {
      try {
        const callData = await fs.readFile(callOrdersPath, 'utf-8');
        const callOrders = JSON.parse(callData).orders || [];
        allOrders = allOrders.concat(callOrders);
      } catch (error) {
        // Файл может не существовать
      }
    }

    // Фильтрация по статусу
    if (status) {
      allOrders = allOrders.filter(order => order.status === status);
    }

    // Сортировка по дате создания (новые сначала)
    allOrders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    return new Response(JSON.stringify({ 
      success: true, 
      orders: allOrders,
      total: allOrders.length
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при получении заявок:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Ошибка сервера при получении заявок' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// PUT - обновление статуса заявки
export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { id, status, type } = body;

    if (!id || !status || !type) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Необходимо указать ID, статус и тип заявки' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const filePath = type === 'product' ? productOrdersPath : callOrdersPath;
    
    // Читаем файл
    const data = await fs.readFile(filePath, 'utf-8');
    const ordersData = JSON.parse(data);
    
    // Находим и обновляем заявку
    const orderIndex = ordersData.orders.findIndex(order => order.id === id);
    if (orderIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Заявка не найдена' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    ordersData.orders[orderIndex].status = status;
    ordersData.orders[orderIndex].updatedAt = new Date().toISOString();

    // Сохраняем файл
    await fs.writeFile(filePath, JSON.stringify(ordersData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Статус заявки обновлен' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при обновлении заявки:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Ошибка сервера при обновлении заявки' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// DELETE - удаление заявки
export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { id, type } = body;

    if (!id || !type) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Необходимо указать ID и тип заявки' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const filePath = type === 'product' ? productOrdersPath : callOrdersPath;
    
    // Читаем файл
    const data = await fs.readFile(filePath, 'utf-8');
    const ordersData = JSON.parse(data);
    
    // Находим и удаляем заявку
    const orderIndex = ordersData.orders.findIndex(order => order.id === id);
    if (orderIndex === -1) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'Заявка не найдена' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    ordersData.orders.splice(orderIndex, 1);

    // Сохраняем файл
    await fs.writeFile(filePath, JSON.stringify(ordersData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ 
      success: true, 
      message: 'Заявка удалена' 
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при удалении заявки:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Ошибка сервера при удалении заявки' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
