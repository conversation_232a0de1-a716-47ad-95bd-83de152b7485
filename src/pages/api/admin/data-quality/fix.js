import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../../utils/auth.js';
import { cleanProductData } from '../../../../utils/dataValidation.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const projectRoot = path.resolve(__dirname, '../../../..');
const productsPath = path.join(projectRoot, 'data', 'product', 'products.json');

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Читаем данные товаров
    const data = await fs.readFile(productsPath, 'utf-8');
    const products = JSON.parse(data);

    // Создаем резервную копию
    const backupPath = productsPath + '.backup.' + Date.now();
    await fs.copyFile(productsPath, backupPath);

    const cleanedProducts = [];
    let fixedCount = 0;
    let removedCount = 0;

    for (const product of products) {
      try {
        const cleanedProduct = cleanProductData(product);

        // Проверяем, были ли изменения
        const originalJson = JSON.stringify(product);
        const cleanedJson = JSON.stringify(cleanedProduct);

        if (originalJson !== cleanedJson) {
          fixedCount++;
        }

        cleanedProducts.push(cleanedProduct);
      } catch (error) {
        removedCount++;
      }
    }

    // Сохраняем очищенные данные
    await fs.writeFile(productsPath, JSON.stringify(cleanedProducts, null, 2), 'utf-8');

    return new Response(JSON.stringify({
      success: true,
      fixedCount,
      removedCount,
      totalProducts: cleanedProducts.length,
      backupPath: path.basename(backupPath)
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Ошибка при автоматическом исправлении данных:', error);
    return new Response(JSON.stringify({ 
      error: 'Ошибка при исправлении данных: ' + error.message 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
