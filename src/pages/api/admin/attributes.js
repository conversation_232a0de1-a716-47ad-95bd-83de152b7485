import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';
import { isAuthenticated } from '../../../utils/auth.js';

// Получаем абсолютный путь к директории проекта
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const attributesPath = path.join(__dirname, '../../../../data/product/attributes.json');

export async function GET() {
  try {
    const data = await fs.readFile(attributesPath, 'utf-8');
    return new Response(data, {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка чтения файла атрибутов' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function POST({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { attributeType, attributeData, isNewType, newTypeName } = body;

    // Валидация входных данных
    const validationError = validateAttributeData(attributeType, attributeData, isNewType);
    if (validationError) {
      return new Response(JSON.stringify({ error: validationError }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const data = await fs.readFile(attributesPath, 'utf-8');
    const attributesData = JSON.parse(data);

    // Если создается новый тип атрибута
    if (isNewType && newTypeName) {
      const typeKey = newTypeName.trim().toLowerCase().replace(/\s+/g, '_').replace(/[^a-zа-я0-9_]/g, '');

      // Проверяем, что ключ не пустой после обработки
      if (!typeKey) {
        return new Response(JSON.stringify({ error: 'Название типа атрибута должно содержать буквы или цифры' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Проверяем, что такой тип еще не существует
      if (attributesData[typeKey]) {
        return new Response(JSON.stringify({ error: 'Тип атрибута с таким названием уже существует' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Создаем новый тип атрибута
      attributesData[typeKey] = [];
      attributesData[typeKey].push(attributeData);
    } else {
      // Добавление нового атрибута в существующую категорию
      if (!attributesData[attributeType]) {
        return new Response(JSON.stringify({ error: 'Неизвестный тип атрибута' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // Проверяем, является ли атрибут массивом объектов или простым массивом
      if (Array.isArray(attributesData[attributeType])) {
        attributesData[attributeType].push(attributeData);
      } else if (attributeType === 'size') {
        // Специальная обработка для стандартных размеров
        const { sizeType, sizeData } = attributeData;
        if (!attributesData[attributeType][sizeType]) {
          attributesData[attributeType][sizeType] = [];
        }
        attributesData[attributeType][sizeType].push(sizeData);
      } else {
        return new Response(JSON.stringify({ error: 'Неподдерживаемый формат атрибута' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка сохранения данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function PUT({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { attributeType, attributeData, originalId } = body;

    // Валидация входных данных (для обновления isNewType всегда false)
    const validationError = validateAttributeData(attributeType, attributeData, false);
    if (validationError) {
      return new Response(JSON.stringify({ error: validationError }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const data = await fs.readFile(attributesPath, 'utf-8');
    let attributesData = JSON.parse(data);

    if (!attributesData[attributeType]) {
      return new Response(JSON.stringify({ error: 'Неизвестный тип атрибута' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Обновление существующего атрибута
    if (Array.isArray(attributesData[attributeType])) {
      // Ищем элемент для обновления
      let found = false;
      attributesData[attributeType] = attributesData[attributeType].map(item => {
        if (typeof item === 'object' && item !== null) {
          // Объект - ищем по id или class
          const itemId = item.id || item.class;
          if (itemId === originalId) {
            found = true;
            return attributeData;
          }
        } else if (typeof item === 'string') {
          // Строка - ищем по значению
          if (item === originalId) {
            found = true;
            return attributeData;
          }
        }
        return item;
      });

      // Если элемент не найден, возвращаем ошибку
      if (!found) {
        return new Response(JSON.stringify({ error: 'Атрибут для обновления не найден' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        });
      }
    } else if (attributeType === 'size') {
      // Специальная обработка для стандартных размеров
      const { sizeType, sizeIndex, sizeData } = attributeData;
      if (attributesData[attributeType][sizeType] && attributesData[attributeType][sizeType][sizeIndex]) {
        attributesData[attributeType][sizeType][sizeIndex] = sizeData;
      }
    } else {
      // Обработка для других типов данных (объекты, не массивы)
      // Это может быть объект с ключами или другая структура
      console.log(`Неподдерживаемая структура для типа атрибута: ${attributeType}`);
      return new Response(JSON.stringify({ error: `Неподдерживаемая структура для типа атрибута: ${attributeType}` }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка обновления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

export async function DELETE({ request }) {
  try {
    // Проверка аутентификации
    if (!isAuthenticated(request)) {
      return new Response(JSON.stringify({ error: 'Не авторизован' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json();
    const { attributeType, id, sizeType, sizeIndex } = body;

    const data = await fs.readFile(attributesPath, 'utf-8');
    let attributesData = JSON.parse(data);

    if (!attributesData[attributeType]) {
      return new Response(JSON.stringify({ error: 'Неизвестный тип атрибута' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Удаление атрибута
    if (Array.isArray(attributesData[attributeType])) {
      if (attributesData[attributeType].length > 0 && typeof attributesData[attributeType][0] === 'object') {
        // Массив объектов - удаляем по id или class
        attributesData[attributeType] = attributesData[attributeType].filter(item => {
          const itemId = item.id || item.class;
          return itemId !== id;
        });
      } else {
        // Простой массив строк - удаляем по значению
        attributesData[attributeType] = attributesData[attributeType].filter(item => item !== id);
      }
    } else if (attributeType === 'size' && sizeType && sizeIndex !== null) {
      // Специальная обработка для стандартных размеров
      if (attributesData[attributeType][sizeType]) {
        attributesData[attributeType][sizeType].splice(parseInt(sizeIndex), 1);
        // Если массив стал пустым, удаляем весь тип размера
        if (attributesData[attributeType][sizeType].length === 0) {
          delete attributesData[attributeType][sizeType];
        }
      }
    }

    await fs.writeFile(attributesPath, JSON.stringify(attributesData, null, 2), 'utf-8');

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Ошибка удаления данных' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Функция валидации данных атрибутов
function validateAttributeData(attributeType, attributeData, isNewType = false) {
  if (!attributeType || !attributeData) {
    return 'Отсутствуют обязательные данные';
  }

  // Для новых типов атрибутов проверяем базовую структуру
  if (isNewType) {
    // Для новых типов требуем минимальную структуру
    if (typeof attributeData === 'object' && attributeData !== null) {
      if (!attributeData.name) {
        return 'Для нового типа атрибута требуется поле name';
      }
    } else if (typeof attributeData !== 'string' || attributeData.trim().length === 0) {
      return 'Значение атрибута не может быть пустым';
    }
    return null; // Новые типы проходят базовую валидацию
  }

  switch (attributeType) {
    case 'colors':
      if (!attributeData.id || !attributeData.name || !attributeData.hex) {
        return 'Для цвета требуются поля: id, name, hex';
      }
      if (!/^#[0-9A-Fa-f]{6}$/.test(attributeData.hex)) {
        return 'Неверный формат hex-кода цвета';
      }
      break;

    case 'textures':
      if (typeof attributeData !== 'string' || attributeData.trim().length === 0) {
        return 'Название текстуры не может быть пустым';
      }
      break;

    case 'size':
      if (attributeData.sizeType && attributeData.sizeData) {
        const { sizeData } = attributeData;
        if (!sizeData.length || !sizeData.width || !sizeData.height) {
          return 'Для размера требуются поля: length, width, height';
        }
        if (sizeData.length <= 0 || sizeData.width <= 0 || sizeData.height <= 0) {
          return 'Размеры должны быть положительными числами';
        }
      } else {
        return 'Для размера требуются поля: sizeType, sizeData';
      }
      break;

    case 'strength_classes':
    case 'frost_resistance':
    case 'water_absorption':
      if (!attributeData.class || !attributeData.description) {
        return 'Для класса требуются поля: class, description';
      }
      break;

    case 'surfaces':
    case 'patterns':
    case 'color_pigments':
      if (!attributeData.id || !attributeData.name || !attributeData.description) {
        return 'Требуются поля: id, name, description';
      }
      break;

    default:
      // Для новых типов атрибутов (не входящих в предопределенные)
      if (typeof attributeData === 'object' && attributeData !== null) {
        // Для объектов проверяем наличие обязательных полей
        if (!attributeData.id && !attributeData.name) {
          return 'Для атрибута требуется поле id или name';
        }
      } else if (typeof attributeData !== 'string' || attributeData.trim().length === 0) {
        return 'Значение атрибута не может быть пустым';
      }
      break;
  }

  return null; // Валидация прошла успешно
}
