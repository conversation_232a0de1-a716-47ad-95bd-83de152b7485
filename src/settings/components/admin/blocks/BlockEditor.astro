---
const { blocks = [], pageId } = Astro.props;
---

<div class="space-y-4 mt-4">
  {blocks.length === 0 && (
    <div class="text-gray-400 text-sm">Нет блоков на этой странице.</div>
  )}
  {blocks.map((block, idx) => (
    <div class="bg-gray-50 border border-gray-200 rounded-lg shadow-sm p-4 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2 mb-1">
          <span class="font-mono text-blue-900 text-xs">ID: {block.id}</span>
          <span class="inline-block bg-gray-200 text-gray-700 text-xs px-2 py-0.5 rounded">{block.type}</span>
          <span class="inline-block bg-gray-100 text-gray-500 text-xs px-2 py-0.5 rounded">Порядок: {block.order ?? idx + 1}</span>
          {block.enabled ? (
            <span class="inline-block bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded">Вкл.</span>
          ) : (
            <span class="inline-block bg-gray-300 text-gray-500 text-xs px-2 py-0.5 rounded">Выкл.</span>
          )}
        </div>
        <div class="text-sm text-gray-700 truncate">
          <b>{block.content?.ru?.title || 'Без названия'}</b>
        </div>
      </div>
      <div class="flex gap-2 flex-shrink-0">
        {pageId ? (
          <a href={`/admin/settings/pages/edit/${pageId}/block/edit?blockId=${block.id}`} class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-xs font-semibold shadow transition">Редактировать</a>
        ) : (
          <button type="button" class="btn btn-sm" disabled>Редактировать</button>
        )}
        <button type="button" class="bg-red-100 text-red-700 px-3 py-1 rounded text-xs font-semibold cursor-not-allowed" disabled>Удалить</button>
        <button type="button" class="bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs font-semibold cursor-not-allowed" disabled>Вверх</button>
        <button type="button" class="bg-gray-200 text-gray-700 px-2 py-1 rounded text-xs font-semibold cursor-not-allowed" disabled>Вниз</button>
      </div>
    </div>
  ))}
  <div>
    {pageId ? (
      <a href={`/admin/settings/pages/edit/${pageId}/block/new`} class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded shadow transition">Добавить блок</a>
    ) : (
      <button type="button" class="bg-blue-200 text-blue-500 font-semibold px-4 py-2 rounded shadow cursor-not-allowed" disabled>Добавить блок</button>
    )}
  </div>
</div>

<style>
.btn {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 0.25rem 0.75rem;
  margin-right: 0.5rem;
  color: #6b7280;
  cursor: pointer;
  text-decoration: none;
}
.btn-primary {
  background: #2563eb;
  color: #fff;
  border-color: #2563eb;
}
.btn-danger {
  background: #ef4444;
  color: #fff;
  border-color: #ef4444;
}
.btn-sm {
  font-size: 0.9em;
  padding: 0.15rem 0.5rem;
}
</style>