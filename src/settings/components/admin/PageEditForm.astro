---
import { loadPageSettings } from '../../utils/settingsLoader.js';
import { savePageSettings } from '../../utils/settingsSaver.js';
import { useState } from 'astro/jsx-runtime';
import BlockEditor from './blocks/BlockEditor.astro';

const { id, isNew } = Astro.props;
const page = Astro.props.page || {
  id: '',
  template: '',
  url: { ru: '', en: '' },
  visible: true,
  blocks: [],
  media: [],
  seo: { title: { ru: '', en: '' }, description: { ru: '', en: '' }, keywords: { ru: '', en: '' } }
};
let lang = 'ru';
const pageId = page.id || id;
---

<form action="/api/admin/save-page" method="POST" class="space-y-6" style="max-width:800px;margin:2rem auto;">
  <h2 class="text-lg font-semibold text-gray-800 mb-2">Редактировать страницу:</h2>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div>
      <label class="block mb-1 text-sm font-medium text-gray-700">Язык:</label>
      <select name="lang" class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500">
        <option value="ru">Русский</option>
        <option value="en">English</option>
      </select>
    </div>
    <div>
      <label class="block mb-1 text-sm font-medium text-gray-700">ID страницы:</label>
      <input name="id" value={page.id} required={!isNew} disabled={!isNew} class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 bg-gray-100" />
    </div>
    <div>
      <label class="block mb-1 text-sm font-medium text-gray-700">URL (ru):</label>
      <input name="url.ru" value={page.url.ru} required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
    </div>
    <div>
      <label class="block mb-1 text-sm font-medium text-gray-700">URL (en):</label>
      <input name="url.en" value={page.url.en} required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
    </div>
    <div>
      <label class="block mb-1 text-sm font-medium text-gray-700">Шаблон:</label>
      <input name="template" value={page.template} required class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
    </div>
    <div class="flex items-center mt-6">
      <input type="checkbox" name="visible" checked={page.visible} class="mr-2 rounded border-gray-300 focus:ring-blue-500" />
      <label class="text-sm text-gray-700">Видимая страница</label>
    </div>
  </div>

  <fieldset class="mt-6">
    <legend class="text-base font-semibold text-gray-800 mb-2">Блоки страницы</legend>
    <BlockEditor blocks={page.blocks} pageId={pageId} />
  </fieldset>

  <fieldset class="mt-6">
    <legend class="text-base font-semibold text-gray-800 mb-2">SEO</legend>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Title (ru):</label>
        <input name="seo.title.ru" value={page.seo.title.ru} class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Title (en):</label>
        <input name="seo.title.en" value={page.seo.title.en} class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Description (ru):</label>
        <input name="seo.description.ru" value={page.seo.description.ru} class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Description (en):</label>
        <input name="seo.description.en" value={page.seo.description.en} class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Keywords (ru):</label>
        <input name="seo.keywords.ru" value={page.seo.keywords.ru} class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
      </div>
      <div>
        <label class="block mb-1 text-sm font-medium text-gray-700">Keywords (en):</label>
        <input name="seo.keywords.en" value={page.seo.keywords.en} class="w-full border rounded px-3 py-2 focus:ring-2 focus:ring-blue-500" />
      </div>
    </div>
  </fieldset>

  <div class="flex gap-4 mt-8">
    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded shadow transition">Сохранить</button>
    <a href="/admin/settings/pages" class="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-2 rounded">Отмена</a>
  </div>
</form> 