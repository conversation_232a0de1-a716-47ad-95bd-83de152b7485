// Тип для локализованных строк
export type LocalizedString = { [lang: string]: string };

// Тип для локализованного контента (например, блоки с разной структурой)
export type LocalizedContent<T = any> = { [lang: string]: T };

// SEO-настройки страницы
export interface SEOConfig {
  title: LocalizedString;
  description: LocalizedString;
  keywords?: LocalizedString;
}

// Медиа-элемент (может быть расширен при необходимости)
export interface MediaItem {
  id: string;
  type: 'image' | 'video' | 'pdf';
  url: string;
  category?: string;
  alt?: LocalizedString;
}

// Блок на странице
export interface BlockConfig {
  id: string;
  type: string;
  enabled: boolean;
  order?: number;
  content: LocalizedContent;
}

// Конфиг одной страницы
export interface PageConfig {
  id: string;
  template: string;
  blocks: BlockConfig[];
  media: MediaItem[];
  seo: SEOConfig;
  visible: boolean;
  url: LocalizedString; // Латинские URL для всех языков
}

// Конфиг меню (например, для header/footer)
export interface MenuItem {
  label: LocalizedString;
  url: LocalizedString;
  order?: number;
}

// Header/Footer конфиг
export interface HeaderFooterConfig {
  template: string;
  menu: MenuItem[];
  [key: string]: any;
}

// Основной конфиг страниц
export interface PagesSettings {
  pages: PageConfig[];
  header: HeaderFooterConfig;
  footer: HeaderFooterConfig;
} 