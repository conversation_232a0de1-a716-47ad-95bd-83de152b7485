import fs from 'fs/promises';
import path from 'path';

const PAGE_JSON_PATH = path.resolve(process.cwd(), 'src/settings/data/page.json');

/**
 * Сохраняет настройки страниц (page.json)
 * @param {any} settings - Объект настроек страниц
 * @returns {Promise<void>}
 */
export async function savePageSettings(settings) {
  // Простая валидация структуры (можно расширить)
  if (!settings.pages || !Array.isArray(settings.pages)) {
    throw new Error('Invalid structure: pages must be an array');
  }
  await fs.writeFile(PAGE_JSON_PATH, JSON.stringify(settings, null, 2), 'utf-8');
} 