# Модуль настройки страни<PERSON> (page.json)

## Описание
Модуль предназначен для управления страницами сайта через конфигурируемый JSON-файл с поддержкой мультиязычности. Все URL для страниц задаются в латинице по международным стандартам для всех языков.

## Структура
- **src/settings/data/page.json** — основной файл настроек страниц
- **src/settings/types.ts** — интерфейсы и типы для page.json
- **src/settings/utils/settingsLoader.js** — утилита для загрузки page.json
- **src/settings/utils/settingsSaver.js** — утилита для сохранения page.json

## Пример структуры page.json
```json
{
  "pages": [
    {
      "id": "about",
      "template": "default",
      "blocks": [
        {
          "id": "about-text",
          "type": "text",
          "enabled": true,
          "order": 1,
          "content": {
            "ru": { "title": "О компании", "body": "Наша компания ..." },
            "en": { "title": "About Us", "body": "Our company ..." }
          }
        }
      ],
      "media": [],
      "seo": {
        "title": { "ru": "О компании", "en": "About" },
        "description": { "ru": "О нашей компании", "en": "About our company" },
        "keywords": { "ru": "компания, услуги", "en": "company, services" }
      },
      "visible": true,
      "url": { "ru": "/about", "en": "/about" }
    }
  ],
  "header": { ... },
  "footer": { ... }
}
```

## Принципы мультиязычности
- Все текстовые поля и контент — объекты с ключами языков (например, "ru", "en").
- URL для всех языков — только латиницей, по международным стандартам.
- Если для языка нет ::url::, страница не отображается на этом языке.

## Валидация и безопасность
- Валидация структуры при сохранении.
- URL — только латиница, цифры, дефисы, слэши.
- Контент для каждого языка хранится отдельно.

## Расширяемость
- Легко добавить новые языки, блоки, страницы.
- Структура поддерживает любые типы блоков и медиа. 