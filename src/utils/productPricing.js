/**
 * Утилиты для работы с ценообразованием товаров на клиентской стороне
 */

/**
 * Получает главную цену товара согласно приоритетам:
 * 1. Цена из варианта с isPrimaryPrice: true (высший приоритет)
 * 2. Базовая цена товара (средний приоритет)
 * 3. null если нет ни того, ни другого (низший приоритет)
 */
export function getMainPrice(product) {
  // Проверяем есть ли варианты с основной ценой
  if (product.variants && Array.isArray(product.variants)) {
    const primaryVariant = product.variants.find(variant => 
      variant.isPrimaryPrice === true && 
      variant.price && 
      variant.price.value > 0
    );
    
    if (primaryVariant) {
      return {
        value: primaryVariant.price.value,
        currency: primaryVariant.price.currency,
        unit: primaryVariant.price.unit,
        simvol: primaryVariant.price.simvol || primaryVariant.price.currency,
        source: 'primary_variant'
      };
    }
  }
  
  // Если нет основного варианта, используем базовую цену
  if (product.basePrice && product.basePrice.value > 0) {
    return {
      value: product.basePrice.value,
      currency: product.basePrice.currency,
      unit: product.basePrice.unit,
      simvol: product.basePrice.simvol || product.basePrice.currency,
      source: 'base_price'
    };
  }
  
  // Если нет ни того, ни другого
  return null;
}

/**
 * Проверяет, является ли вариант основным (с основной ценой)
 */
export function isPrimaryVariant(variant) {
  return variant && variant.isPrimaryPrice === true;
}

/**
 * Находит основной вариант в массиве вариантов
 */
export function findPrimaryVariant(variants) {
  if (!Array.isArray(variants)) return null;
  
  return variants.find(variant => 
    variant.isPrimaryPrice === true && 
    variant.price && 
    variant.price.value > 0
  ) || null;
}

/**
 * Определяет, должна ли главная цена изменяться при выборе варианта
 * Главная цена НЕ должна изменяться при выборе не-основных вариантов
 */
export function shouldUpdateMainPrice(selectedVariant, product) {
  // Если выбранный вариант является основным, то главная цена может обновиться
  // Но на самом деле она и так уже отображает цену основного варианта
  // Поэтому главная цена должна оставаться неизменной
  return false;
}

/**
 * Форматирует цену для отображения
 */
export function formatDisplayPrice(price, formatPrice, formatUnit, settingsProduct) {
  if (!price || !price.value) return '0 BYN';
  
  const formattedPrice = formatPrice({
    amount: price.value,
    simvol: price.simvol || price.currency || 'BYN',
    format: '{amount} {simvol}',
    decimalSeparator: '.',
    thousandsSeparator: ' ',
    decimals: 2
  });
  
  const unitDisplay = price.unit ? formatUnit(price.unit, settingsProduct) : '';
  
  return unitDisplay ? `${formattedPrice} / ${unitDisplay}` : formattedPrice;
}

/**
 * Получает данные товара для добавления в корзину с учетом основного варианта
 */
export function getCartProductData(product) {
  const primaryVariant = findPrimaryVariant(product.variants);

  if (primaryVariant) {
    // Если есть основной вариант, используем его данные
    return {
      id: primaryVariant.id,
      name: `${product.name} (${primaryVariant.name})`,
      price: primaryVariant.price.value,
      currency: primaryVariant.price.currency,
      unit: primaryVariant.price.unit,
      image: `/product/${product.images.main}`,
      category: product.category,
      slug: product.slug,
      variant: {
        id: primaryVariant.id,
        name: primaryVariant.name,
        price: primaryVariant.price.value,
        currency: primaryVariant.price.currency,
        unit: primaryVariant.price.unit
      },
      isPrimaryVariant: true
    };
  } else {
    // Если нет основного варианта, используем базовые данные товара
    return {
      id: product.id,
      name: product.name,
      price: product.basePrice?.value || 0,
      currency: product.basePrice?.currency || 'BYN',
      unit: product.basePrice?.unit || 'piece',
      image: `/product/${product.images.main}`,
      category: product.category,
      slug: product.slug,
      isPrimaryVariant: false
    };
  }
}

/**
 * Инициализирует систему главной цены на странице товара
 */
export function initializeMainPricing(product, formatPrice, formatUnit, settingsProduct) {
  const mainPrice = getMainPrice(product);
  const priceElement = document.querySelector('.product-price');

  if (priceElement && mainPrice) {
    const formattedPrice = formatDisplayPrice(mainPrice, formatPrice, formatUnit, settingsProduct);
    priceElement.innerHTML = formattedPrice;

    // Сохраняем главную цену в data-атрибуте для дальнейшего использования
    priceElement.setAttribute('data-main-price', JSON.stringify(mainPrice));
    priceElement.setAttribute('data-price-source', mainPrice.source);
  }

  return mainPrice;
}

/**
 * Обновляет отображение цены варианта (не главной цены)
 * Используется для показа цены конкретного выбранного варианта
 */
export function updateVariantPrice(variantElement, quantity = 1) {
  const unitPrice = parseFloat(variantElement.getAttribute('data-variant-price') || '0');
  const currency = variantElement.getAttribute('data-variant-currency') || 'BYN';
  const unit = variantElement.getAttribute('data-variant-unit') || 'piece';
  
  // Вычисляем общую стоимость
  const totalPrice = unitPrice * quantity;
  
  // Обновляем отображение цены варианта (не главной цены!)
  const variantTotalPrice = variantElement.querySelector('.variant-total-price');
  if (variantTotalPrice) {
    const formattedTotalPrice = new Intl.NumberFormat('ru-RU', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(totalPrice);
    
    const currencySymbol = currency === 'BYN' ? 'BYN' :
                          currency === 'USD' ? '$' :
                          currency === 'EUR' ? '€' : currency;
    
    // Форматируем единицу измерения
    const formattedUnit = formatUnitLocal(unit);
    const unitDisplay = formattedUnit ? ` / ${formattedUnit}` : '';
    
    variantTotalPrice.innerHTML = `${formattedTotalPrice} ${currencySymbol}${unitDisplay}`;
  }
  
  return {
    unitPrice,
    totalPrice,
    currency,
    unit
  };
}

/**
 * Локальная функция для форматирования единиц измерения
 */
function formatUnitLocal(unit) {
  const unitMap = {
    'piece': 'шт',
    'm2': 'м²',
    'kg': 'кг',
    'liter': 'л',
    'meter': 'м',
    'pack': 'упак'
  };
  
  return unitMap[unit] || unit;
}

// Экспортируем функции в глобальную область видимости для использования в других скриптах
if (typeof window !== 'undefined') {
  window.getMainPrice = getMainPrice;
  window.isPrimaryVariant = isPrimaryVariant;
  window.findPrimaryVariant = findPrimaryVariant;
  window.shouldUpdateMainPrice = shouldUpdateMainPrice;
  window.formatDisplayPrice = formatDisplayPrice;
  window.getCartProductData = getCartProductData;
  window.initializeMainPricing = initializeMainPricing;
  window.updateVariantPrice = updateVariantPrice;
}
