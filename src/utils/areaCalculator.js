/**
 * Утилиты для расчета площади и объема товаров
 */

/**
 * Рассчитывает площадь одной единицы товара в м²
 * @param {Object} size - Размеры товара
 * @param {number} size.length - Длина в мм
 * @param {number} size.width - Ширина в мм
 * @param {number} size.height - Высота в мм (опционально)
 * @returns {number} Площадь в м²
 */
export function calculateAreaPerPiece(size) {
  if (!size || !size.length || !size.width) {
    return 0;
  }
  
  // Конвертируем мм² в м²
  const areaInMm2 = size.length * size.width;
  const areaInM2 = areaInMm2 / 1000000; // 1 м² = 1,000,000 мм²
  
  return parseFloat(areaInM2.toFixed(4));
}

/**
 * Рассчитывает общую площадь для заданного количества товара
 * @param {Object} size - Размеры товара
 * @param {number} quantity - Количество единиц
 * @param {number} wastagePercent - Процент допуска/отходов (по умолчанию 10%)
 * @returns {Object} Объект с расчетами
 */
export function calculateTotalArea(size, quantity, wastagePercent = 10) {
  const areaPerPiece = calculateAreaPerPiece(size);
  const baseArea = areaPerPiece * quantity;
  const wastageArea = baseArea * (wastagePercent / 100);
  const totalArea = baseArea + wastageArea;
  
  return {
    areaPerPiece: parseFloat(areaPerPiece.toFixed(4)),
    baseArea: parseFloat(baseArea.toFixed(2)),
    wastageArea: parseFloat(wastageArea.toFixed(2)),
    totalArea: parseFloat(totalArea.toFixed(2)),
    wastagePercent
  };
}

/**
 * Рассчитывает объем одной единицы товара в м³
 * @param {Object} size - Размеры товара
 * @param {number} size.length - Длина в мм
 * @param {number} size.width - Ширина в мм
 * @param {number} size.height - Высота в мм
 * @returns {number} Объем в м³
 */
export function calculateVolumePerPiece(size) {
  if (!size || !size.length || !size.width || !size.height) {
    return 0;
  }
  
  // Конвертируем мм³ в м³
  const volumeInMm3 = size.length * size.width * size.height;
  const volumeInM3 = volumeInMm3 / 1000000000; // 1 м³ = 1,000,000,000 мм³
  
  return parseFloat(volumeInM3.toFixed(6));
}

/**
 * Рассчитывает общий объем для заданного количества товара
 * @param {Object} size - Размеры товара
 * @param {number} quantity - Количество единиц
 * @returns {Object} Объект с расчетами
 */
export function calculateTotalVolume(size, quantity) {
  const volumePerPiece = calculateVolumePerPiece(size);
  const totalVolume = volumePerPiece * quantity;
  
  return {
    volumePerPiece: parseFloat(volumePerPiece.toFixed(6)),
    totalVolume: parseFloat(totalVolume.toFixed(3))
  };
}

/**
 * Определяет тип расчета на основе единицы измерения товара
 * @param {string} unit - Единица измерения
 * @returns {string} Тип расчета: 'area', 'volume', 'piece'
 */
export function getCalculationType(unit) {
  const areaUnits = ['m2', 'sq_m', 'square_meter'];
  const volumeUnits = ['m3', 'cu_m', 'cubic_meter'];
  
  if (areaUnits.includes(unit)) {
    return 'area';
  } else if (volumeUnits.includes(unit)) {
    return 'volume';
  } else {
    return 'piece';
  }
}

/**
 * Форматирует результат расчета для отображения пользователю
 * @param {Object} calculation - Результат расчета
 * @param {string} type - Тип расчета
 * @param {number} quantity - Количество
 * @returns {string} Отформатированная строка
 */
export function formatCalculationResult(calculation, type, quantity) {
  if (type === 'area') {
    if (calculation.wastageArea > 0) {
      return `${quantity} шт = ${calculation.baseArea} м² (+ ${calculation.wastageArea} м² допуск = ${calculation.totalArea} м²)`;
    } else {
      return `${quantity} шт = ${calculation.baseArea} м²`;
    }
  } else if (type === 'volume') {
    return `${quantity} шт = ${calculation.totalVolume} м³`;
  } else {
    return `${quantity} шт`;
  }
}

/**
 * Получает размеры товара из различных форматов данных
 * @param {Object} product - Данные товара
 * @returns {Object|null} Размеры товара или null
 */
export function extractProductSize(product) {
  if (!product || !product.attributes) {
    return null;
  }
  
  const sizeAttr = product.attributes.size;
  
  if (!sizeAttr) {
    return null;
  }
  
  // Если это массив размеров, берем первый
  if (Array.isArray(sizeAttr) && sizeAttr.length > 0) {
    return sizeAttr[0];
  }
  
  // Если это объект с размерами
  if (sizeAttr.length && sizeAttr.width) {
    return sizeAttr;
  }
  
  // Если это старый формат с вариантами
  if (sizeAttr.variants && sizeAttr.variants.length > 0) {
    return sizeAttr.variants[0];
  }
  
  return null;
}

/**
 * Рассчитывает количество товара, необходимое для покрытия заданной площади
 * @param {Object} size - Размеры товара
 * @param {number} targetArea - Целевая площадь в м²
 * @param {number} wastagePercent - Процент допуска/отходов
 * @returns {Object} Объект с расчетами
 */
export function calculateQuantityForArea(size, targetArea, wastagePercent = 10) {
  const areaPerPiece = calculateAreaPerPiece(size);
  
  if (areaPerPiece === 0) {
    return { quantity: 0, totalArea: 0, wastageArea: 0 };
  }
  
  const areaWithWastage = targetArea * (1 + wastagePercent / 100);
  const quantity = Math.ceil(areaWithWastage / areaPerPiece);
  const actualArea = quantity * areaPerPiece;
  const wastageArea = actualArea - targetArea;
  
  return {
    quantity,
    areaPerPiece: parseFloat(areaPerPiece.toFixed(4)),
    targetArea,
    actualArea: parseFloat(actualArea.toFixed(2)),
    wastageArea: parseFloat(wastageArea.toFixed(2)),
    wastagePercent
  };
}

/**
 * Проверяет, является ли товар физическим (имеет размеры)
 * @param {Object} product - Данные товара
 * @returns {boolean} true если товар физический
 */
export function isPhysicalProduct(product) {
  return extractProductSize(product) !== null;
}

/**
 * Получает единицу измерения для отображения
 * @param {string} unit - Единица измерения
 * @param {string} type - Тип расчета
 * @returns {string} Отформатированная единица измерения
 */
export function getDisplayUnit(unit, type) {
  const unitMap = {
    'piece': 'шт',
    'kg': 'кг',
    'm2': 'м²',
    'sq_m': 'м²',
    'square_meter': 'м²',
    'm3': 'м³',
    'cu_m': 'м³',
    'cubic_meter': 'м³'
  };
  
  return unitMap[unit] || unit;
}
