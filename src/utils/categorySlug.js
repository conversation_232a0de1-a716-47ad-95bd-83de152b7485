/**
 * Утилита для получения правильного categorySlug на основе названия категории
 * Использует данные из categories.json для обеспечения консистентности
 */

import categoriesData from '../../data/product/categories.json';

/**
 * Получает правильный slug для категории
 * @param {string} categoryName - Название категории
 * @returns {string} - Правильный slug категории
 */
export function getCategorySlug(categoryName) {
  // Ищем категорию в данных
  const category = categoriesData.categories.find(cat => cat.name === categoryName);
  
  if (category) {
    return category.slug;
  }
  
  // Если категория не найдена, создаем slug из названия
  // Это fallback для новых категорий, которые еще не добавлены в categories.json
  return categoryName
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^a-z0-9-]/g, '');
}

/**
 * Получает объект маппинга названий категорий к их slug'ам
 * @returns {Object} - Объект с маппингом категория -> slug
 */
export function getCategorySlugMap() {
  const map = {};
  categoriesData.categories.forEach(category => {
    map[category.name] = category.slug;
  });
  return map;
}
