/**
 * Утилиты для валидации и очистки данных товаров
 */

/**
 * Проверяет, является ли значение валидным для атрибута
 * @param {any} value - значение для проверки
 * @returns {boolean}
 */
export function isValidAttributeValue(value) {
  // Проверяем на null, undefined, пустые строки
  if (value === null || value === undefined) {
    return false;
  }
  
  // Проверяем строки
  if (typeof value === 'string') {
    const trimmed = value.trim();
    return trimmed !== '' && trimmed !== 'undefined' && trimmed !== 'null';
  }
  
  // Проверяем массивы
  if (Array.isArray(value)) {
    return value.length > 0 && value.every(item => isValidAttributeValue(item));
  }
  
  // Проверяем объекты
  if (typeof value === 'object') {
    // Пустые объекты считаем невалидными
    if (Object.keys(value).length === 0) {
      return false;
    }
    
    // Проверяем, что все значения в объекте валидны
    return Object.values(value).every(val => 
      val !== null && val !== undefined && val !== 'undefined' && val !== 'null'
    );
  }
  
  // Для чисел и булевых значений
  return true;
}

/**
 * Очищает атрибуты от невалидных значений
 * @param {Object} attributes - объект атрибутов
 * @returns {Object} - очищенный объект атрибутов
 */
export function cleanAttributes(attributes) {
  if (!attributes || typeof attributes !== 'object') {
    return {};
  }
  
  const cleaned = {};
  
  for (const [key, value] of Object.entries(attributes)) {
    if (isValidAttributeValue(value)) {
      cleaned[key] = value;
    }
  }
  
  return cleaned;
}

/**
 * Валидирует и очищает данные варианта товара
 * @param {Object} variant - данные варианта
 * @returns {Object} - очищенные данные варианта
 */
export function cleanVariantData(variant) {
  if (!variant || typeof variant !== 'object') {
    return null;
  }
  
  const cleaned = { ...variant };
  
  // Очищаем атрибуты варианта
  if (cleaned.attributes) {
    cleaned.attributes = cleanAttributes(cleaned.attributes);
  }
  
  // Проверяем обязательные поля
  if (!cleaned.name || typeof cleaned.name !== 'string' || cleaned.name.trim() === '') {
    return null;
  }

  if (!cleaned.price || typeof cleaned.price !== 'object' ||
      !cleaned.price.value || isNaN(cleaned.price.value) || cleaned.price.value <= 0) {
    return null;
  }
  
  return cleaned;
}

/**
 * Валидирует и очищает данные товара
 * @param {Object} product - данные товара
 * @returns {Object} - очищенные данные товара
 */
export function cleanProductData(product) {
  if (!product || typeof product !== 'object') {
    throw new Error('Невалидные данные товара');
  }
  
  const cleaned = { ...product };
  
  // Очищаем основные атрибуты товара
  if (cleaned.attributes) {
    cleaned.attributes = cleanAttributes(cleaned.attributes);
  }
  
  // Очищаем варианты товара
  if (cleaned.variants && Array.isArray(cleaned.variants)) {
    cleaned.variants = cleaned.variants
      .map(variant => cleanVariantData(variant))
      .filter(variant => variant !== null);
  }
  
  return cleaned;
}

/**
 * Проверяет данные товара на наличие проблем
 * @param {Object} product - данные товара
 * @returns {Array} - массив найденных проблем
 */
export function validateProductData(product) {
  const issues = [];
  
  if (!product) {
    issues.push('Отсутствуют данные товара');
    return issues;
  }
  
  // Проверяем обязательные поля
  if (!product.id || typeof product.id !== 'string' || product.id.trim() === '') {
    issues.push('Отсутствует или невалидный ID товара');
  }
  
  if (!product.name || typeof product.name !== 'string' || product.name.trim() === '') {
    issues.push('Отсутствует или невалидное название товара');
  }
  
  // Проверяем атрибуты
  if (product.attributes) {
    for (const [key, value] of Object.entries(product.attributes)) {
      if (!isValidAttributeValue(value)) {
        issues.push(`Невалидный атрибут товара: ${key} = ${JSON.stringify(value)}`);
      }
    }
  }
  
  // Проверяем варианты
  if (product.variants && Array.isArray(product.variants)) {
    product.variants.forEach((variant, index) => {
      if (!variant.name || typeof variant.name !== 'string' || variant.name.trim() === '') {
        issues.push(`Вариант #${index + 1}: отсутствует название`);
      }
      
      if (!variant.price || !variant.price.value || isNaN(variant.price.value) || variant.price.value <= 0) {
        issues.push(`Вариант #${index + 1} (${variant.name || 'без названия'}): невалидная цена`);
      }
      
      if (variant.attributes) {
        for (const [key, value] of Object.entries(variant.attributes)) {
          if (!isValidAttributeValue(value)) {
            issues.push(`Вариант #${index + 1} (${variant.name || 'без названия'}): невалидный атрибут ${key} = ${JSON.stringify(value)}`);
          }
        }
      }
    });
  }
  
  return issues;
}

/**
 * Создает отчет о проблемах в данных товаров
 * @param {Array} products - массив товаров
 * @returns {Object} - отчет с проблемами
 */
export function generateDataReport(products) {
  const report = {
    totalProducts: products.length,
    productsWithIssues: 0,
    totalIssues: 0,
    issues: []
  };
  
  products.forEach((product, index) => {
    const productIssues = validateProductData(product);
    if (productIssues.length > 0) {
      report.productsWithIssues++;
      report.totalIssues += productIssues.length;
      report.issues.push({
        productIndex: index,
        productId: product.id || `Товар #${index + 1}`,
        productName: product.name || 'Без названия',
        issues: productIssues
      });
    }
  });
  
  return report;
}

// Экспорт для использования в браузере
if (typeof window !== 'undefined') {
  window.dataValidation = {
    isValidAttributeValue,
    cleanAttributes,
    cleanVariantData,
    cleanProductData,
    validateProductData,
    generateDataReport
  };
}
