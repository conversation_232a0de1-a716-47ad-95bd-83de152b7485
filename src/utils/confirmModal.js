/**
 * Утилита для работы с модальным окном подтверждения
 * Предоставляет удобный API для показа красивых подтверждений вместо стандартного confirm()
 */

/**
 * Показывает модальное окно подтверждения
 * @param {Object} options - Опции для модального окна
 * @param {string} options.title - Заголовок модального окна
 * @param {string} options.message - Текст сообщения
 * @param {string} options.confirmText - Текст кнопки подтверждения
 * @param {string} options.cancelText - Текст кнопки отмены
 * @param {string} options.type - Тип действия ('danger', 'warning', 'info', 'success')
 * @returns {Promise<boolean>} - true если пользователь подтвердил, false если отменил
 */
export async function showConfirm(options = {}) {
  // Проверяем, что модальное окно инициализировано
  if (!window.confirmModal) {
    console.error('ConfirmModal не инициализирован. Убедитесь, что компонент ConfirmModal.astro добавлен на страницу.');
    // Fallback к стандартному confirm
    return confirm(options.message || 'Вы уверены?');
  }

  // Настройки по умолчанию в зависимости от типа
  const typeDefaults = {
    danger: {
      title: 'Подтверждение удаления',
      confirmText: 'Удалить',
      cancelText: 'Отмена',
      confirmButtonClass: 'bg-red-600 hover:bg-red-700 text-white'
    },
    warning: {
      title: 'Внимание',
      confirmText: 'Продолжить',
      cancelText: 'Отмена',
      confirmButtonClass: 'bg-yellow-600 hover:bg-yellow-700 text-white'
    },
    info: {
      title: 'Подтверждение',
      confirmText: 'ОК',
      cancelText: 'Отмена',
      confirmButtonClass: 'bg-blue-600 hover:bg-blue-700 text-white'
    },
    success: {
      title: 'Подтверждение',
      confirmText: 'Продолжить',
      cancelText: 'Отмена',
      confirmButtonClass: 'bg-green-600 hover:bg-green-700 text-white'
    }
  };

  // Объединяем настройки
  const defaults = typeDefaults[options.type] || typeDefaults.info;
  const finalOptions = {
    ...defaults,
    ...options
  };

  try {
    return await window.confirmModal.show(finalOptions);
  } catch (error) {
    console.error('Ошибка при показе модального окна:', error);
    // Fallback к стандартному confirm
    return confirm(finalOptions.message || 'Вы уверены?');
  }
}

/**
 * Быстрые методы для разных типов подтверждений
 */

/**
 * Подтверждение удаления
 * @param {string} itemName - Название удаляемого элемента
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function confirmDelete(itemName = 'этот элемент', options = {}) {
  return showConfirm({
    type: 'danger',
    title: 'Подтверждение удаления',
    message: `Вы уверены, что хотите удалить ${itemName}? Это действие нельзя отменить.`,
    confirmText: 'Удалить',
    cancelText: 'Отмена',
    ...options
  });
}

/**
 * Подтверждение сохранения изменений
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function confirmSave(options = {}) {
  return showConfirm({
    type: 'success',
    title: 'Сохранение изменений',
    message: 'Сохранить внесенные изменения?',
    confirmText: 'Сохранить',
    cancelText: 'Отмена',
    ...options
  });
}

/**
 * Предупреждение о потере данных
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function confirmLeave(options = {}) {
  return showConfirm({
    type: 'warning',
    title: 'Несохраненные изменения',
    message: 'У вас есть несохраненные изменения. Вы уверены, что хотите покинуть страницу?',
    confirmText: 'Покинуть',
    cancelText: 'Остаться',
    ...options
  });
}

/**
 * Общее информационное подтверждение
 * @param {string} message - Сообщение для подтверждения
 * @param {Object} options - Дополнительные опции
 * @returns {Promise<boolean>}
 */
export async function confirmAction(message, options = {}) {
  return showConfirm({
    type: 'info',
    message,
    ...options
  });
}
