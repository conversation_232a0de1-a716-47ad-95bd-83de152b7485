/**
 * Утилита для автоматического генерирования уникальных ID товаров
 * Формат ID: КАТЕГОРИЯ-ХХХ (например: TB-001, BR-002)
 */

import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const productsPath = path.join(__dirname, '../../data/product/products.json');
const categoriesPath = path.join(__dirname, '../../data/product/categories.json');

/**
 * Получает ID категории по её названию
 * @param {string} categoryName - Название категории
 * @returns {Promise<string|null>} - ID категории или null, если не найдена
 */
export async function getCategoryIdByName(categoryName) {
  try {
    const categoriesData = await fs.readFile(categoriesPath, 'utf-8');
    const categories = JSON.parse(categoriesData);

    const category = categories.categories.find(cat => cat.name === categoryName);
    return category ? category.id : null;
  } catch (error) {
    console.error('Ошибка при получении ID категории:', error);
    return null;
  }
}

/**
 * Получает следующий доступный номер для товара в категории
 * @param {string} categoryId - ID категории (например, "TB", "BR")
 * @returns {Promise<number>} - Следующий доступный номер
 */
export async function getNextProductNumber(categoryId) {
  try {
    const productsData = await fs.readFile(productsPath, 'utf-8');
    const products = JSON.parse(productsData);

    // Находим все товары данной категории
    const categoryProducts = products.filter(product =>
      product.id.startsWith(`${categoryId}-`)
    );

    // Извлекаем номера из ID товаров
    const existingNumbers = categoryProducts.map(product => {
      const match = product.id.match(new RegExp(`^${categoryId}-(\\d+)$`));
      return match ? parseInt(match[1], 10) : 0;
    }).filter(num => !isNaN(num));

    // Находим максимальный номер и добавляем 1
    const maxNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) : 0;
    return maxNumber + 1;

  } catch (error) {
    console.error('Ошибка при получении следующего номера товара:', error);
    return 1; // Возвращаем 1 как fallback
  }
}

/**
 * Генерирует уникальный ID товара
 * @param {string} categoryName - Название категории
 * @returns {Promise<string|null>} - Сгенерированный ID товара или null при ошибке
 */
export async function generateProductId(categoryName) {
  try {
    // Получаем ID категории
    const categoryId = await getCategoryIdByName(categoryName);
    if (!categoryId) {
      throw new Error(`Категория "${categoryName}" не найдена`);
    }

    // Получаем следующий номер
    const nextNumber = await getNextProductNumber(categoryId);

    // Форматируем номер с ведущими нулями (3 цифры)
    const formattedNumber = nextNumber.toString().padStart(3, '0');

    // Возвращаем сгенерированный ID
    return `${categoryId}-${formattedNumber}`;

  } catch (error) {
    console.error('Ошибка при генерации ID товара:', error);
    return null;
  }
}

/**
 * Проверяет, существует ли товар с данным ID
 * @param {string} productId - ID товара для проверки
 * @returns {Promise<boolean>} - true, если товар существует
 */
export async function productIdExists(productId) {
  try {
    const productsData = await fs.readFile(productsPath, 'utf-8');
    const products = JSON.parse(productsData);

    return products.some(product => product.id === productId);
  } catch (error) {
    console.error('Ошибка при проверке существования ID товара:', error);
    return false;
  }
}

/**
 * Получает список всех категорий с их ID
 * @returns {Promise<Array>} - Массив объектов категорий
 */
export async function getAllCategories() {
  try {
    const categoriesData = await fs.readFile(categoriesPath, 'utf-8');
    const categories = JSON.parse(categoriesData);

    return categories.categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      slug: cat.slug
    }));
  } catch (error) {
    console.error('Ошибка при получении списка категорий:', error);
    return [];
  }
}

/**
 * Получает статистику по товарам в каждой категории
 * @returns {Promise<Object>} - Объект со статистикой по категориям
 */
export async function getCategoryStats() {
  try {
    const productsData = await fs.readFile(productsPath, 'utf-8');
    const products = JSON.parse(productsData);
    const categories = await getAllCategories();

    const stats = {};

    categories.forEach(category => {
      const categoryProducts = products.filter(product =>
        product.id.startsWith(`${category.id}-`)
      );

      const existingNumbers = categoryProducts.map(p => {
        const match = p.id.match(new RegExp(`^${category.id}-(\\d+)$`));
        return match ? parseInt(match[1], 10) : 0;
      }).filter(num => !isNaN(num));

      const lastNumber = existingNumbers.length > 0 ? Math.max(...existingNumbers) : 0;

      stats[category.id] = {
        name: category.name,
        count: categoryProducts.length,
        lastNumber: lastNumber,
        nextId: `${category.id}-${(lastNumber + 1).toString().padStart(3, '0')}`
      };
    });

    return stats;
  } catch (error) {
    console.error('Ошибка при получении статистики категорий:', error);
    return {};
  }
}
