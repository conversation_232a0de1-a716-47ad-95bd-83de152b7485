---

import MainLayout from './MainLayout.astro';
import Header from '../components/header/Header.astro';
import Footer from '../components/footer/Footer.astro';
import ShoppingCart from '../components/cart/ShoppingCart.astro';
import ToastNotification from '../components/ui/ToastNotification.astro';

interface Props {
  title: string;
  description?: string;
  pageTitle?: string;
  showBreadcrumbs?: boolean;
  breadcrumbs?: Array<{ text: string; url: string }>;
  showSidebar?: boolean;
  hideTitleSection?: boolean;
}

const {
  title,
  description,
  pageTitle,
  showBreadcrumbs = true,
  breadcrumbs = [],
  showSidebar = true,
  hideTitleSection = false
} = Astro.props;

// Определяем, нужно ли показывать корзину (только на страницах /products)
const currentPath = Astro.url.pathname;
const showCart = currentPath.startsWith('/products');
---

<MainLayout title={title} description={description}>
  <Header slot="header" />
  
  {/* Page Title Section */}
  {hideTitleSection ? null : (
    <section class="bg-gray-100 py-8 sm:py-10 lg:py-14">
      <div class="container mx-auto px-4">
        <div class="relative">
          {/* Заголовок по центру */}
          <div class="text-center">
            <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold">{pageTitle || title}</h1>
            {showBreadcrumbs && breadcrumbs.length > 0 && (
              <nav class="flex justify-center mt-1 sm:mt-1.5 lg:mt-2" aria-label="Breadcrumb">
                <ol class="flex items-center space-x-2 text-xs sm:text-sm text-gray-600">
                  <li>
                    <a href="/" class="hover:text-primary">Главная</a>
                  </li>
                  {breadcrumbs.map((item, index) => (
                    <li class="flex items-center space-x-2">
                      <span class="mx-2">></span>
                      {index === breadcrumbs.length - 1 ? (
                        <span class="text-primary">{item.text}</span>
                      ) : (
                        <a href={item.url} class="hover:text-primary">
                          {item.text}
                        </a>
                      )}
                    </li>
                  ))}
                </ol>
              </nav>
            )}
          </div>

          {/* Корзина справа */}
          {showCart && (
            <div class="absolute right-0 top-0 bottom-0 flex items-center">
              <ShoppingCart showCart={true} />
            </div>
          )}
        </div>
      </div>
    </section>
  )}

  <slot />

  <Footer slot="footer" />

  <!-- Toast notifications для уведомлений о добавлении в корзину -->
  {showCart && <ToastNotification />}
</MainLayout>