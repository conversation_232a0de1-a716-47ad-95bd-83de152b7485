---
import Footer from '../components/footer/Footer.astro';
import FooterLinks from '../components/footer/FooterLinks.astro';
interface Props {
  title: string;
  description?: string;
}

const {
  title,
  description = "Aizen - Modern Architecture Template"
} = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content={description} />
    <title>{title}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/images/favicon.png" />

    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/styles/global.css" />

    <!-- Tooltip <PERSON>ript -->
    <script src="/js/tooltip.js" is:inline></script>
  </head>
  <body class="min-h-screen bg-white">
    <!-- Preloader -->
    <div id="preloader" class="fixed inset-0 z-50 flex items-center justify-center bg-white">
      <div class="h-16 w-16 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
    </div>

    <!-- Main Content -->
    <div class="page-wrapper">
      <slot name="header" />
      <main>
        <slot />
      </main>
    </div>

    <!-- Back to Top Button -->
    <button
      id="backToTop"
      class="fixed bottom-8 right-8 z-40 hidden rounded-none bg-primary p-3 text-white shadow-lg transition-all hover:bg-primary-dark w-10 h-10 items-center justify-center"
      aria-label="Back to top"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        class="h-6 w-6"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 10l7-7m0 0l7 7m-7-7v18"
        />
      </svg>
    </button>

    <!-- Scripts -->
    <script>
      // Preloader
      window.addEventListener('load', () => {
        const preloader = document.getElementById('preloader');
        if (preloader) {
          preloader.style.display = 'none';
        }
      });

      // Back to Top Button
      const backToTop = document.getElementById('backToTop');
      if (backToTop) {
        window.addEventListener('scroll', () => {
          if (window.scrollY > 300) {
            backToTop.classList.remove('hidden');
          } else {
            backToTop.classList.add('hidden');
          }
        });

        backToTop.addEventListener('click', () => {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        });
      }
    </script>

    <style>
      #backToTop:not(.hidden) {
        display: flex;
      }
    </style>

    <Footer />
    <FooterLinks />

  </body>
</html>
