// Global tooltip functionality using event delegation
function initTooltips() {
  // Remove existing listeners to prevent duplicates
  document.removeEventListener('click', handleTooltipClick);
  document.removeEventListener('mouseover', handleTooltipMouseEnter);
  document.removeEventListener('mouseout', handleTooltipMouseLeave);

  // Add event listeners using delegation
  document.addEventListener('click', handleTooltipClick);
  document.addEventListener('mouseover', handleTooltipMouseEnter);
  document.addEventListener('mouseout', handleTooltipMouseLeave);
}

function handleTooltipClick(e) {
  // Check if clicked element is a tooltip trigger
  if (e.target.closest('.tooltip-trigger')) {
    e.preventDefault();
    e.stopPropagation();

    const container = e.target.closest('.tooltip-container');
    if (!container) return;

    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) return;

    // Hide all other tooltips
    document.querySelectorAll('.tooltip').forEach(t => {
      if (t !== tooltip) {
        t.style.opacity = '0';
        t.style.pointerEvents = 'none';
      }
    });

    // Toggle current tooltip
    if (tooltip.style.opacity === '1') {
      tooltip.style.opacity = '0';
      tooltip.style.pointerEvents = 'none';
    } else {
      tooltip.style.opacity = '1';
      tooltip.style.pointerEvents = 'auto';
    }
  }
  // Hide tooltips when clicking outside
  else if (!e.target.closest('.tooltip-container')) {
    document.querySelectorAll('.tooltip').forEach(tooltip => {
      tooltip.style.opacity = '0';
      tooltip.style.pointerEvents = 'none';
    });
  }
}

function handleTooltipMouseEnter(e) {
  if (e.target.closest('.tooltip-trigger')) {
    const container = e.target.closest('.tooltip-container');
    if (!container) return;

    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) return;

    tooltip.style.opacity = '1';
    tooltip.style.pointerEvents = 'auto';
  }
}

function handleTooltipMouseLeave(e) {
  if (e.target.closest('.tooltip-trigger')) {
    const container = e.target.closest('.tooltip-container');
    if (!container) return;

    const tooltip = container.querySelector('.tooltip');
    if (!tooltip) return;

    tooltip.style.opacity = '0';
    tooltip.style.pointerEvents = 'none';
  }
}

// Initialize tooltips when DOM is loaded
document.addEventListener('DOMContentLoaded', initTooltips);

// Also try to initialize immediately if DOM is already loaded
if (document.readyState !== 'loading') {
  initTooltips();
}

// Re-initialize tooltips when new content is added (for dynamic content)
window.initTooltips = initTooltips;
