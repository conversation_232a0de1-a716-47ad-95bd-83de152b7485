# Документация компонентов хедера

## Обзор
Компоненты хедера разработаны для обеспечения адаптивного и удобного навигационного опыта на всех устройствах. Хедер состоит из нескольких компонентов, которые работают вместе для создания единой системы навигации.

## Структура компонентов

### 1. Header.astro
Основной компонент хедера, объединяющий все остальные компоненты.

**Функциональность:**
- Адаптивный дизайн
- Фиксированный хедер при прокрутке
- Отображение логотипа
- Интеграция навигационного меню

**Использование:**
```astro
---
import Header from '../components/header/Header.astro';
---

<Header />
```

### 2. HeaderTop.astro
Верхняя секция хедера, содержащая приветственное сообщение и социальные ссылки.

**Функциональность:**
- Приветственное сообщение
- Ссылки на социальные сети
- Адаптивная верстка

**Использование:**
```astro
---
import HeaderTop from '../components/header/HeaderTop.astro';
---

<HeaderTop />
```

### 3. Navigation.astro
Основной компонент навигации с поддержкой мобильных устройств.

**Функциональность:**
- Десктопное и мобильное меню
- Поддержка выпадающих подменю
- Переключатель мобильного меню
- Закрытие по клику вне меню
- Эффекты при наведении

**Параметры:**
```typescript
interface MenuItem {
  text: string;    // Текст пункта меню
  url: string;     // URL для перехода
  children?: MenuItem[]; // Дочерние пункты меню
}
```

**Использование:**
```astro
---
import Navigation from '../components/header/Navigation.astro';
---

<Navigation />
```

### 4. SocialLinks.astro
Компонент для отображения ссылок на социальные сети.

**Функциональность:**
- Настраиваемые социальные ссылки
- Эффекты при наведении
- Обработка внешних ссылок

**Использование:**
```astro
---
import SocialLinks from '../components/utils/SocialLinks.astro';
---

<SocialLinks />
```

## Мобильная адаптация

Компоненты хедера полностью адаптивны со следующими брейкпоинтами:
- Мобильные: < 768px
- Планшеты: 768px - 1024px
- Десктоп: > 1024px

### Мобильные функции:
- Переключатель меню-бургер
- Полноширинное мобильное меню
- Удобная навигация для тач-устройств
- Плавные анимации

### Десктопные функции:
- Горизонтальное расположение меню
- Выпадающие подменю
- Фиксированный хедер при прокрутке
- Плавные переходы

## Стилизация

Компоненты используют Tailwind CSS для стилизации со следующими пользовательскими классами:
- `bg-primary`: Основной цвет фона
- `text-primary`: Основной цвет текста
- `hover:text-primary`: Цвет при наведении

## JavaScript функциональность

### Мобильное меню
```javascript
// Переключение мобильного меню
const mobileMenuButton = document.getElementById('mobile-menu-button');
const mobileMenu = document.getElementById('mobile-menu');

mobileMenuButton.addEventListener('click', () => {
  mobileMenu.classList.toggle('hidden');
});
```

### Фиксированный хедер
```javascript
// Фиксированный хедер при прокрутке
const stickyHeader = document.getElementById('sticky-header');
let lastScroll = 0;

window.addEventListener('scroll', () => {
  const currentScroll = window.pageYOffset;
  // Показать/скрыть хедер в зависимости от направления прокрутки
});
```

## Лучшие практики

1. **Доступность:**
   - Использование ARIA-атрибутов
   - Поддержка клавиатурной навигации
   - Соблюдение контраста цветов
   - Альтернативные тексты

2. **Производительность:**
   - Отложенная загрузка изображений
   - Оптимизация анимаций
   - Минимизация JavaScript
   - Эффективное использование CSS

3. **SEO:**
   - Семантическая структура HTML
   - Правильная иерархия заголовков
   - Мета-описания
   - Альтернативные тексты для изображений

## Настройка

Для настройки компонентов хедера:

1. **Цвета:**
   - Изменение основных цветов в конфигурации Tailwind
   - Обновление состояний при наведении
   - Настройка цветов фона

2. **Макет:**
   - Настройка отступов контейнера
   - Изменение расстояний между элементами
   - Изменение размеров логотипа

3. **Навигация:**
   - Обновление пунктов меню в Navigation.astro
   - Настройка поведения выпадающих меню
   - Изменение брейкпоинтов для мобильных устройств

## Зависимости

- Tailwind CSS
- Astro
- Пользовательские иконки (можно заменить на Iconify или другие библиотеки иконок) 