# Компонент Features (Характеристики продукции)

## Назначение
Компонент для отображения ключевых характеристик продукции на главной странице. Основан на дизайне секции "Our Services" из оригинального шаблона Aizen, адаптирован под Tailwind и Astro.

## Структура
- Заголовок секции
- Подзаголовок с описанием
- Сетка из трех карточек характеристик:
  - Иконка (через Iconify)
  - Заголовок характеристики
  - Описание

## Использование
```astro
import Features from '../components/features/Features.astro';

<Features />
```

## Кастомизация
### Изменение текста
Все тексты находятся в файле `src/components/features/Features.astro`:
- Заголовок секции
- Подзаголовок
- Названия и описания характеристик

### Изменение иконок
Иконки используются через Iconify. Для изменения:
1. Найдите нужную иконку на [Iconify](https://icon-sets.iconify.design/)
2. Замените значение `data-icon` в соответствующем элементе

### Изменение стилей
Все стили реализованы через Tailwind:
- Цвета: используется фирменный цвет `#c8b499`
- Отступы: `py-20` для секции, `p-6` для карточек
- Тени: `shadow-md` для карточек
- Сетка: `grid-cols-1 md:grid-cols-3` для адаптивности





# Документация: Компонент Features (Характеристики продукции)

## Назначение
Секция для вывода ключевых характеристик продукции в виде карточек.

## Структура
- Секция с оранжевым фоном, заголовком и подзаголовком
- 6 карточек, каждая — отдельный компонент FeatureCard
- В каждой карточке: иконка (png), заголовок, описание

## Как изменить карточки

### Изменить иконку
- Путь к иконке задаётся через проп `icon` в компоненте FeatureCard
- По умолчанию используется `/images/featurecard/rain-cloud.png`
- Для замены используйте свой png/svg в папке `public/images/featurecard/` и укажите путь:
  ```astro
  <FeatureCard icon="/images/featurecard/your-icon.png" ... />
  ```

### Изменить текст
- Заголовок и описание карточки задаются через пропсы `title` и `description`:
  ```astro
  <FeatureCard title="Новый заголовок" description="Новое описание" ... />
  ```

### Изменить порядок карточек
- Просто переставьте вызовы `<FeatureCard ... />` в файле `src/components/features/Features.astro` в нужном порядке

### Добавить/удалить карточку
- Чтобы добавить: скопируйте и вставьте блок `<FeatureCard ... />` с нужными пропсами
- Чтобы удалить: удалите соответствующий вызов `<FeatureCard ... />`

## Пример использования
```astro
<FeatureCard
  icon="/images/featurecard/rain-cloud.png"
  title="Морозостойкость"
  description="Высокая морозостойкость"
/>
```

## Адаптивность и стили
- Все стили через Tailwind
- На мобильных — одна колонка, на десктопе — три
- Иконка центрируется, размер 64x64px

## Доступность
- Иконка с `alt=""` и `aria-hidden="true"` (декоративная)
- Семантическая разметка с использованием `section` и заголовков
- Контрастные цвета для текста
- Иконки через Iconify с поддержкой скринридеров 