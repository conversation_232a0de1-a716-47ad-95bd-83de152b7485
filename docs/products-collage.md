# Документация: Секция "Наша продукция" (ProductsCollage)

## Назначение
Секция-коллаж для вывода категорий продукции в виде современного masonry/grid-коллажа из 6 блоков. Каждый блок — отдельная категория с картинкой, названием, описанием и ссылкой.

## Структура
- Файл секции: `src/components/products/ProductsCollage.astro`
- Карточка категории: `src/components/products/ProductCategoryCard.astro`
- В коллаже 6 блоков, расположение строго по макету (см. grid-template-areas в коде)

## Как изменить карточки
### Изменить изображение
- В пропсе `image` укажите путь к нужному файлу (рекомендуется класть изображения в `public/images/products/`)
- Пример:
  ```astro
  <ProductCategoryCard image="/images/products/new-category.jpg" ... />
  ```

### Изменить название и описание
- Пропсы `title` и `description` задают текст в карточке:
  ```astro
  <ProductCategoryCard title="Новое название" description="Новое описание" ... />
  ```

### Изменить ссылку
- Пропс `link` задаёт адрес перехода при клике на карточку:
  ```astro
  <ProductCategoryCard link="/products/new-category" ... />
  ```

### Изменить порядок или количество блоков
- Порядок и размеры блоков определяются grid-обёрткой в ProductsCollage.astro (см. grid-area: a, b, c, d, e, f)
- Чтобы поменять местами категории, просто переставьте соответствующие div с ProductCategoryCard внутри grid
- Для добавления/удаления блоков потребуется скорректировать grid-template-areas и количество карточек

## Адаптивность
- На десктопе: masonry/grid-коллаж из 6 блоков (см. макет)
- На мобильных и планшетах: все блоки идут друг за другом (одна колонка)
- Высота блоков адаптируется через min-height и медиазапросы (см. встроенный style в секции)

## Рекомендации по изображениям
- Используйте изображения с соотношением сторон, соответствующим размеру блока (квадратные для больших, прямоугольные для нижних)
- Оптимальный размер: не менее 600x600px для крупных блоков, 400x400px для малых
- Все изображения должны быть размещены в public/images/products/

## Пример карточки
```astro
<ProductCategoryCard
  image="/images/products/plitka.jpg"
  title="Плитка тротуарная"
  description="Разнообразие форм и цветов для любых задач"
  link="/products/plitka"
/>
```

## Пример добавления новой категории (требует изменения grid)
1. Добавьте новый div с ProductCategoryCard в нужное место внутри grid
2. Обновите grid-template-areas, columns и rows для корректного отображения

## Доступность
- Все изображения имеют alt с названием категории
- Карточки реализованы как ссылки для удобства навигации

## Важно
- Не используйте скругления, чтобы сохранить фирменный стиль
- Для сложных изменений структуры рекомендуется проконсультироваться с разработчиком 