# Быстрый справочник по системе вариантов

## 🚀 Краткий обзор

### Когда базовая цена АКТИВНА ✅
- Нет вариантов
- Есть варианты без цен  
- Есть варианты с ценами, но НЕТ основной цены

### Когда базовая цена НЕАКТИВНА ❌
- Есть варианты с ценами И есть основная цена

## 🎯 Быстрые действия

### Создать товар без вариантов
1. Заполните название и описание
2. Установите базовую цену
3. Сохраните товар

### Создать товар с одинаковыми ценами
1. Установите базовую цену
2. Добавьте варианты с атрибутами
3. НЕ отмечайте "Основная цена"
4. Сохраните товар

### Создать товар с разными ценами
1. НЕ заполняйте базовую цену
2. Добавьте варианты с разными ценами
3. Отметьте ОДИН вариант как "Основная цена"
4. Сохраните товар

### Изменить основную цену
1. Снимите флаг с текущего основного варианта
2. Поставьте флаг на новый вариант
3. Сохраните изменения

## 🔧 Основные функции

```javascript
// Проверить состояние
hasVariantsWithPricing()     // Есть ли варианты с ценами
hasPrimaryPriceVariant()     // Есть ли основная цена
toggleBasePriceFields()      // Обновить состояние полей

// Получить данные
getProductVariants()         // Все варианты товара
collectVariantsData()        // Собрать данные из DOM

// События
addVariantChangeListener()   // Слушать изменения
notifyVariantChange()        // Уведомить об изменении
```

## 📊 Структура данных

### Минимальный вариант
```json
{
  "id": "PRODUCT-001-01",
  "name": "Название варианта",
  "price": {
    "value": 100,
    "currency": "BYN", 
    "unit": "piece"
  },
  "isPrimaryPrice": false,
  "attributes": {}
}
```

### Полный вариант
```json
{
  "id": "PRODUCT-001-01",
  "name": "Красный велюр",
  "price": {
    "value": 200,
    "currency": "BYN",
    "unit": "piece"
  },
  "sku": "PRODUCT-001-01",
  "isPrimaryPrice": true,
  "attributes": {
    "colors": ["Красный"],
    "material": {
      "id": "velur",
      "name": "Велюр"
    }
  }
}
```

## 🎨 Визуальные индикаторы

### Поля базовой цены
- **Активные**: Белый фон, можно редактировать
- **Неактивные**: Серый фон, заблокированы

### Варианты
- **С основной ценой**: Зеленый бейдж "Основная цена"
- **Обычные**: Без специальных меток
- **В редактировании**: Развернутая форма
- **Сохраненные**: Компактный вид

## ⚡ Горячие клавиши

| Действие | Клавиши |
|----------|---------|
| Добавить вариант | `Ctrl + Alt + V` |
| Сохранить товар | `Ctrl + S` |
| Отменить изменения | `Ctrl + Z` |

## 🔍 Отладка

### Проверка в консоли
```javascript
// Состояние системы
console.log('Варианты:', window.getProductVariants());
console.log('Есть варианты с ценами:', window.hasVariantsWithPricing());
console.log('Есть основная цена:', window.hasPrimaryPriceVariant());

// Принудительное обновление
window.toggleBasePriceFields();
```

### Частые проблемы
- **Базовая цена не активируется** → Проверьте флаги основной цены
- **Варианты не сохраняются** → Заполните все обязательные поля
- **Цена не обновляется** → Перезагрузите страницу

## 📋 Чек-лист создания товара

### Подготовка
- [ ] Определить тип товара (с вариантами / без)
- [ ] Решить про ценообразование (одинаковые / разные цены)
- [ ] Подготовить атрибуты и их значения

### Создание
- [ ] Заполнить основную информацию
- [ ] Установить базовую цену (если нужно)
- [ ] Добавить варианты (если нужно)
- [ ] Выбрать основную цену (если цены разные)
- [ ] Проверить отображение цены покупателя

### Проверка
- [ ] Протестировать переключение вариантов
- [ ] Убедиться в корректности цен
- [ ] Проверить сохранение данных
- [ ] Посмотреть отображение в каталоге

## 🚨 Критические правила

### ❌ НИКОГДА НЕ ДЕЛАЙТЕ
- Не ставьте основную цену у нескольких вариантов
- Не оставляйте пустые цены в вариантах
- Не удаляйте последний вариант с основной ценой без установки базовой цены

### ✅ ВСЕГДА ДЕЛАЙТЕ  
- Заполняйте все поля цены (значение, валюта, единица)
- Проверяйте цену покупателя после изменений
- Сохраняйте товар после изменения вариантов

## 📞 Поддержка

### Файлы для изучения
- `src/utils/variantManager.js` - основная логика
- `doc/variants-pricing-system.md` - полная документация
- `doc/variants-examples.md` - примеры использования

### Тестовые товары
- **TB-001** - варианты без основной цены
- **ME-007** - варианты с основной ценой
- **BD-001** - товар без вариантов

### Страницы для тестирования
- `/admin/products/new` - создание товара
- `/admin/products/edit/TB-001` - редактирование
- `/admin/products/edit/ME-007` - редактирование

---

*Справочник актуален на: 2025-01-14*
*Версия: 2.0*
