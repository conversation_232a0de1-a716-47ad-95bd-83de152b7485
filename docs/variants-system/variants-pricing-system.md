# Система вариантов товаров и ценообразования

## 📋 Обзор системы

Система вариантов товаров позволяет создавать различные конфигурации одного товара с разными атрибутами (цвет, размер, материал и т.д.) и ценами. Каждый вариант может иметь свою цену, а один из вариантов может быть отмечен как "основная цена" для отображения в каталоге.

## 🏗️ Архитектура системы

### Основные компоненты

1. **Базовая цена товара** - цена, указанная в основной форме товара
2. **Варианты товара** - различные конфигурации товара с собственными атрибутами и ценами
3. **Основная цена варианта** - цена варианта, помеченная как основная для отображения покупателям

### Файловая структура

```
src/
├── utils/
│   └── variantManager.js          # Основная логика управления вариантами
├── pages/admin/products/
│   ├── new.astro                  # Страница создания товара
│   └── edit/[id].astro           # Страница редактирования товара
└── components/
    └── products/
        └── ProductVariants.astro  # Компонент отображения вариантов
```

## 💰 Логика ценообразования

### Приоритеты цен

Система определяет итоговую цену товара по следующему приоритету:

1. **Основная цена варианта** (высший приоритет)
   - Цена из варианта, помеченного как `isPrimaryPrice: true`
   - Отображается покупателям в каталоге и карточке товара

2. **Базовая цена товара** (средний приоритет)
   - Цена, указанная в основной форме товара
   - Используется когда нет основной цены варианта

3. **Цена не указана** (низший приоритет)
   - Отображается как "0" или "Цена не указана"

### Условия активации базовой цены

Поля базовой цены (цена, валюта, единица измерения) активны в следующих случаях:

✅ **Активна когда:**
- Нет вариантов товара
- Есть варианты без цен
- Есть варианты с ценами, но ни один не отмечен как основная цена

❌ **Неактивна когда:**
- Есть варианты с ценами И один из них отмечен как основная цена

## 🔧 Основные функции

### `hasVariantsWithPricing()`
Проверяет наличие вариантов с корректными ценами.

```javascript
export function hasVariantsWithPricing() {
  // Проверяет все варианты на наличие:
  // - цены > 0
  // - валюты
  // - единицы измерения
}
```

### `hasPrimaryPriceVariant()`
Проверяет наличие варианта с основной ценой.

```javascript
export function hasPrimaryPriceVariant() {
  // Проверяет есть ли вариант с isPrimaryPrice: true
}
```

### `toggleBasePriceFields()`
Управляет состоянием полей базовой цены.

```javascript
export function toggleBasePriceFields() {
  const hasVariants = hasVariantsWithPricing();
  const hasPrimaryPrice = hasPrimaryPriceVariant();
  
  // Отключает базовую цену только если:
  // hasVariants && hasPrimaryPrice
}
```

## 📊 Структура данных

### Товар с базовой ценой
```json
{
  "id": "TB-001",
  "name": "Тротуарная плитка",
  "basePrice": {
    "value": 450,
    "currency": "BYN",
    "unit": "m2"
  },
  "variants": []
}
```

### Товар с вариантами
```json
{
  "id": "ME-007",
  "name": "Кресло",
  "basePrice": null,
  "variants": [
    {
      "id": "ME-007-01",
      "name": "Кожа черная",
      "price": {
        "value": 980,
        "currency": "USD", 
        "unit": "piece"
      },
      "isPrimaryPrice": true,
      "attributes": {
        "material": "Кожа",
        "colors": ["Черный"]
      }
    }
  ]
}
```

## 🎯 Сценарии использования

### Сценарий 1: Товар без вариантов
- **Состояние**: Базовая цена активна
- **Отображение**: Цена из basePrice
- **Пример**: Простой товар с одной ценой

### Сценарий 2: Товар с вариантами без основной цены
- **Состояние**: Базовая цена активна
- **Отображение**: Цена из basePrice
- **Пример**: Товар с вариантами цветов, но общая цена

### Сценарий 3: Товар с основной ценой варианта
- **Состояние**: Базовая цена неактивна
- **Отображение**: Цена из основного варианта
- **Пример**: Товар с разными ценами по материалам

## 🔄 Интерактивность

### Автоматическое переключение
Система автоматически переключает состояние полей при:
- Добавлении/удалении вариантов
- Изменении цен в вариантах
- Установке/снятии флага "Основная цена"

### События обновления
```javascript
// При изменении варианта
priceInput.addEventListener('input', toggleBasePriceFields);

// При изменении основной цены
checkbox.addEventListener('change', handlePrimaryPriceChange);
```

## 🎨 Визуальные индикаторы

### Состояния полей базовой цены
- **Активные**: Белый фон, черный текст
- **Неактивные**: Серый фон (`bg-gray-100`), серый текст (`text-gray-500`)

### Бейджи вариантов
- **Основная цена**: Зеленый бейдж "Основная цена"
- **Обычный вариант**: Без специальных меток

## 🔍 Отладка и тестирование

### Проверка состояния
```javascript
// В консоли браузера
console.log('Есть варианты с ценами:', hasVariantsWithPricing());
console.log('Есть основная цена:', hasPrimaryPriceVariant());
```

### Тестовые товары
- **TB-001**: Варианты без основной цены
- **ME-007**: Варианты с основной ценой
- **Новый товар**: Без вариантов

## 📝 Примеры кода

### Создание варианта с основной ценой
```javascript
const variant = {
  id: "PRODUCT-001-01",
  name: "Премиум версия",
  price: {
    value: 1000,
    currency: "BYN",
    unit: "piece"
  },
  isPrimaryPrice: true, // Основная цена
  attributes: {
    material: "Премиум материал"
  }
};
```

### Обработка изменения основной цены
```javascript
function handlePrimaryPriceChange(checkbox, variantId) {
  if (checkbox.checked) {
    // Снимаем флаг с других вариантов
    clearOtherPrimaryFlags(variantId);
  }
  
  // Обновляем состояние базовой цены
  toggleBasePriceFields();
}
```

## 🚀 Будущие улучшения

### Планируемые функции
- [ ] Массовое изменение цен вариантов
- [ ] Копирование вариантов между товарами
- [ ] Импорт/экспорт вариантов
- [ ] История изменений цен

### Оптимизации
- [ ] Кэширование состояния вариантов
- [ ] Ленивая загрузка атрибутов
- [ ] Валидация цен в реальном времени

## 🛠️ Техническая реализация

### Жизненный цикл варианта

1. **Создание варианта**
   ```javascript
   // Генерация уникального ID
   const variantId = `variant-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

   // Создание DOM элемента
   const variantRow = createVariantRow(variantId);

   // Добавление обработчиков событий
   addVariantEventListeners(variantRow, variantId);
   ```

2. **Редактирование варианта**
   ```javascript
   // Переключение в режим редактирования
   editVariant(variantId);

   // Сохранение изменений
   saveVariant(variantId);

   // Обновление состояния базовой цены
   toggleBasePriceFields();
   ```

3. **Удаление варианта**
   ```javascript
   // Удаление из DOM
   removeVariantRow(variantId);

   // Обновление состояния
   toggleBasePriceFields();
   notifyVariantChange();
   ```

### Обработка событий

```javascript
// Основные события системы
const events = {
  'variant.created': (variantId) => toggleBasePriceFields(),
  'variant.updated': (variantId) => toggleBasePriceFields(),
  'variant.deleted': (variantId) => toggleBasePriceFields(),
  'variant.primaryChanged': (variantId) => handlePrimaryPriceChange()
};
```

### Валидация данных

```javascript
function validateVariant(variantData) {
  const errors = [];

  if (!variantData.name.trim()) {
    errors.push('Название варианта обязательно');
  }

  if (!variantData.price.value || variantData.price.value <= 0) {
    errors.push('Цена должна быть больше 0');
  }

  if (!variantData.price.currency) {
    errors.push('Валюта обязательна');
  }

  if (!variantData.price.unit) {
    errors.push('Единица измерения обязательна');
  }

  return errors;
}
```

## 📋 API Reference

### Основные методы

#### `window.getProductVariants()`
Возвращает массив всех вариантов товара.

**Возвращает:** `Array<Variant>`

```javascript
const variants = window.getProductVariants();
console.log(variants);
// [
//   {
//     name: "Вариант 1",
//     price: { value: 100, currency: "BYN", unit: "piece" },
//     isPrimaryPrice: true,
//     attributes: {...}
//   }
// ]
```

#### `window.addVariantChangeListener(callback)`
Добавляет слушатель изменений вариантов.

**Параметры:**
- `callback` (Function) - функция обратного вызова

```javascript
window.addVariantChangeListener(() => {
  console.log('Варианты изменились');
  updateCustomerPrice();
});
```

#### `window.toggleBasePriceFields()`
Обновляет состояние полей базовой цены.

```javascript
// Вызывается автоматически при изменениях
window.toggleBasePriceFields();
```

### Утилитарные функции

#### `generateVariantSKU(productId, variantName)`
Генерирует SKU для варианта.

```javascript
const sku = generateVariantSKU('TB-001', 'Красный');
// Результат: "TB-001-01"
```

#### `collectVariantsData()`
Собирает данные всех вариантов из DOM.

```javascript
const variantsData = collectVariantsData();
```

## 🔧 Конфигурация

### Настройки валют
```javascript
// Доступные валюты
const currencies = [
  { code: 'BYN', symbol: 'Br', name: 'Белорусский рубль' },
  { code: 'USD', symbol: '$', name: 'Доллар США' },
  { code: 'EUR', symbol: '€', name: 'Евро' },
  { code: 'RUB', symbol: '₽', name: 'Российский рубль' }
];
```

### Настройки единиц измерения
```javascript
// Доступные единицы
const units = [
  { code: 'piece', name: 'шт', category: 'count' },
  { code: 'm2', name: 'м²', category: 'area' },
  { code: 'kg', name: 'кг', category: 'weight' },
  { code: 'liter', name: 'л', category: 'volume' }
];
```

## 🐛 Устранение неполадок

### Частые проблемы

#### Базовая цена не активируется
**Причина:** Есть вариант с основной ценой
**Решение:** Снимите флаг "Основная цена" со всех вариантов

#### Варианты не сохраняются
**Причина:** Не заполнены обязательные поля
**Решение:** Проверьте название, цену, валюту и единицу измерения

#### Цена покупателя не обновляется
**Причина:** Не работают слушатели событий
**Решение:** Перезагрузите страницу или проверьте консоль на ошибки

### Отладочные команды

```javascript
// Проверка состояния системы
console.log('Варианты:', window.getProductVariants());
console.log('Есть варианты с ценами:', window.hasVariantsWithPricing());
console.log('Есть основная цена:', window.hasPrimaryPriceVariant());

// Принудительное обновление
window.toggleBasePriceFields();
```

## 📊 Метрики и аналитика

### Отслеживаемые события
- Создание варианта
- Изменение цены варианта
- Установка основной цены
- Удаление варианта
- Сохранение товара

### Пример интеграции с аналитикой
```javascript
function trackVariantEvent(action, variantData) {
  // Google Analytics
  gtag('event', 'variant_action', {
    action: action,
    variant_id: variantData.id,
    price: variantData.price.value,
    currency: variantData.price.currency
  });

  // Внутренняя аналитика
  analytics.track('variant_' + action, variantData);
}
```

---

*Документация актуальна на: 2025-01-14*
*Версия системы: 2.0*
*Автор: AI Assistant*
