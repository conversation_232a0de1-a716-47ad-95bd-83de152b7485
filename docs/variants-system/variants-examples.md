# Примеры использования системы вариантов

## 🎯 Практические сценарии

### Сценарий 1: Тротуарная плитка с вариантами цветов

**Описание:** Товар с одинаковой ценой, но разными цветами.

**Настройка:**
- Базовая цена: 450 BYN/м²
- Варианты: Серый, Красный, Желтый
- Основная цена: НЕ установлена

**Результат:** Покупатели видят базовую цену 450 BYN/м²

```json
{
  "id": "TB-001",
  "name": "Тротуарная плитка «Классика»",
  "basePrice": {
    "value": 450,
    "currency": "BYN",
    "unit": "m2"
  },
  "variants": [
    {
      "id": "TB-001-01",
      "name": "Серый",
      "attributes": { "colors": ["Серый"] },
      "price": { "value": 450, "currency": "BYN", "unit": "m2" },
      "isPrimaryPrice": false
    },
    {
      "id": "TB-001-02", 
      "name": "Красный",
      "attributes": { "colors": ["Красный"] },
      "price": { "value": 450, "currency": "BYN", "unit": "m2" },
      "isPrimaryPrice": false
    }
  ]
}
```

### Сценарий 2: Мебель с разными материалами и ценами

**Описание:** Товар с разными ценами в зависимости от материала.

**Настройка:**
- Базовая цена: НЕ используется
- Варианты: Ткань (150 BYN), Кожа (300 BYN), Велюр (200 BYN)
- Основная цена: Ткань (самый популярный)

**Результат:** Покупатели видят цену 150 BYN

```json
{
  "id": "ME-007",
  "name": "Кресло комфорт",
  "basePrice": null,
  "variants": [
    {
      "id": "ME-007-01",
      "name": "Ткань",
      "attributes": { "material": "Ткань" },
      "price": { "value": 150, "currency": "BYN", "unit": "piece" },
      "isPrimaryPrice": true
    },
    {
      "id": "ME-007-02",
      "name": "Кожа", 
      "attributes": { "material": "Кожа" },
      "price": { "value": 300, "currency": "BYN", "unit": "piece" },
      "isPrimaryPrice": false
    },
    {
      "id": "ME-007-03",
      "name": "Велюр",
      "attributes": { "material": "Велюр" },
      "price": { "value": 200, "currency": "BYN", "unit": "piece" },
      "isPrimaryPrice": false
    }
  ]
}
```

### Сценарий 3: Простой товар без вариантов

**Описание:** Товар с одной ценой и без вариантов.

**Настройка:**
- Базовая цена: 25 BYN/шт
- Варианты: НЕТ

**Результат:** Покупатели видят цену 25 BYN/шт

```json
{
  "id": "BD-001",
  "name": "Бордюр садовый",
  "basePrice": {
    "value": 25,
    "currency": "BYN", 
    "unit": "piece"
  },
  "variants": []
}
```

## 🔄 Пошаговые инструкции

### Создание товара с вариантами цветов (одинаковая цена)

1. **Откройте страницу создания товара**
   ```
   /admin/products/new
   ```

2. **Заполните основную информацию**
   - Название: "Тротуарная плитка Классика"
   - Категория: "Тротуарная плитка"
   - Описание: "Классическая плитка для дорожек"

3. **Установите базовую цену**
   - Цена: 450
   - Валюта: BYN
   - Единица: м²

4. **Добавьте варианты цветов**
   - Нажмите "Добавить вариант"
   - Название: "Серый"
   - Выберите атрибут "Цвет" → "Серый"
   - Цена: 450 BYN/м²
   - НЕ отмечайте "Основная цена"

5. **Повторите для других цветов**
   - Красный, Желтый, Коричневый

6. **Результат**
   - Базовая цена остается активной
   - Покупатели видят 450 BYN/м²
   - Могут выбирать цвет при заказе

### Создание товара с разными ценами по материалам

1. **Заполните основную информацию**
   - Название: "Кресло офисное"
   - Категория: "Мебель"

2. **НЕ заполняйте базовую цену**
   - Оставьте поля пустыми

3. **Добавьте варианты материалов**
   
   **Вариант 1 - Ткань (основной):**
   - Название: "Ткань"
   - Атрибут: Материал → Ткань
   - Цена: 150 BYN/шт
   - ✅ Отметьте "Основная цена"

   **Вариант 2 - Кожа:**
   - Название: "Кожа"
   - Атрибут: Материал → Кожа  
   - Цена: 300 BYN/шт
   - ❌ НЕ отмечайте "Основная цена"

4. **Результат**
   - Базовая цена становится неактивной
   - Покупатели видят 150 BYN/шт (из основного варианта)

### Изменение основной цены

1. **Откройте товар с вариантами**
   ```
   /admin/products/edit/ME-007
   ```

2. **Найдите текущий основной вариант**
   - Ищите зеленый бейдж "Основная цена"

3. **Снимите флаг основной цены**
   - Нажмите "Редактировать" у основного варианта
   - Снимите галочку "Использовать как основную цену"
   - Нажмите "Сохранить"

4. **Установите новую основную цену**
   - Выберите другой вариант
   - Нажмите "Редактировать"
   - Поставьте галочку "Использовать как основную цену"
   - Нажмите "Сохранить"

5. **Результат**
   - Цена покупателя обновится автоматически
   - Бейдж "Основная цена" переместится

## 🎨 Визуальные примеры

### Состояние полей базовой цены

#### Активное состояние (можно редактировать)
```
┌─────────────────────────────────────────────────┐
│ 💰 Цена для покупателя                         │
│ ┌─────────┐ ┌─────────┐ ┌─────────────────────┐ │
│ │   450   │ │   BYN   │ │        м²           │ │
│ └─────────┘ └─────────┘ └─────────────────────┘ │
│ ✅ Поля активны - белый фон                     │
└─────────────────────────────────────────────────┘
```

#### Неактивное состояние (заблокировано)
```
┌─────────────────────────────────────────────────┐
│ 💰 Цена для покупателя                         │
│ ┌─────────┐ ┌─────────┐ ┌─────────────────────┐ │
│ │   150   │ │   BYN   │ │        шт           │ │
│ └─────────┘ └─────────┘ └─────────────────────┘ │
│ 🔒 Поля заблокированы - серый фон               │
│ ℹ️  Цена берется из основного варианта          │
└─────────────────────────────────────────────────┘
```

### Варианты товара

#### Вариант с основной ценой
```
┌─────────────────────────────────────────────────┐
│ 📦 Ткань                    🏷️ Основная цена    │
│ 💰 150 BYN/шт              ✏️ Редактировать     │
│ 🏷️ Материал: Ткань                             │
└─────────────────────────────────────────────────┘
```

#### Обычный вариант
```
┌─────────────────────────────────────────────────┐
│ 📦 Кожа                                         │
│ 💰 300 BYN/шт              ✏️ Редактировать     │
│ 🏷️ Материал: Кожа                              │
└─────────────────────────────────────────────────┘
```

## 🔍 Проверочные списки

### Перед созданием товара

- [ ] Определите, нужны ли варианты
- [ ] Решите, будут ли разные цены у вариантов
- [ ] Выберите основной вариант (если цены разные)
- [ ] Подготовьте список атрибутов

### При создании вариантов

- [ ] Заполните название варианта
- [ ] Укажите цену, валюту и единицу
- [ ] Выберите атрибуты варианта
- [ ] Отметьте основную цену (если нужно)
- [ ] Проверьте состояние базовой цены

### После сохранения

- [ ] Проверьте цену покупателя
- [ ] Убедитесь в корректности вариантов
- [ ] Протестируйте переключение основной цены
- [ ] Проверьте отображение в каталоге

## ⚠️ Частые ошибки

### ❌ Неправильно: Основная цена у всех вариантов
```javascript
// НЕ ДЕЛАЙТЕ ТАК
variants: [
  { isPrimaryPrice: true },  // ❌
  { isPrimaryPrice: true },  // ❌ 
  { isPrimaryPrice: true }   // ❌
]
```

### ✅ Правильно: Основная цена только у одного
```javascript
// ПРАВИЛЬНО
variants: [
  { isPrimaryPrice: true },  // ✅ Основная
  { isPrimaryPrice: false }, // ✅ Обычная
  { isPrimaryPrice: false }  // ✅ Обычная
]
```

### ❌ Неправильно: Пустые цены в вариантах
```javascript
// НЕ ДЕЛАЙТЕ ТАК
{
  price: { value: 0, currency: "", unit: "" } // ❌
}
```

### ✅ Правильно: Полные данные цены
```javascript
// ПРАВИЛЬНО
{
  price: { value: 150, currency: "BYN", unit: "piece" } // ✅
}
```

---

*Примеры актуальны на: 2025-01-14*
