## Компоненты Макет (Layout)

В проекте используются следующие основные компоненты макета:
1. MainLayout.astro - базовый макет, который содержит:
    - Метатеги и базовую структуру HTML
    - Прелоадер
    - Слоты для header, main content и footer
    - Кнопку "Наверх"
    - Базовые скрипты
2. PageLayout.astro - макет для стандартных страниц, который:
    - Наследуется от MainLayout
    - Добавляет хедер и футер
    - Включает хлебные крошки (опционально)
    - Предоставляет слот для основного контента
3. BlogLayout.astro - макет для страниц блога, который:
    - Наследуется от PageLayout
    - Добавляет боковую панель для блога
    - Имеет адаптивную сетку (8/12 для контента, 4/12 для сайдбара)
    - Позволяет включать/отключать сайдбар
Для использования этих макетов в страницах, вы можете делать это так:
---
// Для обычной страницы
import PageLayout from '../layouts/PageLayout.astro';
---

<PageLayout 
  title="About Us"
  description="Learn more about our company"
  breadcrumbs={[
    { text: "About", url: "/about" }
  ]}
>
  <!-- Контент страницы -->
</PageLayout>

---
// Для страницы блога
import BlogLayout from '../layouts/BlogLayout.astro';
---

<BlogLayout 
  title="Our Blog"
  description="Latest news and updates"
  breadcrumbs={[
    { text: "Blog", url: "/blog" }
  ]}
>
  <!-- Контент блога -->
</BlogLayout>