# Компоненты Footer

Этот документ описывает компоненты футера (`Footer` и `FooterLinks`) и их составные части, используемые в проекте aizen-astro.

## Footer Component

### Описание

Компонент `Footer` отображает нижнюю часть сайта (футер), включающую информацию о компании, контактные данные, навигационные ссылки, форму обратной связи и ссылки на политику приватности и условия использования.

### Структура

Футер состоит из двух основных частей:

1.  **Верхняя часть (`Footer.astro`):** Содержит сетку из 4 блоков на десктопе (Company Info, Contact Info, Navigation, Contact Form), расположенных адаптивно. Использует Tailwind Grid для макета.
2.  **Нижняя часть (`FooterLinks.astro`):** Содержит информацию о копирайте слева и ссылки на политику приватности, условия использования и FAQ справа на десктопе. Использует Flexbox или Grid для макета и включает визуальные разделители между ссылками.

### Использование

Компоненты футера (`Footer` и `FooterLinks`) должны быть импортированы и использованы в основном макете (`MainLayout.astro`).

```astro
---
import Footer from '../components/footer/Footer.astro';
import FooterLinks from '../components/footer/FooterLinks.astro';
---

<html lang="en">
  <head>
    <!-- ... existing head elements ... -->
  </head>
  <body>
    <!-- ... existing body content ... -->

    <Footer />
    <FooterLinks />

  </body>
</html>
```

### Дочерние компоненты (используемые в Footer.astro)

-   `CompanyInfo.astro`: Информация о компании (логотип, описание, адрес).
-   `ContactInfo.astro`: Контактные данные (телефон, email).
-   `FooterNav.astro`: Навигационные ссылки футера.
-   `ContactForm.astro`: Форма обратной связи с полями для имени, телефона (с маской +375) и сообщения, а также кнопкой отправки, которая меняет состояние после нажатия.

### Адаптивность

-   **Верхняя часть:** 4 колонки на `lg`, 2 колонки на `md`, 1 колонка на `sm` и ниже.
-   **Нижняя часть:** Элементы располагаются горизонтально на `md` и выше, вертикально на `sm` и ниже.

### Кастомизация

-   **Текст:** Текстовое содержимое каждого блока и ссылки настраиваются внутри соответствующих компонентов (`CompanyInfo.astro`, `ContactInfo.astro`, `FooterNav.astro`, `FooterLinks.astro`).
-   **Стили:** Стили задаются с помощью утилитарных классов Tailwind CSS внутри каждого компонента. Фирменные цвета (`primary`, `dark-footer`, `border-dark`, `text-light`, `text-dark`) используются для соответствия дизайну шаблона.
-   **Разделители ссылок:** Вертикальные разделители между ссылками в `FooterLinks.astro` реализованы с помощью `md:border-r md:border-gray-400` классов.

### Доступность (Accessibility)

-   Убедитесь, что все интерактивные элементы (ссылки) имеют соответствующие `aria-*` атрибуты при необходимости.

## Footer.astro

Основной компонент верхней части футера, который собирает блоки с контентом и обеспечивает адаптивный макет сетки.

**Расположение:** `aizen-astro/src/components/Footer.astro`

**Использование:**

Импортируйте компонент в файл макета (например, `src/layouts/MainLayout.astro`) и добавьте его перед компонентом `FooterLinks`.

```astro
---
import Footer from '../components/Footer.astro';
import FooterLinks from '../components/footer/FooterLinks.astro';
// другие импорты и фронтmatter
---

<html lang="ru">
  <head>
    <!-- ... -->
  </head>
  <body>
    <!-- ... основной контент ... -->
    <Footer />
    <FooterLinks />
  </body>
</html>
```

**Структура:**

`Footer.astro` использует Tailwind CSS Grid для создания адаптивного макета для блоков с контентом. На больших экранах (`lg`) блоки располагаются в 4 колонки, на планшетах (`md`) - в 2 колонки, на мобильных устройствах - в 1 колонку.

## CompanyInfo.astro

Компонент для отображения информации о компании в футере.

**Расположение:** `aizen-astro/src/components/footer/CompanyInfo.astro`

**Назначение:** Отображает логотип (заглушка), название компании, УНП, ИНН и дату регистрации.

## ContactInfo.astro

Компонент для отображения контактной информации и ссылок на социальные сети.

**Расположение:** `aizen-astro/src/components/footer/ContactInfo.astro`

**Назначение:** Включает контактные данные (Email, Телефон, Адрес) и интегрирует компонент `SocialLinks.astro`.

## FooterNav.astro

Компонент для отображения навигационных ссылок в футере.

**Расположение:** `aizen-astro/src/components/footer/FooterNav.astro`

**Назначение:** Отображает список ссылок для навигации по разделам сайта.

## ContactForm.astro

Компонент-заглушка для секции контактной формы в футере.

**Расположение:** `aizen-astro/src/components/footer/ContactForm.astro`

**Назначение:** Временная заглушка для будущей формы обратной связи.

## FooterLinks.astro

Компонент для отображения нижней части футера с информацией об авторских правах и ссылками на политику.

**Расположение:** `aizen-astro/src/components/footer/FooterLinks.astro`

**Назначение:** Отображает текст об авторских правах и ссылки на важные страницы, такие как Политика конфиденциальности и Условия использования.

---

Все компоненты стилизованы с использованием Tailwind CSS для соответствия общему дизайну проекта. 