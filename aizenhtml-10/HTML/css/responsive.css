/* Aizen - Html Template */

@media only screen and (max-width: 1140px){
	.page-wrapper{
		overflow:hidden;
	}

	.main-header .header-upper .logo-outer,
	.main-header .header-upper .upper-right{
		width:100%;
		text-align:center;
	}
	
	.main-header .info-box{
		display:inline-block;
		float:none;
		text-align:left;
	}
	
	.main-header .logo-outer{
		padding:20px 0px 0px;
	}
	
	.main-header .header-upper .upper-right{
		padding-top:20px;
	}
	
	.main-header .info-box{
		margin-bottom:30px;
	}
	
	.fact-section .title-column .title-inner .text{
		font-size:20px;
		padding-top:48px;
	}
	
	.full-width-section .outer-box .title-column .content h3{
		font-size:32px;
	}
	
	.full-width-section .outer-box .services-column .column-inner{
		padding:20px 15px 20px 20px;
	}
	
	.consulting-section .title-column .text,
	.consulting-section .form-column .inner-column{
		margin-right:0px;
	}
	
	.about-section .content-column .inner-column .sec-title h2 br{
		display:none;
	}
	
	.left-sidebar,
	.contact-section .form-column .column-inner,
	.about-section .content-column .inner-column{
		padding-right:0px;
	}
	
	.right-sidebar{
		padding-left:0px;
	}
}


@media only screen and (max-width: 991px) {
	.project-single-section .project-info {
		width: 100%;
		margin-top: 70px;
		position: static;
		-webkit-transform: none;
		-moz-transform: none;
		-o-transform: none;
		-ms-transform: none;
		transform: none;
	}
}

@media only screen and (min-width: 768px){
	.main-menu .navigation > li > ul,
	.main-menu .navigation > li > ul > li > ul{
		display:block !important;
		visibility:hidden;
		opacity:0;
	}
}

@media only screen and (max-width: 1023px){
	.main-header .nav-outer{
		padding-right:0px;	
	}
	
	.main-header .header-upper .upper-right{
		position:relative;
		width:100%;	
	}

	.main-menu .navigation > li a{
		background:none;
	}
	
	.main-slider h2{
		font-size:34px;
	}
	
	.main-slider .text{
		font-size:16px;
	}
	
	.main-slider .dark-heading,
	.main-slider h1{
		font-size:50px;
	}
	
	.slider-content h3{
		font-size:24px;
	}
	
	.fixed-header .sticky-header,
	.main-header .outer-box,
	.clients-section .title-box .text br,
	.consulting-section .title-column .box,
	.fact-section .title-column .title-inner .number:before,
	.fact-section .title-column .title-inner .number:after{
		display:none;
	}
	
	.main-header .header-lower{
		margin-bottom:-1px;
	}
	
	.services-title h3,
	.clients-section .title-box h2{
		font-size:32px;
	}
	
	.fact-section .title-column .title-inner .number{
		border:0px;
		width:auto;
		height:auto;
		line-height:1em;
		margin-bottom:10px;
	}
	
	.fact-section .title-column .title-inner .text{
		padding-left:0px;
		padding-top:0px;
	}
	
	.fact-section .title-column .title-inner .number,
	.fact-section .title-column .title-inner .text{
		position:relative;
		display:block;
		text-align:center;
	}
	
	.project-section .filters .pull-right{
		width:100%;
	}
	
	.project-section .filters.style-two .filter-tabs{
		margin-bottom:30px;
	}
	
	.project-section .filters .filter-tabs{
		margin-top:0px;
		margin-bottom:30px;
	}
	
	.project-section .filters .filter-tabs .filter{
		margin-left:0px;
		margin-right:20px;
	}
	
	.full-width-section .outer-box .services-column,
	.full-width-section .outer-box .title-column{
		position:relative;
		width:100%;
	}
	
	.full-width-section .outer-box .services-column .column-inner{
		padding:50px 15px 20px 30px;
	}
	
	.consulting-section .title-column .text{
		text-align:left;
		margin-top:0px;
	}
	
	.consulting-section .form-column .inner-column,
	.consulting-section .title-column .title-inner{
		margin-left:0px;
	}
	
	.call-to-action .btn-column,
	.call-to-action{
		text-align:center;
	}
	
	.call-to-action .text{
		margin-bottom:20px;
		margin-top:0px;
	}
	
	.consulting-section .title-column{
		margin-bottom:10px;
	}
	
	.main-header .info-box:last-child{
		display:none !important;
	}
	
	.page-title{
		padding:100px 0px;
	}
	
	.error-section .inner-container .home-btn{
		position:relative;
		bottom:0px;
		right:auto;
		left:50%;
		margin-left:-85px;
		margin-top:30px;
	}
	
	.main-header .header-lower .bg-box{
		right:15px;
		left:auto;	
	}
}

@media only screen and (max-width: 767px){
	.main-slider h2{
		font-size:30px;
		padding-bottom:10px;
	}
	
	.main-slider .dark-heading{
		font-size:32px;
	}
	
	.main-slider h1{
		font-size:32px;
	}
	
	.main-slider h4{
		font-size:28px;
	}
	
	.main-slider .text{
		font-size:14px;
	}
	
	.main-header .nav-outer{
		padding-right:0px;
		border-right:0px;
		border-left:0px;
	}
	
	.main-header .btn-outer{
		border: 0 none;
		right: auto;
		text-align: left;
		top: 0;
		width: auto;
		z-index: 14;
	}
	
	.main-header .header-lower .search-box-outer .dropdown-menu{
		right:auto;
		left:0px;	
	}
	
	.main-header .btn-outer .quote-btn{
		margin-top:8px;	
	}
	
	.header-main-box .nav-outer .search-box-btn{
		top:15px;
		right:60px;
		margin-top:0px;
		z-index:20;
	}
	
	.main-header .header-top .top-right, 
	.main-header .header-top .top-left {
		width: 100%;
		text-align:center;
	}
	
	.main-header .header-top .top-right ul,
	.main-header .header-top .top-left ul {
		text-align: center;
	}
	
	.main-header .header-top .top-left ul li{
		padding-bottom:0px;
	}
	
	.main-header .header-top .top-right ul li,
	.main-header .header-top .top-left ul li {
		display: inline-block;
		float: none;
	}
	
	.main-header .header-top .top-right ul li{
		padding-top:5px;
	}
	
	.main-header .header-top .social-icon-four li{
		margin-right:-3px;
	}

	.header-upper .logo-outer{
		display: block;
		width: 100%;
	}
	
	.main-header .header-upper .upper-column{
		margin-left:0px;
		width:100%;	
	}
	
	.main-header .header-upper .upper-column{
		padding-right:0px;	
	}
	
	.main-header .header-upper .upper-column.info-box {
		display: block;
		width:100%;
		text-align:center;
		padding:0px;
		max-width:none;
	}
	
	.main-header .info-box .icon-box{
		position:relative;
		display:block;
		top:0px;
		text-align:center;
		margin:0 auto 12px;	
	}
	
	.main-header .main-menu{
		padding-top:0px;
		width:100%;
		margin:0px;
	}
	
	.main-menu .collapse {
	   max-height:300px;
		overflow:auto;
		float:none;
		width:100%;
		padding:10px 0px 0px;
		border:none;
		margin:0px;
		-ms-border-radius:3px;
		-moz-border-radius:3px;
		-webkit-border-radius:3px;
		-o-border-radius:3px;
		border-radius:3px;
   }
   
	.main-menu .collapse.in,
	.main-menu .collapsing{
		padding:0px 0px 0px;
		border:none;
		margin:0px 0px 0px;
		-ms-border-radius:3px;
		-moz-border-radius:3px;
		-webkit-border-radius:3px;
		-o-border-radius:3px;
		border-radius:3px;	
	}
	
	.main-menu .navbar-header{
		position:relative;
		float:none;
		display:block;
		text-align:right;
		width:100%;
		padding:13px 0px 14px;
		right:0px;
		z-index:12;
	}
	
	.main-menu .navbar-header .navbar-toggle{
		display:inline-block;
		z-index:7;
		border:1px solid #232323;
		float:none;
		margin:0px 15px 0px 0px;
		border-radius:0px;
		background-color:#232323 !important;
	}
	
	.main-menu .navbar-header .navbar-toggle .icon-bar{
		background:#ffffff;	
	}
	
	.main-menu .navbar-collapse > .navigation{
		float:none !important;
		margin:0px !important;
		width:100% !important;
		background:#ffffff;
		border-top:none;
	}
	
	.main-menu .navbar-collapse > .navigation > li{
		margin:0px !important;
		float:none !important;
		width:100%;
	}
	
	.main-menu .navigation > li > a,
	.main-menu .navigation > li > ul:before{
		border:none;	
	}
	
	.main-menu .navbar-collapse > .navigation > li > a{
		padding:10px 10px !important;
		border:none !important;
	}
	
	.main-menu .navigation li.dropdown > a:after,
	.main-menu .navigation > li.dropdown > a:before,
	.main-menu .navigation > li > ul > li > a::before,
	.main-menu .navigation > li > ul > li > ul > li > a::before{
		color:#ffffff !important;
		right:15px;
		font-size:16px;
		display:none !important;
	}
	
	.main-menu .navbar-collapse > .navigation > li > ul,
	.main-menu .navbar-collapse > .navigation > li > ul > li > ul{
		position:relative;
		border:none;
		float:none;
		visibility:visible;
		opacity:1;
		display:none;
		margin:0px;
		left:auto !important;
		right:auto !important;
		top:auto !important;
		padding:0px;
		width:100%;
		background:#232323;
		-webkit-border-radius:0px;
		-ms-border-radius:0px;
		-o-border-radius:0px;
		-moz-border-radius:0px;
		border-radius:0px;
		transition:none !important;
		-webkit-transition:none !important;
		-ms-transition:none !important;
		-o-transition:none !important;
		-moz-transition:none !important;
		box-shadow:none !important;
	}
		
	.main-menu .navbar-collapse > .navigation > li > ul,
	.main-menu .navbar-collapse > .navigation > li > ul > li > ul{
		border-top:1px solid rgba(255,255,255,0.10) !important;	
	}
	
	.main-menu .navbar-collapse > .navigation > li,
	.main-menu .navbar-collapse > .navigation > li > ul > li,
	.main-menu .navbar-collapse > .navigation > li > ul > li > ul > li{
		border-top:1px solid rgba(255,255,255,0.10) !important;
		opacity:1 !important;
		top:0px !important;
		left:0px !important;
		visibility:visible !important;
	}
	
	.main-menu .navbar-collapse > .navigation > li:first-child{
		border:none;	
	}
	
	.main-menu .navbar-collapse > .navigation > li > a,
	.main-menu .navbar-collapse > .navigation > li > ul > li > a,
	.main-menu .navbar-collapse > .navigation > li > ul > li > ul > li > a{
		padding:12px 20px !important;
		line-height:22px;
		color:#fa2964;
		background:#232323;
		text-align:left;
		min-height:0px;
		font-weight:500;
		font-size:14px;
		text-transform:uppercase;
	}
	
	.main-menu .navbar-collapse > .navigation > li > a:hover,
	.main-menu .navbar-collapse > .navigation > li > a:active,
	.main-menu .navbar-collapse > .navigation > li > a:focus{
		background:#232323;
	}
	
	.main-menu .navbar-collapse > .navigation > li:hover > a,
	.main-menu .navbar-collapse > .navigation > li > ul > li:hover > a,
	.main-menu .navbar-collapse > .navigation > li > ul > li > ul > li:hover > a,
	.main-menu .navbar-collapse > .navigation > li.current > a,
	.main-menu .navbar-collapse > .navigation > li.current-menu-item > a{
		background:#232323;
		color:#ffffff;
	}
	
	.main-menu .navigation > li.home .fa{
		color:#ffffff;
	}
	
	.main-header .outer-box,
	.main-menu .navbar-collapse > .navigation li.dropdown .dropdown-btn{
		display:block;
	}
	
	.main-menu .navbar-collapse > .navigation li.dropdown:after,
	.main-menu .navigation > li > ul:before,
	.main-header .info-box:after,
	.business-section .title-box .text br,
	.main-header .header-lower .search-box-outer,
	.counter-section.style-two:before{
		display:none !important;	
	}
	
	.main-slider{
		margin-top:0px !important;	
	}
	
	.main-header .top-right .links-nav li{
		margin-left:5px;
	}
	
	.main-header .search-box-outer .dropdown-menu{
		top:48px;
		right:0px;
	}
	
	.main-header .outer-box{
		display:block;
		right:auto;
		z-index:12;
		left:0px;
	}
	
	.main-header .search-form .form-group input[type="text"],
	.main-header .search-form .form-group input[type="tel"],
	.main-header .search-form .form-group input[type="email"],
	.main-header .search-form .form-group textarea{
		height:60px;
	}
	
	.main-header .search-form .form-group input[type="submit"],
	.main-header .search-form button{
		line-height:60px;	
	}
	
	.main-header .btn-box .donate-btn{
		padding:12px 35px 12px 38px;
	}
	
	.main-header .btn-box{
		display:block;
		margin-top:7px;
	}
	
	.main-header .outer-box,
	.main-header .btn-box{
		display:block;
	}
	
	.main-header .header-top .donation-btn{
		float:none;
	}
	
	.header-top .top-left,
	.header-top .top-right{
		width:100%;
		text-align:center;
	}
	
	.header-top .top-left ul li{
		padding-bottom:0px;
	}
	
	.main-header .header-upper{
		padding-bottom:0px;
	}
	
	.services-title h3{
		font-size:26px;
	}
	
	.main-header .outer-box .consult-btn{
		padding:19px 25px;
	}
	
	.services-block-two .inner-box,
	.main-footer .footer-bottom .copyright,
	.main-footer .footer-bottom .foter-nav{
		text-align:center;
	}
	
	.services-block-three .inner,
	.list-style-two li{
		padding-left:0px;
		text-align:center;
	}
	
	.services-block-three .inner .icon-bar{
		position:relative;
		margin-bottom:20px;
	}
	
	.sec-title h2{
		font-size:28px;
	}
	
	.main-menu .navigation > li > ul{
		padding:0px 0px 0px;
	}
	
	.page-title h1{
		font-size:40px;
	}
	
	.services-single .inner-box .lower-content h2{
		font-size:26px;
	}
	
	.services-block-five .inner-box{
		padding:0px 15px 30px;
		text-align:center;
	}
	
	.services-block-five .inner-box .icon-box{
		margin:0 auto;
	}
	
	.sidebar-page-container .comments-area .comment{
		padding-left:0px;
	}
	
	.list-style-two li .icon,
	.sidebar-page-container .comments-area .comment-box .author-thumb{
		position:relative;
	}

	.about-details {
		-webkit-box-shadow: 0 0 0 0;
		-moz-box-shadow: 0 0 0 0;
		box-shadow: 0 0 0 0;
		border: 0;
		padding: 0 20px 40px 20px;
		max-width: 100%;
		position: relative;
		top: 0;
		transform: none;
	}

	.about-details-image {

	}
}

@media only screen and (max-width: 599px){
	.main-header .social-links-one{
		top:0px;
	}

	.main-header .header-top .top-left,
	.main-header .header-top .top-right{
		float:none !important;
		text-align:center;
	}
	
	.main-header .header-upper .upper-right{
		
	}
	
	.main-header .search-form .form-group input[type="text"],
	.main-header .search-form .form-group input[type="tel"],
	.main-header .search-form .form-group input[type="email"],
	.main-header .search-form .form-group textarea{
		width:230px;
	}
	
	.main-slider h2{
		font-size:24px;
		padding-bottom:10px;
	}
	
	.main-slider .text{
		font-size:13px;
	}
	
	.main-slider .text br{
		display:none !important;
	}
	
	.clients-section .title-box h2{
		font-size:24px;
	}
	
	.services-block-two .inner-box{
		padding:45px 15px;
	}
	
	.header-top .top-right{
		padding-top:10px;
	}
	
	.header-top .top-left{
		display:none;
	}
}

@media only screen and (max-width: 479px) {
	.main-header .header-upper .upper-column.info-box{
		margin-left: auto;
		margin-right: auto;
		float: none;
	}
	
	.main-slider h2:before{
		display:none;
	}
	
	.main-slider h1{
		font-size:19px;
	}
	
	.main-slider h2{
		font-size:19px;
		border:none;
		padding-bottom:10px;
	}
	
	.main-slider .text{
		font-size:12px;
	}
	
	.slider-content h3,
	.main-slider h4{
		font-size:16px;
	}	
}