Система настроек для раздела "Настройки" в админ-панели проекта, интегрированной с Once UI и AstroJS. Система позволит управлять дизайном, SEO, контентом и функционалом сайта без написания кода.

**ВАЖНО**
Система настроек должна быть разработана с условием возможности переиспользования, независимой разработки от текущего проекта и возможности последующей интеграции в пустой AstraJS проект, для его настройки. Учитывай это в организации структуры файлов данной системы.

# Обзор системы
- **Цель**: Создание интуитивной админ-панели для управления сайтом.

## 📦 Технический стек
- **Фреймворк**: Astro (v5.0+)
- **UI-библиотека**: Once UI (@once-ui/react)
- **Хранение настроек**: JSON-файлы + localStorage
- **State-менеджмент**: Zustand (легковесное решение)
- **Хостинг**: Vercel, Netlify, GitHub Pages и т.п. (бесплатный тариф)

## Модули системы настроек
Для каждого модуля будет отдельный JSON-файл хранения настроек
Основной файл настроек (system-settings.json) - Подключение модулей 

Примерная структура для системы настроек (если чего-то не хватает/не корректно - дополнить/исправить):
src/settings/
   ├── components/
   │ ├── admin/
   ├── layouts/
   ├── pages/
   │ └── admin/
   │ └── settings.astro
   ├── utils/
   │ ├── settingsLoader.js
   │ ├── settingsSaver.js
   │ └── mediaUtils.js
   ├──public/
   ├── data/
   │ ├── site.json
   │ ├── design.json
   │ ├── page.json
   │ └── ...
   └── media/

### I. Общие настройки сайта (site.json)
1. **Название сайта**: 
   - Текстовое поле для ввода названия сайта (используется в шапке и title)
2. **Описание сайта**:
   - Краткое описание (может использоваться в мета-теге description по умолчанию)
3. **Язык и регион**:
   - Выбор языка сайта (если мультиязычность)
   - Формат даты, валюты и т.д.
4. **Контактная информация**:
   - Название компании
   - Юридическая информация
   - Адрес компании (юридический/фактический)
   - Телефон
   - Email
   - Рабочие часы
5. **Социальные сети**:
   - Ссылки на профили в соцсетях (Facebook, Instagram, Twitter и т.д.)
6. **Системные настройки**:
   - Язык и регион:
   - Часовой пояс
   - Формат даты/времени

### II. Брендинг & Дизайн (design.json)
1. **Логотип**: 
   - Загрузка логотипа (с предпросмотром и возможностью удаления) (PNG, SVG, JPG)
   - Настройка размера (опционально, можно автоматически или вручную - (в пикселях или %))
2. **Фавикон (favicon)**:
   - Загрузка фавикона (ICO, PNG)
3. **Цветовая схема/Фоны**:
   - Выбор основной цветовой палитры (например, основной цвет, вторичный, акцентный) (HEX/RGB)
   - Возможность задать цвета для различных элементов (кнопок, ссылок, заголовков и т.д.)
   - Цвет текста (основной, заголовки, ссылки)
   - Фоны (для шапки, подвала, фон сайта (HEX/RGB)) - Загрузка фонового изображения (PNG, JPG, SVG)
   - Настройка позиционирования и повторения изображения
4. **Шрифты**:
   - Выбор шрифта для заголовков и основного текста (из безопасного списка - системные или Google Fonts)
   - Настройка размера шрифта для разных элементов (body, заголовки H1-H6)
   - Высота строки, межбуквенный интервал 
5. **Компоненты/Кнопки/Формы**:
   - Тени
   - Толщина обводки
   - Цвет обводки
   - Цвет обводки при фокусе
   - Эффект при наведении
   - Закругление углов (в пикселях)
   - Настройка карточек (Тень, Закругления, Внутренние отступы)
   - Ссылки (Стиль, Стиль ссылок при наведении)
   - Скорость анимаций (мс)

### III. Настройка страниц (page.json)
Для каждой страницы (главная, каталог товаров, о компании, контакты) должны быть настройки:
1. **Главная страница**:
   - Выбор шаблона (если есть несколько вариантов)
   - Управление блоками (слайдер, приветственный текст, популярные товары, категории и т.д.) - возможность включать/выключать и настраивать контент внутри блоков (WYSIWYG-редактор (CKEditor/TinyMCE))
   - Медиа-библиотека:
      Загрузка/удаление изображений, PDF, видео
      Категоризация медиафайлов
   - SEO настройки для главной (title, description, keywords)
2. **Страница "О компании"**:
   - Выбор шаблона (если есть несколько вариантов)
   - Редактор контента (WYSIWYG)
   - SEO настройки
3. **Страница "Контакты"**:
   - Выбор шаблона (если есть несколько вариантов)
   - Редактор контента (можно добавить карту, форму обратной связи и т.д.)
   - Настройка формы обратной связи (если есть)
   - SEO настройки
4. **Дополнительные страницы**:
   - Создание/редактирование/удаление страниц (например, "Доставка", "Оплата", "Блог" и т.д.)
   - Для каждой страницы: URL, контент (WYSIWYG), SEO настройки, видимость (в меню или нет)
5. **Шапка (Header) & Подвал (Footer)**:
   a) Header
   - Выбор шаблона (если есть несколько вариантов)
   - Тип меню (горизонтальное, вертикальное, гамбургер)
   - Показ/скрытие корзины, поиска, телефона
   - Закрепление шапки при скролле
   b) Footer
   - Выбор шаблона (если есть несколько вариантов)
   - Текст копирайта
   - Блоки контента (колонки с текстом/ссылками). Кол-во колонок и стиль
   - Социальные иконки (ссылки на соцсети). Кол-во элементов

### IV. Формы (forms.json)
1. **Форма обратной связи**:
   - Выбор шаблона (если есть несколько вариантов)
   - Настройка полей (имя, email, телефон, сообщение и т.д.)
   - Управление обязательными полями
   - Email/Telegram Bot/Google Sheets и т.п., на который отправляются(дублируются) заявки
   - Сообщение об успешной отправке для пользователя
2. **Подписка на рассылку**:
   - Интеграция с сервисами (Mailchimp, SendPulse)
   - Текст призыва к подписке
3. **Виджеты**:
   - Онлайн-чат (Tawk.to, JivoSite)
   - Кнопка "Позвонить"

### V. SEO (seo.json)
1. **Глобальные настройки SEO**:
   - Title по умолчанию (шаблон)
   - Description по умолчанию (шаблон)
   - Keywords по умолчанию
   - Включение/отключение индексации (robots.txt, noindex для определенных страниц)
   - Настройка канонических URL
   - Генерация и управление sitemap.xml
2. **Open Graph и социальные сети**:
   - Загрузка изображения по умолчанию для соц. сетей
   - Настройка формата вывода при расшаривании
3. **Интеграции**:
   - Google Analytics ID
   - Яндекс.Метрика
   - Код верификации Google Search Console

### VI. Интеграции (integrations.json)
1. **Google Analytics**:
   - Поле для ввода ID Google Analytics
2. **Яндекс.Метрика**:
   - Поле для ввода кода
3. **Другие сервисы**:
   - Коды перед закрывающим head (например, верификация сайта)
   - Коды перед закрывающим body (например, чаты)
   ***Платежные системы***:
   - Stripe, PayPal, LiqPay (ввод API-ключей)
   ***Службы доставки***:
   - Новая Почта, Justin (API-настройки)
   ***1С/ERP-синхронизация***:
   - Настройка обмена товарами/заказами

### VII. Настройка каталога (если предусмотрено) (catalog.json)
1. **Отображение товаров**:
   - Вид сетки (количество колонок)
   - Элементы карточки товара (рейтинг, кнопка "В корзину", лейблы)
   - Сортировка по умолчанию (цена, новизна, популярность)
   - Настройка отображения товаров (количество на странице, вид сетки/список, сортировка по умолчанию)
   - SEO настройки
2. **Страница товара**:
   - Выбор шаблона (если есть несколько вариантов)
   - Шаблон галереи для товара (слайдер, сетка)
   - Включение блока "Похожие товары"
   - Отображение атрибутов (размеры, цвет)
   - Отображение вариантов (атрибут + значение = стоимость)
   - Настройка отображения информации о товаре (описание короткое, полное)
   - SEO настройки (шаблон для title и description, например, с подстановкой названия товара)

### VIII. Настройки магазина (если есть интернет-магазин) (shop.json)
1. **Валюта**:
   - Выбор валюты магазина
2. **Способы оплаты**:
   - Включение/отключение методов оплаты (карты, наличные, онлайн-платежи)
   - Настройки для каждого метода (например, реквизиты для банковского перевода)
3. **Доставка**:
   - Методы доставки (самовывоз, курьер, почта)
   - Зоны доставки и стоимость
4. **Налоги**:
   - Настройка налоговых ставок
5. **Корзина & Оформление**:
   - Выбор шаблона (если есть несколько вариантов)
   - Минимальная сумма заказа

### IX. Безопасность (security.json)
1. **Настройки доступа**:
   - Регистрация пользователей (включить/выключить)
   - Политика паролей
2. **Резервное копирование**:
   - Возможность создания резервной копии настроек и данных
3. **Режим обслуживания** (заглушка)
4. **CAPTCHA для форм**

### X. Производительность (performance.json)
1. **Кэширование**:
   - Включение/выключение кэша
   - Очистка кэша
2. **Минификация**:
   - CSS и JS

### XI. Дополнительно (additional.json)
1. **Пользовательский CSS/JS**:
   - Поля для ввода пользовательского кода CSS и JS, который будет применен на сайте
   - Глобальные стили (Custom CSS)
2. **Режим разработки/технического обслуживания**:
   - Включение режима "На сайте ведутся работы" с возможностью установки страницы-заглушки
3. **Виджеты аналитики**:
   - Facebook Pixel
   - Google Tag Manager
