#!/usr/bin/env node

/**
 * Скрипт для валидации и исправления данных товаров
 * Проверяет на наличие некорректных значений типа "undefined", null, пустых строк
 */

import fs from 'fs';
import path from 'path';

const PRODUCTS_FILE = 'data/product/products.json';

function validateAndFixProducts() {
  console.log('🔍 Начинаем валидацию данных товаров...');
  
  // Читаем файл с товарами
  let products;
  try {
    const data = fs.readFileSync(PRODUCTS_FILE, 'utf8');
    products = JSON.parse(data);
  } catch (error) {
    console.error('❌ Ошибка чтения файла products.json:', error.message);
    return;
  }

  let issuesFound = 0;
  let issuesFixed = 0;

  // Функция для проверки и исправления значения
  function validateValue(value, path) {
    if (value === 'undefined' || value === 'null') {
      console.log(`⚠️  Найдено некорректное значение "${value}" в ${path}`);
      issuesFound++;
      return null; // Заменяем на null
    }
    
    if (typeof value === 'string' && value.trim() === '') {
      console.log(`⚠️  Найдена пустая строка в ${path}`);
      issuesFound++;
      return null; // Заменяем на null
    }
    
    return value;
  }

  // Функция для рекурсивной проверки объекта
  function validateObject(obj, basePath = '') {
    if (Array.isArray(obj)) {
      return obj.map((item, index) => 
        validateObject(item, `${basePath}[${index}]`)
      ).filter(item => item !== null);
    }
    
    if (obj && typeof obj === 'object') {
      const result = {};
      for (const [key, value] of Object.entries(obj)) {
        const currentPath = basePath ? `${basePath}.${key}` : key;
        const validatedValue = validateObject(value, currentPath);
        
        if (validatedValue !== null) {
          result[key] = validatedValue;
        }
      }
      return result;
    }
    
    return validateValue(obj, basePath);
  }

  // Проверяем каждый товар
  products.forEach((product, index) => {
    console.log(`\n📦 Проверяем товар ${index + 1}: ${product.name || 'Без названия'}`);
    
    const originalIssues = issuesFound;
    
    // Проверяем основные атрибуты товара
    products[index] = validateObject(product, `products[${index}]`);
    
    const newIssues = issuesFound - originalIssues;
    if (newIssues > 0) {
      console.log(`   ✅ Исправлено ${newIssues} проблем в товаре`);
      issuesFixed += newIssues;
    } else {
      console.log(`   ✅ Товар в порядке`);
    }
  });

  // Сохраняем исправленные данные
  if (issuesFixed > 0) {
    try {
      // Создаем резервную копию
      const backupFile = `${PRODUCTS_FILE}.backup.${Date.now()}`;
      fs.copyFileSync(PRODUCTS_FILE, backupFile);
      console.log(`\n💾 Создана резервная копия: ${backupFile}`);
      
      // Сохраняем исправленные данные
      fs.writeFileSync(PRODUCTS_FILE, JSON.stringify(products, null, 2));
      console.log(`✅ Сохранены исправленные данные в ${PRODUCTS_FILE}`);
    } catch (error) {
      console.error('❌ Ошибка сохранения файла:', error.message);
      return;
    }
  }

  // Выводим итоги
  console.log('\n📊 ИТОГИ ВАЛИДАЦИИ:');
  console.log(`   Всего товаров проверено: ${products.length}`);
  console.log(`   Найдено проблем: ${issuesFound}`);
  console.log(`   Исправлено проблем: ${issuesFixed}`);
  
  if (issuesFound === 0) {
    console.log('🎉 Все данные в порядке!');
  } else if (issuesFixed === issuesFound) {
    console.log('🎉 Все проблемы успешно исправлены!');
  } else {
    console.log('⚠️  Некоторые проблемы требуют ручного исправления');
  }
}

// Запускаем валидацию
validateAndFixProducts();
